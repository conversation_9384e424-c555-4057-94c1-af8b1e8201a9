import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
} from "react-native"
import Colors, { LIGHT_GREY } from "@/constants/Colors"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"
import _ from "lodash"
import { Section } from "./constants"
import { LinearGradient } from "expo-linear-gradient"
import Badge from "../profile/Badge"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { useFeatureFlag } from "@/utils/featureFlags"
import { Feature, useTestingContext } from "@/context/TestingContext"

interface NewsNavBarProps {
  sections: Section[]
  selectedSectionIndex: number
  points?: number
  flatlistRef: React.RefObject<FlatList<any>>
  onSectionChange: (index: number) => void
}

export const NewsNavBar = ({
  sections,
  selectedSectionIndex,
  points,
  flatlistRef,
  onSectionChange,
}: NewsNavBarProps) => {
  const { featureIsOn } = useTestingContext()

  const gistsFlag = featureIsOn(Feature.Gists)
  const showBadge = useFeatureFlag("levels") && !_.isNil(points) && !gistsFlag
  const { top } = useSafeAreaInsets()

  const renderItem = ({ item, index }: { item: Section; index: number }) => {
    return (
      <TouchableOpacity
        style={styles.sectionTitle}
        onPress={() => onSectionChange(index)}
      >
        <Text
          style={[
            styles.text,
            {
              color:
                selectedSectionIndex == index ? Colors.light.text : "#B3B3B3",
            },
          ]}
        >
          {item.title}
        </Text>
      </TouchableOpacity>
    )
  }

  return (
    <View
      style={[
        styles.navbarContainer,
        { paddingTop: gistsFlag ? 0 : top + topPadding },
      ]}
    >
      <View style={styles.scrollerContainer}>
        <FlatList
          data={sections}
          ref={flatlistRef}
          keyExtractor={(item) => item.name}
          horizontal
          showsHorizontalScrollIndicator={false}
          renderItem={renderItem}
          extraData={selectedSectionIndex}
          ListFooterComponent={<View style={{ width: 20 }} />}
        />
        {showBadge ? (
          <TouchableOpacity
            style={styles.levelsBadgeContainer}
            activeOpacity={1}
          >
            <Badge variant="points" points={points} />
          </TouchableOpacity>
        ) : null}
        {!showBadge ? (
          <LinearGradient
            colors={["rgba(255,255,255,255)", "rgba(255,255,255,0)"]}
            style={styles.rightGradient}
            start={{ x: 1, y: 0.5 }}
            end={{ x: 0, y: 0.5 }}
          />
        ) : null}
      </View>
    </View>
  )
}

const borderTopWidth = 1
const topPadding = 15
const paddingHorizontal = hp("0.2%")
const sectionTitleHeight = 45

export const NAVBAR_HEIGHT = borderTopWidth + sectionTitleHeight
export const NAVBAR_HEIGHT_WITH_PADDING = topPadding + NAVBAR_HEIGHT

const styles = StyleSheet.create({
  navbarContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal,
    width: wp("100%"),
    backgroundColor: "white",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.42,
    shadowRadius: 6,
    elevation: 3,
  },
  scrollerContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderTopWidth,
    borderColor: LIGHT_GREY,
  },
  sectionTitle: {
    height: sectionTitleHeight,
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    fontFamily: "InterTight-SemiBold",
    padding: hp("1%"),
    fontSize: hp("1.8%"),
  },
  rightGradient: {
    pointerEvents: "none",
    position: "absolute",
    top: 0,
    right: 0,
    width: 100,
    height: sectionTitleHeight,
    zIndex: 10,
  },
  levelsBadgeContainer: {
    marginRight: 20,
  },
})
