import { get, put } from "@/network"
import { INPRESS_API_URL } from "./constants"

export type UserSettings = {
  autoUpdateLocation: boolean
  matchMoodsOptOut: boolean
  ratingSoundsAndHaptics: boolean
  nonessentialNotificationsDisabled: boolean
}

export type RawUserSettings = {
  auto_update_location: UserSettings["autoUpdateLocation"]
  match_moods_opt_out: UserSettings["matchMoodsOptOut"]
  rating_sounds_and_haptics: UserSettings["ratingSoundsAndHaptics"]
  nonessential_notifications_disabled: UserSettings["nonessentialNotificationsDisabled"]
}

export const convertRawUserSettings = (raw: RawUserSettings): UserSettings => ({
  autoUpdateLocation: raw.auto_update_location,
  matchMoodsOptOut: raw.match_moods_opt_out,
  ratingSoundsAndHaptics: raw.rating_sounds_and_haptics,
  nonessentialNotificationsDisabled: raw.nonessential_notifications_disabled,
})

const convertUserSettings = (
  settings: Partial<UserSettings>,
): Partial<RawUserSettings> => ({
  auto_update_location: settings.autoUpdateLocation,
  match_moods_opt_out: settings.matchMoodsOptOut,
  rating_sounds_and_haptics: settings.ratingSoundsAndHaptics,
  nonessential_notifications_disabled:
    settings.nonessentialNotificationsDisabled,
})

type GetSettingsResponse = {
  settings: RawUserSettings
}

export const getUserSettings = async ({
  token,
}: {
  token: string
}): Promise<UserSettings> => {
  const { settings } = await get<null, GetSettingsResponse>(
    `${INPRESS_API_URL}/user/settings`,
    token,
  )

  return convertRawUserSettings(settings)
}

type UpdateSettingsProps = {
  token: string
} & Partial<UserSettings>

export const updateUserSettings = async ({
  token,
  ...newSettings
}: UpdateSettingsProps): Promise<UserSettings> => {
  type Params = Partial<RawUserSettings>
  const { settings } = await put<Params, GetSettingsResponse>(
    `${INPRESS_API_URL}/user/settings`,
    convertUserSettings(newSettings),
    token,
  )
  return convertRawUserSettings(settings)
}
