import { ScrollView, StyleSheet, View } from "react-native"
import { newsfeedMarginHorizontal, SPOTLIGHT_IMAGE_HEIGHT } from "./constants"
import {
  FullImageArticleCard,
  HEIGHT as FULL_IMAGE_HEIGHT,
} from "./FullImageArticleCard"
import { Article } from "@/types/news"

export const FullImageArticleLayout = ({
  articles,
}: {
  articles: Article[]
}) => {
  return (
    <ScrollView
      contentContainerStyle={{
        paddingHorizontal: newsfeedMarginHorizontal,
        height: HEIGHT,
      }}
      horizontal
      showsHorizontalScrollIndicator={false}
      snapToInterval={WIDTH + MARGIN_RIGHT}
      snapToAlignment="start"
      decelerationRate="fast"
    >
      {articles.map((article) => (
        <View style={styles.articleContainer} key={article.id}>
          <FullImageArticleCard article={article} />
        </View>
      ))}
    </ScrollView>
  )
}

const FOOTER_HEIGHT = 20
export const HEIGHT = FULL_IMAGE_HEIGHT + FOOTER_HEIGHT

export const WIDTH = 315
export const MARGIN_RIGHT = 20

const styles = StyleSheet.create({
  container: {
    marginLeft: newsfeedMarginHorizontal,
  },
  articleContainer: {
    width: WIDTH,
    marginRight: MARGIN_RIGHT,
  },
})
