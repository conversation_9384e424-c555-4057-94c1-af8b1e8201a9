import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_PROPS } from "./news"
import { useNewsContext } from "@/context/NewsContext"
import { useEffect } from "react"

export default function Story() {
  const { setArticles } = useNewsContext()

  useEffect(() => {
    setArticles([])
  }, [setArticles])

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} />
    </Screen>
  )
}
