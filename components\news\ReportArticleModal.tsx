import { useState } from "react"
import { View, Text, TouchableOpacity, Modal } from "react-native"
import { TextInput } from "../TextInput"
import { modalStyles } from "@/styles"
import { useSession } from "@/ctx"
import { reportArticle } from "@/apiQueries/apiQueries"
import { Session } from "@/types/user"

export const submitReport = async ({
  articleId,
  reason,
  note,
  session,
}: {
  articleId: number
  reason: string
  note: string
  session: Session
}) => {
  if (!session) return

  try {
    return await reportArticle({
      token: session.token,
      reason,
      note,
      articleId,
    })
  } catch (error) {
    console.error(error)
    throw error
  }
}

const ReportArticleModal = ({
  articleId,
  modalVisible,
  onClose,
}: {
  articleId: number
  modalVisible: boolean
  onClose: () => void
}) => {
  const { session } = useSession()
  const [reason, setReason] = useState<string>("Other")
  const [note, setNote] = useState("")

  const reasonOptions = ["Too many ads", "Paywalled", "Other"]

  return (
    <Modal visible={modalVisible} transparent={true} animationType="slide">
      <View style={modalStyles.modalContainer}>
        <View style={modalStyles.modalContent}>
          <Text style={modalStyles.header}>Report this article</Text>
          <Text style={modalStyles.subHeader}>
            Why are you reporting this article?
          </Text>
          <View style={[modalStyles.reasonsContainer, { flexWrap: "wrap" }]}>
            {reasonOptions.map((option) => {
              const selected = option === reason
              return (
                <TouchableOpacity
                  key={option}
                  style={[
                    modalStyles.reasonButton,
                    selected && modalStyles.selectedReason,
                  ]}
                  onPress={() => setReason(option)}
                >
                  <Text
                    style={[
                      modalStyles.reasonText,
                      selected && modalStyles.selectedReasonText,
                    ]}
                  >
                    {option}
                  </Text>
                </TouchableOpacity>
              )
            })}
          </View>
          <Text style={modalStyles.reasonLabel}>Reason</Text>
          <Text style={modalStyles.helperText}>
            Help us understand the problem.
          </Text>
          <TextInput
            inputStyle={modalStyles.textInput}
            placeholder="Write additional comments here..."
            value={note}
            onChangeText={setNote}
            multiline
          />
          <TouchableOpacity
            style={modalStyles.button}
            onPress={() =>
              submitReport({
                articleId,
                reason,
                note,
                session: session!,
              })
            }
          >
            <Text style={modalStyles.buttonText}>Submit report</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[modalStyles.button, modalStyles.cancelButton]}
            onPress={onClose}
          >
            <Text style={modalStyles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
}

export default ReportArticleModal
