import { AgePreferenceSelector } from "@/components/signInOrUp/AgePreferenceSelector"
import { SettingsSection } from "./SettingsSection"

interface AgePreferenceSectionProps {
  initialMinAge: number
  initialMaxAge: number
  onChange: (values: number[]) => void
}

export const AgePreferenceSection = ({
  initialMinAge,
  initialMaxAge,
  onChange,
}: AgePreferenceSectionProps) => {
  return (
    <SettingsSection
      title="Age"
      subtitle="Choose the age range for who you want to match with."
    >
      <AgePreferenceSelector
        minAgePref={initialMinAge}
        maxAgePref={initialMaxAge}
        onChange={onChange}
      />
    </SettingsSection>
  )
}
