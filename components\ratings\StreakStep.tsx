import { useLevels } from "@/context/LevelContext"
import { EventType } from "@/utils/tracking"
import { CelebrateStep } from "./CelebrateStep"
import { shareFile } from "@/utils/sharing"

type StreakStepProps = {
  streakType: string
}

export const StreakStep = ({ streakType }: StreakStepProps) => {
  const { loading, streaks } = useLevels()

  if (loading || !streaks) return null

  const streak = streaks.find((streak) => streak.type === streakType)!

  const handleShare = async () => {
    await shareFile({
      url: streak.shareableUrl,
      fallbackMessage: `I just hit a ${streak.title} on InPress! www.inpress.app`,
      eventType: EventType.StreakSharePressed,
      trackingData: { streakType: streak.type },
    })
  }

  return (
    <CelebrateStep
      title={streak.title}
      subtitle={streak.subtitle}
      handleShare={handleShare}
    />
  )
}
