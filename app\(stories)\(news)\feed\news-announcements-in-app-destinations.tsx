import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_PROPS } from "./news"
import { Announcement } from "@/apiQueries/newsFeed"
import { GENERIC_IMAGE_URL } from "./news-announcements"

export default function NewsFeedStory() {
  const announcements: Announcement[] = [
    {
      id: 1,
      title:
        "Check out our new Match With Friends feature! Find out what you have in common with your friends!",
      imageUrl: GENERIC_IMAGE_URL,
      inAppDestination: "matchWithFriends",
    },
  ]

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} announcements={announcements} />
    </Screen>
  )
}
