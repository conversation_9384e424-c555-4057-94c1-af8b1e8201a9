import { EventType, trackEvent } from "./tracking"
import * as Sharing from "expo-sharing"
import * as Sentry from "@sentry/react-native"
import { Share } from "react-native"
import { Level } from "@/types/levels"

type ShareFileProps = {
  url: string
  fallbackMessage: string
  eventType: EventType
  trackingData: any
}

export const shareFile = async ({
  url,
  fallbackMessage,
  eventType,
  trackingData,
}: ShareFileProps) => {
  trackEvent(eventType, { data: trackingData })
  if (await Sharing.isAvailableAsync()) {
    await Sharing.shareAsync(url)
  } else {
    const errorMessage = "File sharing not available"
    Sentry.captureException(new Error(errorMessage), {
      fingerprint: [errorMessage],
    })
    Share.share({
      message: fallbackMessage,
    })
  }
}

export const shareLevelUp = async (level: Level) => {
  await shareFile({
    url: level.shareableUrl,
    fallbackMessage: `I just leveled up to ${level.name} on InPress! www.inpress.app`,
    eventType: EventType.LevelUpSharePressed,
    trackingData: { levelId: level.id, levelName: level.name },
  })
}
