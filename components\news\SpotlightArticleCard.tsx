import { useState } from "react"
import { StyleSheet, View } from "react-native"
import { Card, Title } from "react-native-paper"
import ArticleSource from "./ArticleSource"
import Footer, { FOOTER_HEIGHT } from "./Footer"
import { MATCH_MOODS_HEIGHT, MatchMoodsWidget } from "./MatchMoodsWidget"
import { ArticleMatchPreview } from "@/apiQueries/newsFeed"
import { Image } from "expo-image"
import { useFeatureFlag } from "@/utils/featureFlags"
import { ReadRatedCover } from "./ReadRatedCover"
import ReportArticleModal from "./ReportArticleModal"
import { Article } from "@/types/news"

interface SpotlightArticleCardProps {
  article: Article
  imageHeight: number
  matchPreview?: ArticleMatchPreview
  hideMatchMoods: boolean
  onPress: (article: Article) => void
}

export const SpotlightArticleCard = ({
  article,
  imageHeight,
  matchPreview,
  hideMatchMoods,
  onPress,
}: SpotlightArticleCardProps) => {
  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")

  const [isReporting, setIsReporting] = useState(false)

  return (
    <Card
      style={[
        styles.headlineContainer,
        {
          height:
            SPOTLIGHT_ARTICLE_HEIGHT_FN(newReadRatedFlag) +
            (hideMatchMoods ? 0 : MATCH_MOODS_HEIGHT),
        },
      ]}
      elevation={0}
      onPress={() => onPress(article)}
      onLongPress={() => setIsReporting(true)}
    >
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: article.imageUrl }}
          style={{ height: imageHeight, borderRadius: 10 }}
        />
        {newReadRatedFlag && <ReadRatedCover article={article} />}
      </View>
      <Card.Content>
        <View>
          <ArticleSource article={article} />
          <Title style={styles.headlineText} numberOfLines={3}>
            {article.title}
          </Title>
          {hideMatchMoods ? null : (
            <MatchMoodsWidget matchPreview={matchPreview} />
          )}

          <View style={[styles.divider, { opacity: hideMatchMoods ? 0 : 1 }]} />

          {!newReadRatedFlag && (
            <Footer
              isRead={article.isOpened}
              isRated={article.isSurveyed}
              articleId={article.id}
            />
          )}
        </View>
        <ReportArticleModal
          articleId={article.id}
          modalVisible={isReporting}
          onClose={() => setIsReporting(false)}
        />
      </Card.Content>
    </Card>
  )
}

const HEADLINE_MARGIN_BOTTOM = 6
const HEADLINE_TEXT_HEIGHT = 108
const DIVIDER_MARGIN_BOTTOM = 12
const DIVIDER_BORDER_WIDTH = 1
const DIVIDER_HEIGHT = DIVIDER_MARGIN_BOTTOM + DIVIDER_BORDER_WIDTH

export const SPOTLIGHT_ARTICLE_HEIGHT_FN = (newReadRatedFlag: boolean) => {
  return (
    HEADLINE_MARGIN_BOTTOM +
    HEADLINE_TEXT_HEIGHT +
    FOOTER_HEIGHT +
    DIVIDER_HEIGHT +
    (newReadRatedFlag ? 180 : 210)
  )
}

const styles = StyleSheet.create({
  headlineContainer: {
    marginBottom: HEADLINE_MARGIN_BOTTOM,
    backgroundColor: "transparent",
    shadowColor: "transparent",
  },
  imageContainer: {
    marginBottom: 24,
  },
  headlineText: {
    fontSize: 28,
    fontFamily: "PPEditorialOld-Italic",
    letterSpacing: 0.16,
    lineHeight: 32,
    color: "black",
    marginBottom: HEADLINE_MARGIN_BOTTOM,
  },
  divider: {
    borderBottomColor: "#CBCBCB",
    marginBottom: DIVIDER_MARGIN_BOTTOM,
    borderBottomWidth: 1,
  },
})
