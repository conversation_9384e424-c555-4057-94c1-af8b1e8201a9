{"expo": {"name": "InPress", "slug": "inpress-expo-router", "version": "240.0.0", "orientation": "portrait", "icon": "./assets/images/inpress-icon-white-on-black.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "runtimeVersion": "1.0.0", "ios": {"supportsTablet": false, "buildNumber": "240", "bundleIdentifier": "com.scoopt.inpress", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"NSLocationAlwaysAndWhenInUseUsageDescription": "InPress uses your location to deliver local news and match you with people in your area.", "NSLocationWhenInUseUsageDescription": "InPress uses your location to deliver local news and match you with people in your area.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": ["cstr6suwn9.skadnetwork"]}]}}, "android": {"versionCode": 240, "adaptiveIcon": {"foregroundImage": "./assets/images/inpress-icon-white-on-black.png", "backgroundColor": "#ffffff"}, "package": "com.scoopt.inpress", "googleServicesFile": "./google-services.json", "config": {"googleMaps": {"apiKey": "AIzaSyB08un4GCREXiuy0FSKQ43vUM1ccU9bgOI"}}, "permissions": ["android.permission.ACCESS_FINE_LOCATION", "com.google.android.gms.permission.AD_ID"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/inpress-icon-white-on-black.png"}, "plugins": ["expo-router", ["expo-font", {"fonts": ["assets/fonts/PPEditorialOld-Regular.otf", "assets/fonts/PPEditorialOld-Italic.otf", "assets/fonts/InterTight-Regular.ttf", "assets/fonts/InterTight-SemiBold.ttf"]}], ["@sentry/react-native/expo", {"organization": "inpress-1m", "project": "inpress-app"}], ["expo-image-picker", {"photosPermission": "InPress uses the photo library to allow you to select pictures for your profile."}], ["expo-camera", {"cameraPermission": "In<PERSON>ress uses your camera for taking pictures for your profile."}], "expo-localization", ["react-native-fbsdk-next", {"appID": "437278972477723", "clientToken": "37f42594766e72e5c2ee2aa86ec9f5c4", "displayName": "InPress", "autoLogAppEventsEnabled": true, "advertiserIDCollectionEnabled": true}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "InPress uses your location to deliver local news and match you with people in your area.", "locationWhenInUsePermission": "InPress uses your location to deliver local news and match you with people in your area."}], "react-native-appsflyer", "expo-tracking-transparency"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "6845f5f4-0431-47e1-9e84-d75f2cecde9b"}, "appsflyer": {"appId": "id6456752116"}}, "owner": "inpress"}}