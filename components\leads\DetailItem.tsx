import { StyleSheet, Image, View, Text } from "react-native"

interface DetailItemProps {
  iconComponent: React.JSX.Element
  text: string
}

export const DetailItem = ({ iconComponent, text }: DetailItemProps) => {
  return (
    <View style={styles.container}>
      {iconComponent}
      <Text style={styles.text}>{text}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    marginVertical: 6,
    fontFamily: "InterTight-Regular",
    fontWeight: "bold",
    alignSelf: "center",
  },
})
