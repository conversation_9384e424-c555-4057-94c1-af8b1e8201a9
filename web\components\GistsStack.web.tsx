import TinderCard from "react-tinder-card"
import { GistCard } from "@/components/gists/GistCard"
import { articles } from "../../app/(stories)/(news)/feed/news"
import { map } from "lodash"
import { CardSwiper } from "./CardSwiper"

const onSwipe = () => {}
const onCardLeftScreen = () => {}

export const GistsStackWeb = () => {
  return (
    <div style={styles.container}>
      <CardSwiper articles={articles} onSwipe={onSwipe} />
      <div style={styles.buttons}>
        <button>left</button>
        <button>right</button>
      </div>
    </div>
  )
}
const styles = {
  container: {
    position: "relative" as const,
    width: "100%",
    height: "100vh",
    backgroundColor: "#f8f8f8",
    display: "flex",
    alignItems: "end",
    justifyContent: "center",
    borderColor: "black",
    borderWidth: 1,
    borderStyle: "solid",
  },
  buttons: {},
}
