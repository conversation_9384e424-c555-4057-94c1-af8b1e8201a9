import { StyleSheet, Text, View } from "react-native"
import { LeaderboardUser } from "@/types/user"
import { DARK_GREY, LIGHT_GREY, SANDSTONE } from "@/constants/Colors"
import { useRef } from "react"
import { Avatar } from "../widgets/Avatar"
import { FlatList } from "react-native-gesture-handler"
import { TimedImage } from "../TrackedImage"
import { IMAGES } from "@/constants/Images"
import _ from "lodash"

export type LeaderboardProps = {
  users: LeaderboardUser[]
  currentUserId: number
  showLargeVersion?: boolean
}

export const Leaderboard = ({
  users,
  currentUserId,
  showLargeVersion = false,
}: LeaderboardProps) => {
  const flatListRef = useRef<FlatList>(null)

  const renderRank = (rank: number) => {
    return (
      <View style={styles.rankContainer}>
        {rank < 4 ? (
          <TimedImage
            source={IMAGES[`Trophy_${rank}`]}
            name={`trophy-${rank}`}
            style={styles.trophyIcon}
          />
        ) : (
          <Text style={styles.rank}>{rank.toLocaleString()}</Text>
        )}
      </View>
    )
  }

  const renderUserRow = ({ item: user }: { item: LeaderboardUser }) => {
    return (
      <View style={styles.userContainer}>
        <View style={styles.userInnerContainer}>
          {renderRank(user.rank)}
          <Avatar user={user} size={AVATAR_HEIGHT} />
          <Text style={styles.userName} numberOfLines={1}>
            {user.firstName}
          </Text>
          {user.rank === 1 ? (
            <View style={styles.leaderView}>
              <Text style={styles.tagText}>{"LEADER"}</Text>
            </View>
          ) : user.id == currentUserId ? (
            <Text style={[styles.tagText, { color: LIGHT_GREY }]}>{"you"}</Text>
          ) : null}
        </View>
        <Text
          style={styles.pointsText}
        >{`${user.points.toLocaleString()} pts`}</Text>
      </View>
    )
  }

  const numberOfUsersToShow = showLargeVersion ? 5 : 3
  const paddingUserCount = (numberOfUsersToShow - 1) / 2
  const currentUserIndex = users.findIndex((user) => user.id === currentUserId)

  const lowestIndex = _.max([currentUserIndex - paddingUserCount, 0])!

  const highestIndex = _.min([
    currentUserIndex + paddingUserCount,
    users.length - 1,
  ])!

  const usersToShow =
    lowestIndex === 0
      ? users.slice(0, numberOfUsersToShow)
      : highestIndex === users.length - 1
      ? users.slice(-numberOfUsersToShow)
      : users.slice(lowestIndex, highestIndex + 1)

  const flatListHeight =
    AVATAR_HEIGHT * usersToShow.length + ROW_GAP * (usersToShow.length - 1)

  const containerHeight =
    (!showLargeVersion ? TITLE_HEIGHT + ROW_GAP * 1 : ROW_GAP * 2) +
    flatListHeight

  return (
    <View style={[styles.container, { height: containerHeight }]}>
      {!showLargeVersion && (
        <Text style={styles.title}>KNOWLEDGE LEADERBOARD</Text>
      )}
      <FlatList
        ref={flatListRef}
        data={usersToShow}
        ListHeaderComponent={
          showLargeVersion ? () => <View style={{ height: 0 }} /> : null
        }
        renderItem={renderUserRow}
        keyExtractor={(item) => item.id.toString()}
        style={{ height: flatListHeight }}
        contentContainerStyle={[
          styles.flatListContent,
          { height: flatListHeight },
        ]}
        scrollEnabled={false}
      />
    </View>
  )
}

const TITLE_HEIGHT = 45
const AVATAR_HEIGHT = 28
const ROW_GAP = 16

const styles = StyleSheet.create({
  container: {
    backgroundColor: DARK_GREY,
    paddingHorizontal: 12,
    borderRadius: 20,
    width: "100%",
  },
  title: {
    height: TITLE_HEIGHT,
    paddingVertical: TITLE_HEIGHT / 3,
    color: SANDSTONE,
    textAlign: "center",
    fontSize: 12,
    fontFamily: "InterTight-SemiBold",
  },
  flatListContent: {
    gap: ROW_GAP,
  },
  userContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  userInnerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  rankContainer: {
    alignItems: "center",
    width: 32,
  },
  rank: {
    color: "#FFFFFF",
    fontSize: 12,
    fontFamily: "InterTight-Regular",
  },
  trophyIcon: {
    width: 20,
    height: 20,
    resizeMode: "contain",
  },
  userName: {
    color: "white",
    fontSize: 14,
    fontFamily: "InterTight-Regular",
    maxWidth: 70,
  },
  leaderView: {
    backgroundColor: "#F6BD40",
    paddingHorizontal: 6,
    paddingVertical: 3.5,
    borderRadius: 5,
  },
  tagText: {
    fontSize: 14,
    fontFamily: "InterTight-Regular",
  },
  pointsText: {
    color: LIGHT_GREY,
    fontSize: 14,
    fontFamily: "InterTight-Regular",
  },
})
