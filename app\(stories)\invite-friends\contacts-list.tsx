import {
  ContactsList_,
  ContactWithHash,
} from "@/components/settings/matchWithFriends/ContactsList"
import { Screen } from "@/components/Themed"
import {
  ContactStatus,
  StatusByContactHash,
} from "@/apiQueries/connectRequests"
import { normalizePhoneNumber } from "@/utils/phoneNumbers"
import { sha256 } from "@/utils/hash"
import { useState } from "react"
import _ from "lodash"

const contactWithImage = {
  id: "6",
  contactType: "person",
  name: "<PERSON>",
  phoneNumbers: [{ number: "6666666666", label: "mobile" }],
  hash: "d65e3ea58c88e2e0a259286fe4e0358ff8e6f007d8fc39002408875578e41f59",
  imageAvailable: true,
  image: {
    uri: "https://i.pravatar.cc/300",
  },
}

const contacts: ContactWithHash[] = [
  {
    id: "1",
    name: "<PERSON>",
    phoneNumbers: [{ number: "12223334444" }],
    hash: "52383c27bcb71a04576627c34ebb6ac1dab2c7850f91db91a1745ce8d8479e97",
  },
  {
    id: "2",
    name: "Jane Doe",
    phoneNumbers: [{ number: "0987654321" }],
    hash: "dd33d16a1017bda818bf3d13630f8fec1d245dd213c6d96a88a35bb6cf23d66b",
  },
  {
    id: "3",
    name: "Alice",
    phoneNumbers: [{ number: "3333333333" }],
    hash: "0e57e33861d7cd5677f43da8eae9255c963ed0748e49b01faf7bd25210ab673f",
  },
  {
    id: "4",
    name: "Kaitlin",
    phoneNumbers: [{ number: "4444444444" }],
    hash: "1cc7e574eb3f4e5c88cc23eff9b751f4e5ac4bbd0fe8c5404b69a94202c3aeae",
  },
  {
    id: "5",
    name: "Susan",
    phoneNumbers: [{ number: "5555555555" }],
    hash: "910a625c4ba147b544e6bd2f267e130ae14c591b6ba9c25cb8573322dedbebd0",
  },
  contactWithImage,
].map((c) => ({
  id: c.id,
  name: c.name,
  hash: c.hash,
  phoneNumber: normalizePhoneNumber(c.phoneNumbers![0].number!),
  contactType: "person",
}))

const statuses: ContactStatus[] = [
  {
    showAsExisting: false,
    requestSent: false,
    connected: false,
  },
  {
    showAsExisting: true,
    requestSent: false,
    connected: false,
  },
  {
    showAsExisting: false,
    requestSent: true,
    connected: false,
  },
  {
    showAsExisting: true,
    requestSent: true,
    connected: false,
  },
  {
    showAsExisting: true,
    requestSent: false,
    connected: true,
  },
  {
    showAsExisting: true,
    requestSent: true,
    connected: true,
  },
]

const statusByContactHash: StatusByContactHash = {}

contacts.forEach(async (contact, index) => {
  const hash = await sha256(contact.phoneNumber)
  statusByContactHash[hash] = statuses[index % statuses.length]
})

export default function Story() {
  const [statuses, setStatuses] =
    useState<StatusByContactHash>(statusByContactHash)

  const handleSendRequest = (contact: ContactWithHash) => {
    console.log("Send request triggered", JSON.stringify(contact, null, 2))

    console.log("Simulating refresh of contact statuses")
    setTimeout(() => {
      console.log("Refreshed contact statuses")
      setStatuses((prev) => ({
        ...prev,
        [contact.hash]: {
          ...prev[contact.hash],
          requestSent: _.random(0, 1) === 1,
        },
      }))
    }, 1000)
  }

  return (
    <Screen>
      <ContactsList_
        contacts={contacts}
        latestStatusesByHash={statuses}
        onSendRequest={handleSendRequest}
        onSendSmsRequest={(contacts) =>
          console.log("Send multi invite", JSON.stringify(contacts, null, 2))
        }
      />
    </Screen>
  )
}
