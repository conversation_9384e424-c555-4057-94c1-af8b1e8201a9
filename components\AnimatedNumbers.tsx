import {
  Animated,
  Easing,
  LayoutChangeEvent,
  StyleProp,
  StyleSheet,
  TextStyle,
  View,
  ViewStyle,
} from "react-native"
import React, { useCallback, useEffect, useMemo, useRef } from "react"
import { NormalText } from "./StyledText"

// This component is based on the package here: https://github.com/heyman333/react-native-animated-numbers
// That package was causing a visual glitch and not animating correctly.

interface CustomAnimatedNumbersProps {
  animateToNumber: number
  fontStyle?: StyleProp<TextStyle>
  animationDuration?: number
  includeComma?: boolean
  easing?: Animated.TimingAnimationConfig["easing"]
  containerStyle?: StyleProp<ViewStyle>
}

const AnimatedNumbers: React.FC<CustomAnimatedNumbersProps> = ({
  animateToNumber,
  fontStyle,
  animationDuration = 1000,
  includeComma = false,
  easing = Easing.out(Easing.quad),
  containerStyle,
}) => {
  const animationRef = useRef<Animated.CompositeAnimation | null>(null)
  const animationsRef = useRef<Animated.Value[]>([])
  const [textHeight, setTextHeight] = React.useState(0)

  // Convert number to absolute and string for processing
  const absNumber = Math.abs(animateToNumber)
  const numberString = absNumber.toString()

  // Create array of digits and separators
  const numberArray = useMemo(() => {
    if (includeComma) {
      const formatted = absNumber.toLocaleString("en-US")
      return formatted.split("")
    }
    return numberString.split("")
  }, [absNumber, includeComma])

  // Track digit indices to align with animations
  const digitIndices = useMemo(() => {
    const indices: number[] = []
    let animIndex = 0
    numberArray.forEach((char, index) => {
      if (!isNaN(parseInt(char))) {
        indices[index] = animIndex++
      } else {
        indices[index] = -1 // Non-digit
      }
    })
    return indices
  }, [numberArray])

  // Initialize animations for each digit
  const animations = useMemo(() => {
    return numberArray
      .filter((char) => !isNaN(parseInt(char)))
      .map(() => new Animated.Value(0))
  }, [numberArray.length])

  // Store animations to prevent memory leaks
  useEffect(() => {
    animationsRef.current = animations
    return () => {
      if (animationRef.current) {
        animationRef.current.stop()
      }
    }
  }, [animations])

  // Measure text height for animation
  const handleLayout = useCallback(
    (e: LayoutChangeEvent) => {
      const { height } = e.nativeEvent.layout
      if (height > 0 && height !== textHeight) {
        setTextHeight(height)
      }
    },
    [textHeight],
  )

  // Animate digits when number or height changes
  useEffect(() => {
    if (textHeight === 0) return

    if (animationRef.current) {
      animationRef.current.stop()
    }

    const compositions = animationsRef.current.map((animation, animIndex) => {
      const digit = parseInt(
        numberArray[digitIndices.findIndex((i) => i === animIndex)] || "0",
      )
      return Animated.timing(animation, {
        toValue: -digit * textHeight,
        duration: animationDuration,
        easing,
        useNativeDriver: true,
      })
    })

    if (compositions.length > 0) {
      animationRef.current = Animated.parallel(compositions)
      animationRef.current.start()
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.stop()
      }
    }
  }, [numberArray, digitIndices, textHeight, animationDuration, easing])

  return (
    <>
      {textHeight !== 0 && (
        <View
          style={[styles.container, containerStyle, { height: textHeight }]}
        >
          {animateToNumber < 0 && (
            <NormalText
              style={[fontStyle, styles.text, { height: textHeight }]}
            >
              -
            </NormalText>
          )}
          {numberArray.map((char, index) => {
            if (isNaN(parseInt(char))) {
              // Render comma or other separator
              return (
                <NormalText
                  key={`char-${index}`}
                  style={[fontStyle, styles.text, { height: textHeight }]}
                >
                  {char}
                </NormalText>
              )
            }

            const animIndex = digitIndices[index]
            return (
              <View
                key={`digit-${index}`}
                style={[styles.digitContainer, { height: textHeight }]}
              >
                <Animated.View
                  style={{
                    transform: [{ translateY: animations[animIndex] || 0 }],
                  }}
                >
                  {[...Array(10).keys()].map((num) => (
                    <NormalText
                      key={num}
                      style={[fontStyle, styles.text, { height: textHeight }]}
                    >
                      {num}
                    </NormalText>
                  ))}
                </Animated.View>
              </View>
            )
          })}
        </View>
      )}
      <View style={styles.hiddenTextContainer}>
        <NormalText style={fontStyle} onLayout={handleLayout} numberOfLines={1}>
          0
        </NormalText>
      </View>
    </>
  )
}

export default AnimatedNumbers

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  digitContainer: {
    overflow: "hidden",
  },
  text: {
    textAlign: "center",
  },
  hiddenTextContainer: {
    opacity: 0,
    position: "absolute",
    pointerEvents: "none",
  },
})
