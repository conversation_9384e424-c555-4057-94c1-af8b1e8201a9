import { disabledOpacity } from "@/components/constants"
import moment, { MomentInput } from "moment"
import {
  Pressable,
  StyleSheet,
  Text,
  View,
  StyleProp,
  ViewStyle,
} from "react-native"

type ActionButton = {
  text: string
  onPress: () => void
}

export function GenericNotificationItem({
  timestamp,
  TextComponent,
  LogoComponent,
  primaryButton,
  secondaryButton,
  disabled,
  containerStyle,
}: {
  timestamp: MomentInput
  TextComponent: React.ReactNode
  LogoComponent: React.ReactNode
  primaryButton?: ActionButton
  secondaryButton?: ActionButton
  disabled?: boolean
  containerStyle?: StyleProp<ViewStyle>
}) {
  const disabledButtonStyle = disabled ? { opacity: disabledOpacity } : {}

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.logoContainer}>{LogoComponent}</View>
      <View style={styles.contentContainer}>
        {TextComponent}
        <View style={styles.optionsContainer}>
          {primaryButton && (
            <Pressable
              style={[styles.primaryButton, disabledButtonStyle]}
              disabled={disabled}
              onPress={primaryButton.onPress}
            >
              <Text style={styles.primaryButtonText}>{primaryButton.text}</Text>
            </Pressable>
          )}
          {secondaryButton && (
            <Pressable
              style={[styles.secondaryButton, disabledButtonStyle]}
              disabled={disabled}
              onPress={secondaryButton.onPress}
            >
              <Text style={styles.secondaryButtonText}>
                {secondaryButton.text}
              </Text>
            </Pressable>
          )}
        </View>
      </View>
      <Text style={styles.timestampText}>
        {moment().diff(timestamp, "hours") > 6
          ? moment(timestamp).format("h:mm a")
          : moment(timestamp).fromNow()}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: "5%",
    paddingVertical: 24,
    height: 125,
    flexDirection: "row",
    flex: 1,
  },
  logoContainer: {
    width: 77,
    height: 77,
    borderRadius: 77 / 2,
  },
  contentContainer: {
    justifyContent: "center",
    flex: 1,
    paddingLeft: "3%",
    paddingRight: "5%",
  },
  optionsContainer: {
    flexDirection: "row",
    marginTop: 10,
  },
  primaryButton: {
    backgroundColor: "black",
    borderRadius: 32,
    paddingVertical: 8,
    paddingHorizontal: 15,
    marginRight: "5%",
  },
  primaryButtonText: {
    fontSize: 10,
    lineHeight: 10,
    fontFamily: "InterTight-Regular",
    color: "white",
    letterSpacing: 1,
  },
  secondaryButton: {
    borderColor: "black",
    borderRadius: 32,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderWidth: 1,
    backgroundColor: "white",
  },
  secondaryButtonText: {
    fontSize: 10,
    lineHeight: 10,
    fontFamily: "InterTight-Regular",
    color: "black",
    letterSpacing: 1,
  },
  timestampText: {
    maxWidth: 80,
    minWidth: 80,
    alignItems: "flex-end",
    textAlign: "right",
    fontSize: 10,
    color: "black",
    fontFamily: "InterTight-SemiBold",
  },
})
