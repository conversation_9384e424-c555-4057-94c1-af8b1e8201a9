# InPress app

Backwards compatibility notes are tracked in Notion and marked with (BC-X) where X is a tracking number.

## Environment variables

InPress API URL: EXPO_PUBLIC_INPRESS_URL

Stream (for chat) API key: EXPO_PUBLIC_STREAM_API_KEY

Mixpanel (for user analytics) token: EXPO_PUBLIC_MIXPANEL_TOKEN

Admin token (for impersonating users): EXPO_PUBLIC_ADMIN_TOKEN

Admin password (also for impersonation): EXPO_PUBLIC_ADMIN_PASSWORD

Revenue Cat iOS key: EXPO_PUBLIC_REVENUE_CAT_IOS_KEY

Revenue Cat Android key: EXPO_PUBLIC_REVENUE_CAT_ANDROID_KEY

Tracking token: EXPO_PUBLIC_TRACKING_TOKEN

User session version: EXPO_PUBLIC_SESSION_VERSION - see doc: https://www.notion.so/Session-versioning-585a79d0b3e5486a8157d25f3315203b?pvs=4

Activate story mode: EXPO_PUBLIC_RENDER_STORIES (by setting to "true")

InPress Public API key: EXPO_PUBLIC_PUBLIC_API_TOKEN - Used to make requests to unauthenticated endpoints like uploading images.

Enable error tracking: EXPO_PUBLIC_ENABLE_TRACKING

Ad Attribution: EXPO_PUBLIC_APPS_FLYER_DEV_KEY - Integrates with Apps Flyer

## Development

A git pre-push hook script is provided at `pre-push-git-hook` which can be placed in .git/hooks/pre-push in order to run typescript checking prior to every push.

The codebase is also formatted using prettier.js. The config file is included in the repo.

The initial screen of the app requires an invite code to create an account. For testing purposes you can use the code 9573.

### Stories

Most of the components can be developed in a "story mode" by setting the env var EXPO_PUBLIC_RENDER_STORIES to "true", i.e.:

`EXPO_PUBLIC_RENDER_STORIES=true npx expo start`

This allows them to be developed and tested offline without any external dependencies.

### Feature Flags

We use PostHog for feature flags. There's a development project set up in PostHog for testing purposes.

### Payments

The app uses RevenueCat to facilitate payments, which is a cross-platform In-App-Purchase wrapper.

For development and testing purposes it is recommended that you use a sandbox user created on the App Developer site.

# Scripts

## Generating and inserting user data

There are a few scripts included for generating users for demo purposes and inserting them.

## Scalability testing

This script simulates multiple overlapping user scenarios where they create accounts and navigate around the app.

# Admin interface

There's an admin interface for impersonating users which can be accessed by tapping the upper right corner of the initial screen 6 times.

# Third-party Tools

## Sentry

Sentry tracks crashes and errors so it's easier to catch these preemptively and debug them.

It requires that a secret environment variable is set with EAS: `eas secret:create --scope project --name SENTRY_AUTH_TOKEN --value $SENTRY_TOKEN --type string`

# Installation

## Android

For notifications to work, the instructions here must be followed: https://docs.expo.dev/push-notifications/fcm-credentials/
