import { GenericNotificationItem } from "./GenericNotificationItem"
import { HitDayOrWeekStreakNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"
import { useLevels } from "@/context/LevelContext"

export function HitDayOrWeekStreakNotificationItem({
  item,
}: {
  item: HitDayOrWeekStreakNotification
}) {
  const { loading, streaks } = useLevels()

  if (loading || !streaks) {
    return null
  }

  const streak = streaks.find((s) => s.type === item.streakType)

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          Congrats on your <BoldText>{streak!.title}</BoldText> streak! You
          earned {item.pointsEarned} points. Keep it going!
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
