import { Share, StyleSheet, Text } from "react-native"
import { SANDSTONE } from "@/constants/Colors"
import { Button } from "../Button"
import ShareIcon from "../icons/settings/ShareIcon"
import { Card } from "react-native-paper"
import { miniCardStyles } from "./constants"

interface ShareReminderProps {
  referralCode: string
  displayMode?: "Card" | "Button"
}

const ShareReminder = ({
  referralCode,
  displayMode = "Card",
}: ShareReminderProps) => {
  const handleClick = () => {
    Share.share({
      message: `Hi, I thought you might enjoy InPress, the first news-based dating and friends app. You can use my code ${referralCode} to join! https://inpress.app/`,
    })
  }

  return displayMode === "Button" ? (
    <Button
      text="Share"
      iconComponent={<ShareIcon fill="white" />}
      isTextOnLeft
      onPress={handleClick}
    />
  ) : (
    <Card style={styles.container} onPress={handleClick}>
      <Card.Content>
        <Text>
          <Text style={miniCardStyles.intro}>Give a week, get a week!</Text>{" "}
          <Text style={miniCardStyles.bodyText}>
            Invite a friend to InPress, and you'll both get a free week of
            premium when they sign up. Click to share!
          </Text>
        </Text>
      </Card.Content>
    </Card>
  )
}

const HEIGHT = 95

const styles = StyleSheet.create({
  container: {
    ...miniCardStyles.container,
    height: HEIGHT,
  },
})

export default ShareReminder
export { HEIGHT }
