import { LeadsPage_, LeadsPageProps_ } from "@/components/LeadsPage"
import { USER } from "../(account)/account"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { faker } from "@faker-js/faker"
import { useState } from "react"
import { Lead } from "@/types/social"

const latitude = 38.93317
const longitude = -77.03179

export const LEADS: Lead[] = [
  {
    id: 1,
    score: 0.94,
    topics: [
      { name: "Music", rootId: 1128 },
      { name: "<PERSON>", rootId: 1128 },
      { name: "Science", rootId: 2876 },
    ],
    user: { ...USER, latitude: latitude + 0.001, longitude: longitude + 0.001 },
  },
  {
    id: 2,
    score: 0.76,
    topics: [
      { name: "Cooking", rootId: 0 },
      { name: "Travel", rootId: 3302 },
      { name: "Photography", rootId: 1128 },
    ],
    user: { ...USER, latitude: latitude + 0.002, longitude: longitude + 0.002 },
  },
  {
    id: 3,
    score: 0.32,
    topics: [
      { name: "Sports", rootId: 2454 },
      { name: "Movies", rootId: 1128 },
      { name: "Books", rootId: 1128 },
    ],
    user: { ...USER, latitude: latitude + 0.003, longitude: longitude + 0.003 },
  },
].map((lead, index) => ({
  ...lead,
  user: {
    ...lead.user,
    id: index + 1,
    firstName: faker.person.firstName(),
  },
}))

export const DEFAULT_PROPS: LeadsPageProps_ = {
  leads: LEADS,
  maxLeadsReached: false,
  user: USER,
  connectionMode: ConnectionMode.Dates,
  onSwipe: (swipe: any) => Promise.resolve({ isMutual: swipe.type === "like" }),
  onHideAndReport: async () => {},
}

export default function LeadsStory() {
  const [leads, setLeads] = useState(LEADS)
  const handleReport = async (userId: number) => {
    setLeads((leads) => leads?.filter((l) => l.user.id !== userId))
  }

  return (
    <LeadsPage_
      {...DEFAULT_PROPS}
      leads={leads}
      onHideAndReport={handleReport}
    />
  )
}
