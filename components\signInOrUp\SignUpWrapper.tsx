import { MaterialIcons } from "@expo/vector-icons"
import { Screen, View } from "../Themed"
import { ScreenHeader } from "../widgets/ScreenHeader"
import {
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from "react-native"
import { ProgressBar } from "react-native-paper"
import { SvgProps } from "react-native-svg"
import { BROWNSTONE } from "@/constants/Colors"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"

export interface SignUpWrapperPassedProps {
  onBack?: () => void
  onNext?: () => void
}

export interface SignUpWrapperProps extends SignUp<PERSON>rapperPassedProps {
  progress?: number
  iconComponent?: (props: SvgProps) => JSX.Element
  title?: string
  subtitle?: string
  submitIsDisabled?: boolean
  screenStyle?: ViewStyle
  children: React.ReactNode
}

export const SignUpWrapper = ({
  progress,
  iconComponent,
  title,
  subtitle,
  submitIsDisabled,
  screenStyle,
  onBack,
  onNext,
  children,
}: SignUpWrapperProps) => {
  const { top: safeTop, bottom: safeBottom } = useSafeAreaInsets()

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <Screen
        style={{
          paddingTop: safeTop + SCREEN_PADDING,
          paddingBottom: safeBottom + SCREEN_PADDING,
          justifyContent: "flex-end",
          ...screenStyle,
        }}
      >
        {progress ? (
          <ProgressBar
            progress={progress}
            style={styles.progressBar}
            color={BROWNSTONE}
          />
        ) : null}
        {title && (
          <ScreenHeader
            title={title}
            subtitle={subtitle}
            iconComponent={iconComponent}
          />
        )}
        <View style={{ flexGrow: 1 }}>{children}</View>
        <View style={styles.navigationContainer}>
          {onBack && (
            <TouchableOpacity onPress={onBack}>
              <MaterialIcons
                name="arrow-circle-left"
                size={NAV_HEIGHT}
                color="black"
              />
            </TouchableOpacity>
          )}
          <View style={{ flex: 1 }} />
          {onNext && (
            <TouchableOpacity onPress={onNext} disabled={submitIsDisabled}>
              <MaterialIcons
                name="arrow-circle-right"
                size={56}
                color="black"
                style={{ opacity: submitIsDisabled ? 0.15 : 1 }}
              />
            </TouchableOpacity>
          )}
        </View>
      </Screen>
    </KeyboardAvoidingView>
  )
}

export const SCREEN_PADDING = hp("3%")
export const NAV_HEIGHT = 56

const styles = StyleSheet.create({
  progressBar: {
    backgroundColor: "rgba(0, 0, 0, 0.1)",
    borderRadius: 100,
  },
  navigationContainer: {
    height: NAV_HEIGHT,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
  },
})
