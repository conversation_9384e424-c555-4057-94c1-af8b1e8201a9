import { StyleSheet, View, Text } from "react-native"
import { sectionTitleStyle } from "./constants"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import _ from "lodash"
import { useSocialContext } from "@/context/SocialContext"
import { SocialEventType, Topic } from "@/types/social"
import { sectionGroups } from "../news/constants"
import { useEffect } from "react"
import { trackEvent } from "@/utils/tracking"

interface SharedInterestsPanelProps {
  topics: Topic[]
  horizontallyCentered?: boolean
}

const SharedInterestsPanel = ({
  topics,
  horizontallyCentered = false,
}: SharedInterestsPanelProps) => {
  const { rootTopicIdToName } = useSocialContext()

  useEffect(() => {
    if (topics.filter((t) => t.rootId === null).length > 0) {
      trackEvent(SocialEventType.LeadWithoutTopicRootIdSeen, {
        data: topics,
      })
    }
  }, [topics])

  const getSectionGroup = (topic: Topic) => {
    if (topic.rootId === null) return undefined
    const rootTopicName = rootTopicIdToName[topic.rootId]
    const group = sectionGroups.find((group) =>
      group.sections.some((section) => section.name === rootTopicName),
    )
    return group
  }

  const colors = [
    "#B8FFFB",
    "#AFE0FF",
    "#DBEDFD",
    "#D9D4FC",
    "#FFD6FA",
    "#FAE3EB",
    "#FFD2B2",
    "#FBE5C5",
    "#FFFDA0",
    "#DDFECF",
    "#A8FFC0",
    "#E6E6E6",
  ]

  const hashString = (str: string): number => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash |= 0
    }
    return hash
  }

  const getColorForString = (str: string): string => {
    const hash = hashString(str)
    const index = Math.abs(hash) % colors.length
    return colors[index]
  }

  return (
    <View style={[styles.topicsContainer]}>
      <Text
        style={[
          sectionTitleStyle,
          { textAlign: horizontallyCentered ? "center" : "auto" },
        ]}
      >
        Top shared interests
      </Text>
      <View
        style={[
          styles.topicTagsContainer,
          { justifyContent: horizontallyCentered ? "center" : "flex-start" },
        ]}
      >
        {topics.length === 0 ? (
          <Text style={styles.topicText}>No shared interests found yet</Text>
        ) : (
          _.uniqBy(topics, "name")
            .slice(0, 8)
            .map((topic, index) => {
              const group = getSectionGroup(topic)
              return (
                <View
                  key={index}
                  style={[
                    styles.topicTextContainer,
                    {
                      backgroundColor: group
                        ? group.backgroundColor
                        : getColorForString(topic.name),
                    },
                  ]}
                >
                  <Text
                    key={index}
                    style={[
                      styles.topicText,
                      group && { color: group.textColor },
                    ]}
                  >
                    {topic.name}
                  </Text>
                </View>
              )
            })
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  topicsContainer: {
    flexDirection: "column",
    textAlign: "center",
  },
  topicTagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingBottom: 5,
    gap: wp(3),
  },
  topicTextContainer: {
    borderRadius: 10,
    paddingHorizontal: 10,
    paddingVertical: 4,
    backgroundColor: "#F5F5F5",
  },
  topicText: {
    color: "black",
    fontSize: 14,
    textAlign: "left",
    fontFamily: "InterTight-SemiBold",
  },
})

export default SharedInterestsPanel
