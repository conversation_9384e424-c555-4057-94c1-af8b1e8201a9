import { StyleSheet, View, Text } from "react-native"
import { sectionTitleStyle } from "./constants"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import _ from "lodash"

interface SharedInterestsPanelProps {
  topics: string[]
  horizontallyCentered?: boolean
}

const SharedInterestsPanel = ({
  topics,
  horizontallyCentered = false,
}: SharedInterestsPanelProps) => {
  const colors = [
    "#B8FFFB",
    "#AFE0FF",
    "#DBEDFD",
    "#D9D4FC",
    "#FFD6FA",
    "#FAE3EB",
    "#FFD2B2",
    "#FBE5C5",
    "#FFFDA0",
    "#DDFECF",
    "#A8FFC0",
    "#E6E6E6",
  ]

  const hashString = (str: string): number => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash |= 0
    }
    return hash
  }

  const getColorForString = (str: string): string => {
    const hash = hashString(str)
    const index = Math.abs(hash) % colors.length
    return colors[index]
  }

  return (
    <View style={[styles.topicsContainer]}>
      <Text
        style={[
          sectionTitleStyle,
          { textAlign: horizontallyCentered ? "center" : "auto" },
        ]}
      >
        Top shared interests
      </Text>
      <View
        style={[
          styles.topicTagsContainer,
          { justifyContent: horizontallyCentered ? "center" : "flex-start" },
        ]}
      >
        {topics.length === 0 ? (
          <Text style={styles.topicText}>No shared interests found yet</Text>
        ) : (
          _.uniq(topics)
            .slice(0, 8)
            .map((topic, index) => (
              <View
                key={index}
                style={{
                  ...styles.topicTextContainer,
                  backgroundColor: getColorForString(topic),
                }}
              >
                <Text key={index} style={styles.topicText}>
                  {topic}
                </Text>
              </View>
            ))
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  topicsContainer: {
    flexDirection: "column",
    textAlign: "center",
  },
  topicTagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingBottom: 5,
    gap: wp(3),
  },
  topicTextContainer: {
    borderRadius: 10,
    paddingHorizontal: 10,
    paddingVertical: 4,
    backgroundColor: "#F5F5F5",
  },
  topicText: {
    color: "black",
    fontSize: 14,
    textAlign: "left",
    fontFamily: "InterTight-SemiBold",
  },
})

export default SharedInterestsPanel
