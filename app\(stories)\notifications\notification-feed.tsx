import { NotificationFeed_ } from "@/components/notifications/NotificationFeed"
import { Notification, NotificationType } from "@/apiQueries/notificationTypes"
import moment from "moment"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { ConnectRequestStatus } from "@/apiQueries/connectRequests"
import { random, range, sample } from "lodash"
import { faker } from "@faker-js/faker"
import { USER } from "../(account)/account"
import _ from "lodash"
import { Session } from "@/types/user"

const baseImageUrl =
  "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-"

const genders = ["female", "male"]

export const SESSION: Session = {
  version: "1",
  user: { ...USER, friendsModeIsActivated: true },
  token: "123",
}

export const NOTIFICATIONS: Notification[] = range(1, 10).map((i) => ({
  id: i.toString(),
  isRead: false,
  type: sample([
    NotificationType.NewFriendRequest,
    NotificationType.NewMatch,
    NotificationType.NewLike,
    NotificationType.FriendRequestAccepted,
  ]),
  createdAt: moment()
    .subtract((i - 1) * 500 + random(0, 500), "minutes")
    .toISOString(),
  user: {
    id: 4721,
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    image: {
      id: i,
      path: "images/profile4.jpg",
      url: `${baseImageUrl}${_.sample(genders)}${_.random(1, 4)}.jpg`,
    },
    isArchived: false,
  },
  connectRequest: {
    id: 1,
    connectionMode: ConnectionMode.Friends,
    status: sample([
      ConnectRequestStatus.PENDING,
      ConnectRequestStatus.ACCEPTED,
    ]),
  },
  chatChannel: {
    streamId: "123",
  },
  connectionMode: sample([ConnectionMode.Dates, ConnectionMode.Friends]),
}))

export default function NotificationFeedStory() {
  return (
    <NotificationFeed_
      notifications={NOTIFICATIONS}
      session={{
        version: "1",
        user: USER,
        token: "123",
      }}
      onNavigate={(params) => {
        console.log(params)
      }}
      onRefresh={() => {}}
    />
  )
}
