import { useState, useEffect } from "react"
import { View, StyleSheet, TouchableOpacity, Text } from "react-native"
import { Feather } from "@expo/vector-icons"
import { DraggableGrid } from "react-native-draggable-grid"
import { Ionicons } from "@expo/vector-icons"
import { Entypo } from "@expo/vector-icons"
import Colors from "@/constants/Colors"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"
import { pickImage, requestMediaPermissions } from "@/utils/images"
import { Image } from "expo-image"

export interface LocalOrRemoteImage {
  uri?: string
  path?: string
  url?: string
}

export default function MultiImagePicker({
  initialImages,
  onImagesChange,
  onDragChanged: onDragChange,
}: {
  initialImages?: LocalOrRemoteImage[]
  onImagesChange: (images: LocalOrRemoteImage[]) => void
  onDragChanged?: (enabled: boolean) => void
}) {
  const [images, setImages] = useState<(LocalOrRemoteImage | null)[]>(
    initialImages
      ? initialImages.concat(Array(6 - initialImages.length).fill(null))
      : Array(6).fill(null),
  )

  useEffect(() => {
    requestMediaPermissions()
  }, [])

  useEffect(() => {
    const nonNullImages = images.filter(
      (image) => image !== null,
    ) as LocalOrRemoteImage[]
    onImagesChange(nonNullImages)
  }, [images])

  const onClickDeleteImage = (index: number) => {
    setImages(images.map((item, i) => (i === index ? null : item)))
  }

  const handleImagePress = async (index: number) => {
    const uri = await pickImage()
    setImages(images.map((item, i) => (i === index ? { uri } : item)))
  }

  const renderItem = ({
    image,
    key,
  }: {
    image: LocalOrRemoteImage | null
    key: number
  }) => {
    const imageUri = image?.uri || image?.url

    return (
      <View key={`${imageUri}|${key}`}>
        <View style={styles.flatListItemStyle}>
          {imageUri && (
            <View style={styles.deleteIconView}>
              <TouchableOpacity onPress={() => onClickDeleteImage(key)}>
                <Entypo name="cross" color={Colors.light.redColor} size={25} />
              </TouchableOpacity>
            </View>
          )}
          {imageUri ? (
            <Image
              source={{ uri: imageUri }}
              style={styles.imageStyle}
              resizeMode="cover"
            />
          ) : (
            <TouchableOpacity
              style={styles.imageBackgroundViewStyle}
              onPress={() => handleImagePress(key)}
            >
              <View style={styles.addImageIconView}>
                <View>
                  <Feather name="image" color={"grey"} size={20} />
                </View>
                <View style={styles.plusIconStyle}>
                  <Ionicons
                    name="add-circle"
                    color={Colors.light.primaryColor}
                    size={20}
                  />
                </View>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
    )
  }

  return (
    <View style={{}}>
      <View style={styles.validationView}>
        <Text style={styles.validationTextStyle}>
          At least two photos are required.
        </Text>
      </View>
      <View style={styles.imageView}>
        <DraggableGrid
          style={{ margin: 0 }}
          numColumns={3}
          renderItem={renderItem}
          data={images.map((image, index) => ({
            image,
            key: index,
          }))}
          onDragStart={() => onDragChange && onDragChange(true)}
          onDragRelease={(items) => {
            setImages(items.map((item) => item.image))
            onDragChange && onDragChange(false)
          }}
        />

        <View style={styles.editView}>
          <Text style={styles.editTextStyle}>
            Tap and hold to drag and reorder
          </Text>
        </View>

        <View style={styles.bottomView}>
          <View style={styles.bulbIconStyle}>
            <Ionicons name="bulb-outline" color={Colors.light.text} size={20} />
          </View>
          <Text style={styles.bottomTextStyle}>
            Use a clear shot of your face or full body for your first photo. No
            nudity, or AI-generated content.
          </Text>
        </View>
      </View>
    </View>
  )
}

export const styles = StyleSheet.create({
  imageView: {
    height: hp("42%"),
  },
  flatListItemStyle: {
    position: "relative",
    alignItems: "flex-start",
  },
  imageStyle: {
    width: hp("13%"),
    height: hp("13%"),
    borderWidth: wp("0.5%"),
    borderRadius: hp("1%"),
    backgroundColor: "white",
  },
  validationView: {
    marginBottom: hp("1.5%"),
    alignItems: "flex-start",
  },
  validationTextStyle: {
    fontSize: hp("1.5%"),
    fontWeight: "500",
    color: Colors.light.validTextColor,
  },
  addImageIconView: {
    flexDirection: "row",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
  },
  plusIconStyle: {
    top: hp("1.2%"),
    right: wp("2%"),
  },
  imageBackgroundViewStyle: {
    width: hp("13%"),
    height: hp("13%"),
    justifyContent: "center",
    backgroundColor: "white",
    alignItems: "center",
    borderColor: Colors.light.greyColor,
    borderWidth: wp("0.5%"),
    borderRadius: hp("1%"),
    borderStyle: "dashed",
  },
  bottomView: {
    borderColor: Colors.light.tabIconDefault,
    bottom: hp("0.5%"),
    alignSelf: "center",
    borderWidth: wp("0.5%"),
    borderRadius: hp("1%"),
    paddingHorizontal: hp("2.5%"),
    paddingBottom: 4,
    alignItems: "center",
  },
  bottomTextStyle: {
    textAlign: "center",
    fontSize: hp("1.5%"),
    paddingBottom: hp("0.5%"),
    color: Colors.light.text,
  },
  bulbIconStyle: {
    bottom: hp("1.8%"),
    width: wp("4.5%"),
    backgroundColor: Colors.light.background,
    alignItems: "center",
    padding: 0,
  },
  editView: {
    marginBottom: 24,
  },
  editTextStyle: {
    color: Colors.light.text,
    fontSize: hp("1.5%"),
  },
  deleteIconView: {
    position: "absolute",
    top: hp("0.5%"),
    zIndex: 1000,
    right: wp("2%"),
  },
})
