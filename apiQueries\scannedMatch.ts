import { ScannedMatch } from "@/components/leads/ScannedMatchModal"
import { INPRESS_API_URL } from "./constants"
import { get } from "@/network"
import { convertRawUnknownTypeUser, RawUnknownTypeUser } from "@/types/user"
import { combineTopicFields, RawTopic } from "@/types/social"

export interface RawScannedMatch
  extends Omit<ScannedMatch, "thisUser" | "otherUser" | "topics"> {
  thisUser: RawUnknownTypeUser
  otherUser: RawUnknownTypeUser
  topics: string[]
  topics_with_roots: RawTopic[] | null
}

type GetScannedMatchParams = {
  otherUserId: number
}

interface GetScannedMatchProps extends GetScannedMatchParams {
  token: string
}

const getScannedMatch = async ({
  token,
  otherUserId,
}: GetScannedMatchProps): Promise<ScannedMatch> => {
  const rawMatch = await get<GetScannedMatchParams, RawScannedMatch>(
    `${INPRESS_API_URL}/scanned-match`,
    token,
    { otherUserId },
  )

  return {
    ...rawMatch,
    thisUser: convertRawUnknownTypeUser(rawMatch.thisUser),
    otherUser: convertRawUnknownTypeUser(rawMatch.otherUser),
    topics: combineTopicFields(rawMatch.topics, rawMatch.topics_with_roots),
  }
}

export { getScannedMatch }
