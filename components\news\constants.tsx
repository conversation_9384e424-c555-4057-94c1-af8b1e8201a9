import {
  BL<PERSON>,
  BUR<PERSON>UNDY,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DARK_SOFT_GREEN,
  LIGHT_BLUE,
  LIGHT_BURGUNDY,
  LIGHT_CHAMPAGNE,
  LIGHT_OLIVE_GREEN,
  LIGHT_PURPLE,
  OLIVE_GREEN,
  <PERSON><PERSON>PL<PERSON>,
  SANDSTON<PERSON>,
  SOFT_GREEN,
} from "@/constants/Colors"
import {
  leadsPath,
  matchesPath,
  accountPath,
  alertsPath,
  levelsPath,
  matchWithFriendsPath,
  qrCodePath,
  newsFeedPath,
} from "@/utils/deepLinks"
import { HrefObject } from "@/utils/localParams"
import { router } from "expo-router"
import { StyleSheet } from "react-native"

export type Layout =
  | "alerts"
  | "miniArticleList"
  | "horizontal"
  | "list"
  | "grid"
  | "fullImage"
  | "spotlight"
  | "announcements"
  | "share"

export type Section = {
  name: string
  layout: Layout
  subtitle?: string
  title: string
  isNews: boolean
}

const layouts: Layout[] = ["horizontal", "list", "spotlight", "fullImage"]

const alertsSection: Section = {
  name: "alerts",
  title: "InPress Alerts",
  layout: "alerts" as Layout,
  isNews: false,
}

const finishReadingSection: Section = {
  name: "finishReading",
  title: "Finish Reading",
  layout: "miniArticleList" as Layout,
  isNews: true,
}

const allOtherSections: Section[] = [
  { name: "topStories", title: "Top Stories" },
  { name: "forYou", title: "For You" },
  { name: "local", title: "Local" },
  { name: "eventsNearYou", title: "Events Near You" },
  {
    name: "announcements",
    title: "InPress InSights",
    layout: "announcements" as Layout,
    isNews: false,
  },
  {
    name: "discussionStarters",
    title: "Discussion Starters",
    subtitle: "Articles perfect for sparking conversations.",
  },
  { name: "culture", title: "Culture" },
  { name: "sports", title: "Sports" },
  { name: "music", title: "Music" },
  { name: "worldNews", title: "World News" },
  { name: "technology", title: "Technology" },
  { name: "foodCooking", title: "Food & Cooking" },
  { name: "travel", title: "Travel" },
  { name: "lifestyle", title: "Lifestyle" },
  {
    name: "share",
    title: "Invite a Friend",
    layout: "share" as Layout,
    isNews: false,
  },
  { name: "politics", title: "Politics" },
  { name: "feelGood", title: "Feel Good" },
  { name: "business", title: "Business" },
  { name: "booksLiterature", title: "Books & Literature" },
  { name: "science", title: "Science" },
  { name: "filmTv", title: "Film & TV" },
  { name: "history", title: "History" },
  { name: "relationshipsDating", title: "Relationships & Dating" },
  { name: "health", title: "Health" },
  { name: "environment", title: "Environment" },
].map((section, index) => ({
  layout: layouts[index % layouts.length],
  isNews: true,
  ...section,
}))

export const newsfeedSections = [
  finishReadingSection,
  alertsSection,
  ...allOtherSections,
]

export const getSectionByName = (name: string): Section => {
  return newsfeedSections.find((section) => section.name === name)!
}

export type SectionGroup = {
  title: string
  name: string
  sections: Section[]
  backgroundColor: string
  textColor: string
}

export const sectionGroups: SectionGroup[] = [
  {
    title: "The Front Page",
    name: "theFrontPage",
    sections: [getSectionByName("topStories"), getSectionByName("forYou")],
    backgroundColor: OLIVE_GREEN,
    textColor: LIGHT_OLIVE_GREEN,
  },
  {
    title: "Meeting Place",
    name: "meetingPlace",
    sections: [
      getSectionByName("local"),
      getSectionByName("eventsNearYou"),
      getSectionByName("discussionStarters"),
    ],
    backgroundColor: BURGUNDY,
    textColor: LIGHT_BURGUNDY,
  },
  {
    title: "The Scene",
    name: "theScene",
    sections: [
      getSectionByName("culture"),
      getSectionByName("music"),
      getSectionByName("booksLiterature"),
      getSectionByName("filmTv"),
      getSectionByName("history"),
    ],
    backgroundColor: PURPLE,
    textColor: LIGHT_PURPLE,
  },
  {
    title: "Leisure",
    name: "leisure",
    sections: [
      getSectionByName("sports"),
      getSectionByName("foodCooking"),
      getSectionByName("travel"),
      getSectionByName("lifestyle"),
    ],
    backgroundColor: BLUE,
    textColor: LIGHT_BLUE,
  },
  {
    title: "The Rundown",
    name: "theRundown",
    sections: [
      getSectionByName("worldNews"),
      getSectionByName("politics"),
      getSectionByName("business"),
      getSectionByName("environment"),
      getSectionByName("science"),
      getSectionByName("technology"),
    ],
    backgroundColor: CHAMPAGNE,
    textColor: LIGHT_CHAMPAGNE,
  },
  {
    title: "The Pulse",
    name: "thePulse",
    sections: [
      getSectionByName("feelGood"),
      getSectionByName("relationshipsDating"),
      getSectionByName("health"),
    ],
    backgroundColor: SOFT_GREEN,
    textColor: DARK_SOFT_GREEN,
  },
]

export const newsStyles = StyleSheet.create({
  smallArticleTitle: {
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
    letterSpacing: 0.16,
    color: "black",
    lineHeight: 19.2,
  },
})

export const SPOTLIGHT_IMAGE_HEIGHT = 168
export const newsfeedMarginHorizontal = 10

export const gridArticleMargin = 8

const miniCardFont = {
  fontSize: 16,
  fontFamily: "InterTight-SemiBold",
}

export const miniCardStyles = {
  container: {
    backgroundColor: "black",
    marginHorizontal: newsfeedMarginHorizontal,
  },
  intro: {
    color: SANDSTONE,
    ...miniCardFont,
  },
  bodyText: {
    color: "white",
    ...miniCardFont,
  },
}

export type Destination =
  | "news"
  | "leads"
  | "matches"
  | "account"
  | "levels"
  | "matchWithFriends"
  | "qrCode"
  | "alerts"

const destinationMap: Record<Destination, HrefObject["pathname"]> = {
  news: newsFeedPath,
  leads: leadsPath,
  matches: matchesPath,
  account: accountPath,
  levels: levelsPath,
  matchWithFriends: matchWithFriendsPath,
  qrCode: qrCodePath,
  alerts: alertsPath,
}

export const navigateToInAppDestination = (destination: Destination): void => {
  const url = destinationMap[destination]
  if (url) {
    router.navigate(url)
  } else {
    console.error("Invalid in-app destination", destination)
  }
}
