import { GistsStack_ } from "@/components/gists/GistsStack"
import React from "react"
import { Article } from "@/types/news"
import { articles } from "../../../app/(stories)/(news)/feed/news"
import { View, StyleSheet, Text } from "react-native"
import { StoryNewsProvider } from "../../../app/(stories)/story-components/StoryNewsProvider"
import { StoryLevelsProvider } from "../../../app/(stories)/story-components/StoryLevelsProvider"

const GistsStackStory: React.FC = () => {
  console.log("rendering GistsStack")
  return (
    <StoryLevelsProvider>
      <StoryNewsProvider>
        <View style={styles.container}>
          <Text>GistsStackStory</Text>
          <GistsStack_
            articles={articles}
            demoMode={true}
            onSwipe={async () => {}}
          />
        </View>
      </StoryNewsProvider>
    </StoryLevelsProvider>
  )
}

const styles = StyleSheet.create({
  container: {
    height: 800,
    width: 350,
    alignSelf: "center",
  },
})

export default GistsStackStory

/*import { Text } from "react-native"
export default function GistsStackStory() {
  return <Text>GistsStackStory</Text>
}*/
