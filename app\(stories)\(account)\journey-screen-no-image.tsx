import { JourneyScreen } from "@/screens/account/JourneyScreen"
import { USER } from "./account"
import { LEADERBOARD_USERS } from "../(news)/survey-completion/leaderboard"

export default function AccountStory() {
  return (
    <JourneyScreen
      user={{ ...USER, images: [], points: 2474 }}
      streakDays={3}
      articlesRead={12}
      articlesRated={8}
      leaderboardUsers={LEADERBOARD_USERS}
      onUpdatePicture={async (uri) => console.log("uri", uri)}
    />
  )
}
