import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { useHideHeader } from "../story_utils"
import {
  InScoreStep,
  SUBTITLE,
  TITLE,
} from "@/components/signInOrUp/InScoreStep"
import { router } from "expo-router"

export default function Story() {
  useHideHeader()

  return (
    <SignUpWrapper
      title={TITLE}
      subtitle={SUBTITLE}
      progress={0.85}
      onBack={() => router.back()}
      onNext={() => {}}
    >
      <InScoreStep />
    </SignUpWrapper>
  )
}
