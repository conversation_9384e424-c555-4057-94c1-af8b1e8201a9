import { StyleSheet, View } from "react-native"
import { Text } from "../Themed"
import { BROWNSTONE } from "@/constants/Colors"
import { SimilarityPreferenceSection } from "@/screens/account/SimilarityPreferenceSection"
import { GenderPreferenceSection } from "@/screens/account/GenderPreferenceSection"
import { Switch } from "react-native-paper"
import { useEffect, useState } from "react"
import { Button } from "../Button"
import { AgePreferenceSection } from "@/screens/account/AgePreferenceSection"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"
import _ from "lodash"
import { FontAwesome5, Foundation } from "@expo/vector-icons"
import moment from "moment"
import { Preferences } from "@/types/user"
import { pushWithParams } from "@/utils/localParams"
import { ActivateParams } from "@/app/(signInOrUp)/activate-mode"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { activateModePath } from "@/utils/deepLinks"

type MatchPreferencesSectionProps = {
  mode: ActiveConnectionMode
  modeIsActivated: boolean
  isExpanded: boolean
  canBeCollapsed: boolean
  userCreatedAt: string
  onExpandedChange: (value: boolean) => void
  initialPreferences: Preferences | undefined
  onPreferencesChange: (preferences: Preferences) => void
}

export default function MatchPreferencesSection({
  mode,
  isExpanded,
  canBeCollapsed,
  userCreatedAt,
  onExpandedChange,
  initialPreferences,
  onPreferencesChange,
}: MatchPreferencesSectionProps) {
  const [preferences, setNewPreferences] = useState(initialPreferences)

  const userCreatedInLastWeek = moment().diff(moment(userCreatedAt), "days") < 7

  useEffect(() => {
    if (preferences) onPreferencesChange(preferences)
  }, [preferences])

  const renderSectionContents = () => {
    if (!preferences) return

    const { genders, minAge, maxAge, minSimilarity, maxSimilarity } =
      preferences

    return (
      <View style={styles.body}>
        <AgePreferenceSection
          initialMinAge={minAge}
          initialMaxAge={maxAge}
          onChange={(values) =>
            setNewPreferences({
              ...preferences,
              minAge: values[0],
              maxAge: values[1],
            })
          }
        />
        <SimilarityPreferenceSection
          minSimilarity={minSimilarity}
          maxSimilarity={maxSimilarity}
          disabled={userCreatedInLastWeek}
          onChange={(values) =>
            setNewPreferences({
              ...preferences,
              minSimilarity: values[0],
              maxSimilarity: values[1],
            })
          }
        />
        <GenderPreferenceSection
          initialGenders={genders}
          onChange={(genders) => setNewPreferences({ ...preferences, genders })}
        />
      </View>
    )
  }

  const renderActivateModeButton = () => (
    <View style={styles.buttonContainer}>
      <Button
        text={`Set up ${_.startCase(mode)} profile`}
        isTextOnLeft={true}
        iconComponent={
          mode === ConnectionMode.Dates ? (
            <Foundation name="heart" size={24} color="white" />
          ) : (
            <FontAwesome5 name="user-friends" size={24} color="white" />
          )
        }
        onPress={() =>
          pushWithParams<ActivateParams>({
            pathname: activateModePath,
            params: { presetConnectionMode: mode },
          })
        }
      />
    </View>
  )

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{_.startCase(mode)}</Text>
        <Switch
          value={isExpanded}
          onValueChange={onExpandedChange}
          color={BROWNSTONE}
          disabled={isExpanded && !canBeCollapsed}
        />
      </View>
      {isExpanded
        ? preferences
          ? renderSectionContents()
          : renderActivateModeButton()
        : null}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#DDDDDD",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  title: {
    fontFamily: "InterTight-SemiBold",
  },
  body: {
    paddingVertical: 16,
    gap: 48,
  },
  buttonContainer: {
    marginTop: 35,
  },
})
