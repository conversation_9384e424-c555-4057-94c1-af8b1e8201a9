import { USER } from "../(account)/account"
import { MatchesPage_ } from "@/components/MatchesPage"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { defaultProps } from "../chats/chat-preview"

export default function Story() {
  return (
    <MatchesPage_
      matches={[
        {
          ...defaultProps,
          topics: ["topic1", "topic2"],
          chatChannelId: "channelId",
        },
      ]}
      activeConnectionMode={ConnectionMode.Friends}
      user={{ ...USER, friendsModeIsActivated: true }}
      testMode={true}
      onSelectChannel={async () => {}}
    />
  )
}
