import React, {
  createContext,
  Dispatch,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react"
import {
  submitGistRating as submitGistRating_,
  SubmitGistRatingProps,
} from "@/apiQueries/newsFeed"
import {
  getNewsArticles,
  reportArticle as reportArticle_,
  ReportArticleParams,
  SubmitSurveyProps,
  submitSurvey as submitSurvey_,
  SurveyResponse,
} from "@/apiQueries/apiQueries"
import { getSectionByName, Section } from "@/components/news/constants"
import { getSession } from "./session"
import { isUnratedArticle, rebuildArticles } from "@/utils/processArticles"
import { Article, NewsEventType } from "@/types/news"
import { trackEvent } from "@/utils/tracking"
import { StorageKey, usePersistentState } from "@/app/hooks/usePersistentState"

export type NewsTabName = "gists" | "newsfeed"

export interface NewsContextProps {
  activeTab: NewsTabName
  setActiveTab: React.Dispatch<React.SetStateAction<NewsTabName>>
  loadArticles: () => Promise<void>
  isLoading: boolean
  articles: Article[]
  setArticles: Dispatch<React.SetStateAction<Article[]>>
  swipeHintShown: boolean
  setSwipeHintShown: (shown: boolean) => void
  activeSection: Section
  setActiveSection: Dispatch<React.SetStateAction<Section>>
  selectedSection: Section | null
  setSelectedSection: Dispatch<React.SetStateAction<Section | null>>
  shuffleModeOn: boolean
  setShuffleModeOn: (on: boolean) => void
  gistIsSwiping: boolean
  setGistIsSwiping: Dispatch<React.SetStateAction<boolean>>
  handleFullRating: (props: SubmitSurveyProps) => Promise<SurveyResponse>
  handleGistRating: (props: SubmitGistRatingProps) => Promise<void>
  reportArticle: (props: ReportArticleParams) => Promise<void>
}

const NewsContext = createContext<NewsContextProps | undefined>(undefined)

export type NewsProviderProps = {
  initialArticles?: Article[]
  submitFullRating?: (props: SubmitSurveyProps) => Promise<SurveyResponse>
  submitGistRating?: (props: SubmitGistRatingProps) => Promise<void>
  reportArticle?: NewsContextProps["reportArticle"]
  children: React.ReactNode
}

export const NewsProvider: React.FC<NewsProviderProps> = ({
  initialArticles,
  submitFullRating,
  submitGistRating,
  reportArticle,
  children,
}) => {
  const [activeTab, setActiveTab] = useState<NewsTabName>("gists")
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [articles, setArticles] = useState<Article[]>([])
  const [swipeHintShown, setSwipeHintShown] = usePersistentState<boolean>(
    StorageKey.SwipeHintShown,
    false,
  )

  const topStories = getSectionByName("topStories")
  const [activeSection, setActiveSection] = useState<Section>(topStories)
  const [selectedSection, setSelectedSection] = useState<Section | null>(null)

  const [shuffleModeOn, setShuffleModeOn] = useState<boolean>(false)

  const [gistIsSwiping, setGistIsSwiping] = useState<boolean>(false)

  const hasMounted = useRef(false)

  const rebuildAndSetArticles = ({
    articles,
    startSection = activeSection,
    shuffleModeOn_ = shuffleModeOn,
  }: {
    articles: Article[]
    startSection?: Section
    shuffleModeOn_?: boolean
  }) => {
    const rebuiltArticles = rebuildArticles({
      articles,
      startSection,
      shuffleModeOn: shuffleModeOn_,
    })
    setArticles(rebuiltArticles)
  }

  const loadArticles = async () => {
    try {
      const session = getSession()
      const articles = await getNewsArticles(session!.token)
      rebuildAndSetArticles({ articles })
    } catch (error) {
      console.error("Failed to load articles:", error)
    }
  }

  useEffect(() => {
    if (!initialArticles) {
      loadArticles().then(() => setIsLoading(false))
    } else {
      rebuildAndSetArticles({ articles: initialArticles })
    }
  }, [])

  useEffect(() => {
    if (!hasMounted.current) {
      hasMounted.current = true
      return
    }

    rebuildAndSetArticles({
      articles,
      shuffleModeOn_: activeTab === "gists" ? shuffleModeOn : false,
    })
  }, [activeTab, shuffleModeOn])

  useEffect(() => {
    const firstUnratedArticle = articles.find(isUnratedArticle)
    if (firstUnratedArticle) {
      setActiveSection(getSectionByName(firstUnratedArticle.frontpageSection))
    }
  }, [articles])

  useEffect(() => {
    if (selectedSection) {
      rebuildAndSetArticles({
        articles,
        startSection: selectedSection,
      })
      setActiveSection(selectedSection)
      setShuffleModeOn(false)
      setSelectedSection(null)
    }
  }, [selectedSection])

  useEffect(() => {
    trackEvent(
      gistIsSwiping
        ? NewsEventType.GistSwipingStarted
        : NewsEventType.GistSwipingStopped,
    )
  }, [gistIsSwiping])

  const handleFullRating = async (props: SubmitSurveyProps) => {
    try {
      const submitFn = submitFullRating || submitSurvey_
      const response = await submitFn(props)

      setArticles((prevArticles) =>
        prevArticles.map((a) =>
          props.articleId === a.id ? { ...a, isSurveyed: true } : a,
        ),
      )

      return response
    } catch (error) {
      console.error("Failed to submit full rating:", error)
      throw error
    }
  }

  const handleGistRating = async (props: SubmitGistRatingProps) => {
    try {
      const submitFn = submitGistRating || submitGistRating_
      await submitFn(props)

      setArticles((prevArticles) =>
        prevArticles.map((a) =>
          props.articleId === a.id ? { ...a, gistRating: props.rating } : a,
        ),
      )
    } catch (error) {
      console.error("Failed to submit gist rating:", error)
    }
  }

  return (
    <NewsContext.Provider
      value={{
        activeTab,
        setActiveTab,
        loadArticles,
        isLoading,
        articles,
        setArticles,
        swipeHintShown,
        setSwipeHintShown,
        activeSection,
        setActiveSection,
        selectedSection,
        setSelectedSection,
        shuffleModeOn,
        setShuffleModeOn,
        gistIsSwiping,
        setGistIsSwiping: setGistIsSwiping,
        handleFullRating,
        handleGistRating,
        reportArticle: reportArticle || reportArticle_,
      }}
    >
      {children}
    </NewsContext.Provider>
  )
}

export const useNewsContext = () => {
  const context = useContext(NewsContext)
  if (!context) {
    throw new Error("Context must be used within a provider")
  }
  return context
}
