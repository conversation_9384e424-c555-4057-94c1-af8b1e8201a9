import { ArticleSurvey } from "@/components/ratings/ArticleSurvey"
import { POINT_EVENTS } from "./survey-completion/level-3"

export default function ArticleSurveyStory() {
  return (
    <ArticleSurvey
      initialPoints={20}
      soundsAndHapticsOn
      onSurveyComplete={async () => ({
        pointEvents: POINT_EVENTS,
        leveledUp: true,
      })}
      onFetchLeaderboard={async () => []}
    />
  )
}
