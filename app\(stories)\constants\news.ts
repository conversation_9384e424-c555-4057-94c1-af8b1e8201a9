import { NewsProviderProps } from "@/context/NewsContext"
import { NEWSFEED_ARTICLES } from "../(news)/feed/news"

export const NEWS_PROVIDER_PROPS: Required<NewsProviderProps> = {
  initialArticles: NEWSFEED_ARTICLES,
  submitFullRating: async (props) => {
    console.log("Submitted full rating:", props)
    return null as any
  },
  submitGistRating: async (props) =>
    console.log("Submitted gist rating:", props),
  reportArticle: async () => console.log("Submitted report:"),
  children: null,
}
