import { ComponentP<PERSON>, use<PERSON><PERSON>back, useEffect, useRef, useState } from "react"
import FontAwesome from "@expo/vector-icons/FontAwesome"
import {
  router,
  Tabs,
  useFocusEffect,
  useGlobalSearchParams,
  usePathname,
} from "expo-router"
import Colors from "@/constants/Colors"
import { useColorScheme } from "@/components/useColorScheme"
import { useClientOnlyValue } from "@/components/useClientOnlyValue"
import { useSession } from "@/ctx"
import { Text } from "@/components/Themed"
import { Redirect } from "expo-router"
import { Image, StyleSheet, View } from "react-native"
import { EventType, trackEvent } from "@/utils/tracking"
import { Loader } from "@/components/widgets/Loader"
import * as Notifications from "expo-notifications"
import { getLeads, getMatches, Lead, Match } from "@/apiQueries/apiQueries"
import Toggle from "@/components/Toggle"
import ConnectionModeContext, {
  ActiveConnectionMode,
} from "@/context/ModeContext"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"

import NewsIcon, { NewsSelectedIcon } from "@/components/Tab/NewsIcon"
import LeadsIcon, { LeadsSelectedIcon } from "@/components/Tab/LeadsIcon"
import MatchesIcon, { MatchesSelectedIcon } from "@/components/Tab/MatchesIcon"
import SettingsIcon, {
  SettingsSelectedIcon,
} from "@/components/Tab/SettingsIcon"
import NotificationsIcon, {
  NotificationsSelectedIcon,
} from "@/components/Tab/NotificationsIcon"
import {
  checkIfNotificationsEnabled,
  getPushToken,
  handleResponse,
  setupNotifications,
} from "@/utils/pushNotifications"
import { useLastNotificationResponse } from "expo-notifications"
import AsyncStorage from "@react-native-async-storage/async-storage"
import _ from "lodash"
import { useTestMode } from "@/utils/testModeCtx"
import { NotificationType } from "@/apiQueries/notificationTypes"
import { posthog } from "@/services/posthog"
import { getNotifications } from "@/apiQueries/notifications"
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from "react-native-reanimated"
import { isUserWithProfile, Session } from "@/types/user"
import { updateProfile, updatePushToken } from "@/apiQueries/auth"
import {
  checkVersionStatus,
  VersionStatus,
} from "@/apiQueries/checkVersionStatus"
import { UpdateRequired } from "@/screens/UpdateRequired"
import { getUserSettings, updateUserSettings } from "@/apiQueries/userSettings"
import {
  getUserPosition,
  MissingLocationPermissionError,
} from "../hooks/useLocation"
import { hasRecentlyUpdatedLocation } from "@/utils/location"
import { LAST_LOC_UPDATE_TIME_KEY } from "@/utils/localStorage"
import moment from "moment"
import { fontStyles } from "@/styles"
import { AppProviders } from "@/context/AppProviders"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { GISTS_MESSAGE } from "@/components/signInOrUp/SecretlyTappableLogo"
import { Feature, useTestingContext } from "@/context/TestingContext"

configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
})

type TestModeItem = "short" | "long"

// You can explore the built-in icon families and icons on the web at https://icons.expo.fyi/
function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>["name"]
  color: string
}) {
  return <FontAwesome size={28} style={{ marginBottom: -3 }} {...props} />
}

const NOTIFICATION_COUNT_POLLING_INTERVAL_MS = 60 * 1000

export default function TabLayout() {
  if (process.env.EXPO_PUBLIC_RENDER_STORIES === "true") {
    console.log("Redirecting to stories")
    return <Redirect href="/(stories)" />
  }

  const pathname = usePathname()
  const globalParams = useGlobalSearchParams()

  const receivedListener = useRef<Notifications.Subscription>()
  const responseListener = useRef<Notifications.Subscription>()

  const colorScheme = useColorScheme()
  const { session, refreshSession, isLoading } = useSession()
  const { toggleTestMode, deactivateTestMode } = useTestMode()
  const { featureIsOn, toggleFeature } = useTestingContext()

  const [versionStatus, setVersionStatus] = useState<VersionStatus>()
  const [activeConnectionMode, setActiveConnectionMode] =
    useState<ActiveConnectionMode>()
  const lastNotificationResponse = useLastNotificationResponse()
  const [lastPathname, setLastPathname] = useState(pathname)
  const [sessionWasRefreshed, setSessionWasRefreshed] = useState(false)
  const [newsTapCount, setNewsTapCount] = useState(0)
  const [testModePattern, setTestModePattern] = useState<TestModeItem[]>([])

  const [unreadMessagesCount, setUnreadMessagesCount] = useState(0)
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0)

  const { bottom } = useSafeAreaInsets()

  const fetchNotifications = useCallback(async () => {
    if (!session) return
    const notifications = await getNotifications(session.token)
    const unreadNotifications = notifications.filter((n) => !n.isRead)
    const [unreadMessages, unreadOther] = _.partition(
      unreadNotifications,
      (n) => n.type === NotificationType.NewMessage,
    )

    setUnreadNotificationCount(unreadOther.length)
    setUnreadMessagesCount(unreadMessages.length)
  }, [session])

  useEffect(() => {
    const fetchNotificationsInterval = setInterval(() => {
      fetchNotifications()
    }, NOTIFICATION_COUNT_POLLING_INTERVAL_MS)

    return () => clearInterval(fetchNotificationsInterval)
  }, [session])

  useFocusEffect(() => {
    checkVersionStatus().then(setVersionStatus)

    if (pathname === "/matches") {
      setUnreadMessagesCount(0)
    } else if (pathname === "/notifications") {
      setUnreadNotificationCount(0)
    }
    fetchNotifications()

    if (lastPathname !== pathname) {
      setLastPathname(pathname)
      setTestModePattern([])
      deactivateTestMode()
    }

    try {
      trackEvent(EventType.ViewedScreen, {
        route: pathname,
        route_params: globalParams,
      })
      Notifications.setBadgeCountAsync(0)
    } catch (e) {
      console.error("Failed to track event", e)
    }

    posthog.reloadFeatureFlagsAsync()
  })

  const checkTestModePattern = () => {
    const correctPattern = ["short", "short", "long", "short", "long"]
    if (_.isEqual(testModePattern, correctPattern)) {
      toggleTestMode()
    }
  }

  useEffect(() => {
    checkTestModePattern()
  }, [testModePattern])

  useEffect(() => {
    if (session) {
      if (!sessionWasRefreshed) {
        console.log("Refreshing session")
        refreshSession(session.token)
        setSessionWasRefreshed(true)
      } else {
        console.log("Session was already refreshed")
      }

      if (!activeConnectionMode) {
        if (isUserWithProfile(session.user)) {
          setActiveConnectionMode(
            session.user.friendsModeIsActivated
              ? ConnectionMode.Friends
              : ConnectionMode.Dates,
          )
        } else {
          setActiveConnectionMode(ConnectionMode.Dates)
        }
      }

      checkIfNotificationsEnabled().then(async (enabled) => {
        if (!enabled) {
          return
        }

        await setupNotifications()
        const pushToken = await getPushToken()
        await updatePushToken({ token: session.token, pushToken })

        if (!receivedListener.current)
          receivedListener.current =
            Notifications.addNotificationReceivedListener((notification) => {
              trackEvent(EventType.NotificationReceived, {
                data: notification.request.content,
              })
            })

        if (!responseListener.current)
          responseListener.current =
            Notifications.addNotificationResponseReceivedListener(
              (response) => {
                handleResponse({
                  response,
                  setActiveConnectionMode,
                })
              },
            )
      })

      return () => {
        receivedListener.current &&
          Notifications.removeNotificationSubscription(receivedListener.current)
        responseListener.current &&
          Notifications.removeNotificationSubscription(responseListener.current)
      }
    }

    async function prefetchImages() {
      if (!session || !isUserWithProfile(session.user)) {
        return
      }

      let datesLeads: Lead[] = []
      let datesMatches: Match[] = []
      let friendsLeads: Lead[] = []
      let friendsMatches: Match[] = []

      if (session.user.datesModeIsActivated) {
        const { leads } = await getLeads({
          token: session.token,
          connectionMode: ConnectionMode.Dates,
        })
        datesLeads = leads

        datesMatches = await getMatches({
          token: session.token,
          connectionMode: ConnectionMode.Dates,
        })
      }

      if (session.user.friendsModeIsActivated) {
        const { leads } = await getLeads({
          token: session.token,
          connectionMode: ConnectionMode.Friends,
        })
        friendsLeads = leads

        friendsMatches = await getMatches({
          token: session.token,
          connectionMode: ConnectionMode.Friends,
        })
      }

      const usersToPrefetchImagesOf = [
        datesLeads.map((lead) => lead.user),
        datesMatches.map((match) => match.user),
        friendsLeads.map((lead) => lead.user),
        friendsMatches.map((match) => match.user),
        [session.user],
      ].flat()

      for await (const user of usersToPrefetchImagesOf) {
        if (user.images.length > 0) {
          await Image.prefetch(user.images[0].url)
        }
      }
    }

    prefetchImages()
  }, [session])

  useEffect(() => {
    let isMounted = true
    if (!isMounted || !lastNotificationResponse?.notification) {
      return
    }

    AsyncStorage.getItem("lastHandledNotificationId").then((lastId) => {
      if (lastId === lastNotificationResponse.notification.request.identifier) {
        return
      }

      handleResponse({
        response: lastNotificationResponse,
        setActiveConnectionMode,
      })

      AsyncStorage.setItem(
        "lastHandledNotificationId",
        lastNotificationResponse.notification.request.identifier,
      )
    })

    return () => {
      isMounted = false
    }
  }, [lastNotificationResponse])

  const maybeUpdateLocation = async (session: Session) => {
    const settings = await getUserSettings({ token: session.token })

    if (!settings.autoUpdateLocation) {
      console.log("Auto update location is disabled")
      return
    }

    if (await hasRecentlyUpdatedLocation()) {
      console.log("Location was already updated recently ")
      return
    }

    try {
      const position = await getUserPosition()
      await updateProfile({ token: session.token, userUpdate: position })
      await AsyncStorage.setItem(
        LAST_LOC_UPDATE_TIME_KEY,
        moment().toISOString(),
      )
      trackEvent(EventType.LocationAutoUpdated)
      console.log("Location updated successfully")
    } catch (error: any) {
      if (error instanceof MissingLocationPermissionError) {
        await updateUserSettings({
          token: session.token,
          autoUpdateLocation: false,
        })
      } else {
        console.error("Error updating location", error)
      }
    }
  }

  useEffect(() => {
    if (!session) return

    maybeUpdateLocation(session)
  }, [session])

  if (isLoading) {
    return <Loader />
  }

  // Only require authentication within the (app) group's layout as users
  // need to be able to access the (auth) group and sign in again.
  if (!session) {
    console.log("Redirecting to login")
    // On web, static rendering will stop here as the user is not authenticated
    // in the headless Node process that the pages are rendered in.
    return <Redirect href="/(signInOrUp)" />
  }

  const handleTitlePress = () => {
    setTestModePattern([...testModePattern, "short"])
  }

  const handleTitleLongPress = () => {
    setTestModePattern([...testModePattern, "long"])
  }

  const handleNewsPress = () => {
    setNewsTapCount((prevCount) => prevCount + 1)
    if (newsTapCount >= 8) {
      if (!featureIsOn(Feature.Gists)) {
        alert(GISTS_MESSAGE)
      } else {
        alert("Gists is now disabled")
      }
      toggleFeature(Feature.Gists)
      setNewsTapCount(0)
    }
  }

  const renderTabScreen = ({
    name,
    title,
    icon,
    selectedIcon,
    options,
    listeners,
  }: {
    name: string
    title: string
    icon: React.JSX.Element
    selectedIcon: React.JSX.Element
    options?: ComponentProps<typeof Tabs.Screen>["options"]
    usesActivityIndicator?: boolean
    listeners?: ComponentProps<typeof Tabs.Screen>["listeners"]
  }) => (
    <Tabs.Screen
      name={name}
      options={{
        title,
        tabBarIcon: ({ focused }) => {
          return <View>{focused ? selectedIcon : icon}</View>
        },
        tabBarShowLabel: false,
        ...options,
      }}
      listeners={listeners}
    />
  )

  const shouldShowToggle =
    isUserWithProfile(session.user) &&
    (session.user.connectionMode === ConnectionMode.Both ||
      (session.user.datesModeIsActivated &&
        session.user.friendsModeIsActivated))

  if (versionStatus === "update_required") {
    return <UpdateRequired />
  }

  return (
    <ConnectionModeContext.Provider
      value={
        activeConnectionMode && {
          activeConnectionMode,
          setActiveConnectionMode,
        }
      }
    >
      <AppProviders>
        <Tabs
          screenListeners={{
            tabPress: (e) => {
              if (e.target?.startsWith("account-") && pathname !== "/account") {
                router.replace("/(app)/account")
              }
            },
          }}
          screenOptions={{
            tabBarActiveTintColor: Colors[colorScheme ?? "light"].tint,
            // Disable the static render of the header on web
            // to prevent a hydration error in React Navigation v6.
            headerShown: useClientOnlyValue(false, true),
            tabBarStyle: [styles.tabBar, { height: TAB_HEIGHT + bottom }],
          }}
        >
          <Tabs.Screen redirect name="index" />
          {renderTabScreen({
            name: "news",
            title: "News Feed",
            icon: <NewsIcon />,
            selectedIcon: <NewsSelectedIcon />,
            options: {
              title: "",
              headerShown: false,
            },
            listeners: {
              tabPress: (e) => handleNewsPress(),
            },
          })}
          {renderTabScreen({
            name: "leads",
            title: "Leads",
            icon: <LeadsIcon />,
            selectedIcon: <LeadsSelectedIcon />,
            options: {
              headerTitle: "",
              headerLeft: () => (
                <Text
                  style={styles.tabTitle}
                  onPress={handleTitlePress}
                  onLongPress={handleTitleLongPress}
                >
                  Leads
                </Text>
              ),
              headerRight: () => shouldShowToggle && <Toggle />,
            },
          })}
          {renderTabScreen({
            name: "matches",
            title: "Matches",
            usesActivityIndicator: true,
            icon: <MatchesIcon notificationCount={unreadMessagesCount} />,
            selectedIcon: <MatchesSelectedIcon />,
            options: {
              headerTitle: "",
              headerLeft: () => <Text style={styles.tabTitle}>Matches</Text>,
              headerRight: () => shouldShowToggle && <Toggle />,
            },
          })}
          {renderTabScreen({
            name: "account",
            title: "Profile",
            icon: <SettingsIcon />,
            selectedIcon: <SettingsSelectedIcon />,
            options: {
              headerShown: false,
              popToTopOnBlur: true,
            },
          })}
          {renderTabScreen({
            name: "notifications",
            title: "Notifications",
            icon: (
              <NotificationsIcon notificationCount={unreadNotificationCount} />
            ),
            selectedIcon: <NotificationsSelectedIcon />,
            options: {
              headerTitle: "",
              headerLeft: () => (
                <Text style={styles.tabTitle}>Notifications</Text>
              ),
              popToTopOnBlur: true,
            },
          })}
        </Tabs>
      </AppProviders>
    </ConnectionModeContext.Provider>
  )
}

export const TAB_HEIGHT = 49

const styles = StyleSheet.create({
  tabBar: {
    shadowOffset: { width: 0, height: -4 },
    shadowRadius: 4,
    shadowOpacity: 0.15,
    paddingHorizontal: 25,
    paddingTop: 4,
  },
  tabTitle: {
    fontSize: 28,
    marginLeft: 24,
    paddingBottom: 10,
    ...fontStyles.editorial,
  },
})
