import { useEffect, useState } from "react"
import { <PERSON>ert, View } from "react-native"
import { SignUpWrapper } from "./SignUpWrapper"
import { Button } from "../Button"
import { TextInput } from "../TextInput"
import { useSession } from "@/ctx"
import { router } from "expo-router"
import {
  sendPhoneVerification,
  uploadNewProfileImages,
  verifyPhoneNumber,
} from "@/apiQueries/auth"
import { PasswordStep } from "./PasswordStep"
import { GenderPrefs, GenderPrefStep } from "./GenderPrefStep"
import { BiographyStep } from "./BiographyStep"
import BioIcon from "../icons/onboarding/BioIcon"
import { SvgProps } from "react-native-svg"
import NameIcon from "../icons/onboarding/NameIcon"
import VerificationIcon from "../icons/onboarding/VerificationIcon"
import DateGenderPreferenceIcon from "../icons/onboarding/DateGenderPreferenceIcon"
import PicturesIcon from "../icons/onboarding/PicturesIcon"
import { MembershipStep } from "./MembershipStep"
import { ConnectionMode, ConnectionModeStep } from "./ConnectionModeStep"
import { EulaStep } from "./EulaStep"
import _ from "lodash"
import DiamondIcon from "../icons/onboarding/DiamondIcon"
import { EventType, trackAttributionEvent, trackEvent } from "@/utils/tracking"
import moment from "moment"
import NotificationStep, {
  SUBTITLE as notificationStepSubtitle,
  TITLE as notificationStepTitle,
} from "./NotificationStep"
import { ActiveConnectionMode } from "@/context/ModeContext"
import PromptStep from "./promptStep/PromptStep"
import { Loader } from "../widgets/Loader"
import AsyncStorage from "@react-native-async-storage/async-storage"
import MultiImagePicker, { LocalOrRemoteImage } from "../MultiImagePicker"
import PermissionStep from "./PermissionStep"
import LocationIcon from "../icons/onboarding/LocationIcon"
import { isInTheUSA } from "@/utils/allowableAreas"
import CodeVerification from "./CodeVerification"
import { MIN_IMAGES } from "./constants"
import PhoneIcon from "../icons/onboarding/PhoneIcon"
import PhoneStep from "./PhoneStep"
import VerifyPhoneStep, { CODE_LENGTH } from "./VerifyPhoneStep"
import { attemptReferralRegistration } from "@/apiQueries/inviteCode"
import {
  IS_NEWS_ONLY_KEY,
  loadfromStorage,
  ONBOARDING_DATA_KEY,
  saveToStorage,
} from "@/utils/localStorage"
import NotificationsIconLarge from "../icons/onboarding/NotificationsIconLarge"
import { getUserPosition } from "@/app/hooks/useLocation"
import { LatLng } from "react-native-maps"
import { registerWithoutProfile } from "@/apiQueries/registerUser"
import { birthdateFormat, ScoopResponse } from "@/types/user"
import { GenderStep } from "./GenderStep"
import { JobStep } from "./JobStep"
import JobIcon from "../icons/onboarding/JobIcon"
import GenderIcon from "../icons/onboarding/GenderIcon"
import BirthdayIcon from "../icons/onboarding/BirthdayIcon"
import { BirthdatePicker } from "../BirthdatePicker"
import { OutOfUsaStep } from "./OutOfUsaStep"
import { GeoAreaStatus, getGeoAreaStatus } from "@/apiQueries/geoAreas"
import { JustBeYourselfStep } from "./JustBeYourselfStep"
import { LowUserArea } from "./LowUserArea"
import { captureScreen } from "react-native-view-shot"
import { uploadScreenshot } from "@/apiQueries/tracking"

const isNewUserWithProfile = (
  user: FinishedNewUser | FinishedNewAccount,
  isNewsOnly: boolean,
): user is FinishedNewUser => {
  return !isNewsOnly
}

export interface FinishedNewAccount {
  accessCode?: string
  accessCodeType?: string
  eulaAgreementDate: Date
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  password: string
  passwordConfirmation: string
  latitude: number
  longitude: number
  isNewsOnly: boolean
  pushToken: string
}

export interface FinishedNewProfile {
  birthdate: string
  occupation: string
  gender: string
  connectionMode: ConnectionMode
  datesModeIsActivated: boolean
  friendsModeIsActivated: boolean
  genderPrefs: GenderPrefs
  minAgePref: number
  maxAgePref: number
  minSimilarityPref: number
  maxSimilarityPref: number
  images: LocalOrRemoteImage[]
  biography: string
  scoopResponses: ScoopResponse[]
}

export interface FinishedNewUser
  extends FinishedNewAccount,
    FinishedNewProfile {}

export type NewUser = Partial<FinishedNewUser> & {
  birthdate: string
  connectionMode: ConnectionMode
  genderPrefs: FinishedNewProfile["genderPrefs"]
  scoopResponses: FinishedNewProfile["scoopResponses"]
}

export const DEFAULT_BIRTHDATE = moment()
  .subtract(18, "years")
  .format(birthdateFormat)

export const defaultNewUser: NewUser = {
  birthdate: DEFAULT_BIRTHDATE,
  genderPrefs: [],
  connectionMode: ConnectionMode.Both,
  datesModeIsActivated: false,
  friendsModeIsActivated: false,
  minAgePref: 18,
  maxAgePref: 99,
  minSimilarityPref: 0,
  maxSimilarityPref: 100,
  biography: "",
  scoopResponses: [],
}

export type Step = (
  | {
      bodyComponent: () => JSX.Element
      dontIncludeWrapper: true
    }
  | {
      title: string
      subtitle?: string
      submitIsDisabled?: boolean
      hideNavigation?: boolean
      hideNext?: boolean
      iconComponent?: (props: SvgProps) => JSX.Element
      bodyComponent?: () => JSX.Element
      dontIncludeWrapper?: false
    }
) & {
  label: string
  onNext?: () => void
}

export interface Prompt {
  id: number
  text: string
}

export const SignUp = () => {
  const { register, refreshSession } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [signUpError, setSignUpError] = useState<string>()
  const [isNewsOnly, setIsNewsOnly] = useState<boolean | undefined>()

  const loadMode = async () => {
    try {
      const isNewsOnlyStr = await AsyncStorage.getItem(IS_NEWS_ONLY_KEY)
      setIsNewsOnly(isNewsOnlyStr === "true")
    } catch (e) {
      console.error(e)
    }
  }

  useEffect(() => {
    loadMode()
  }, [])

  if (isNewsOnly === undefined) return <Loader />
  const takeAndUploadScreenshot = async () => {
    try {
      const uri = await captureScreen({ format: "png", quality: 1 })
      await uploadScreenshot(uri)
    } catch (error) {
      console.error("Error capturing or uploading screenshot:", error)
    }
  }

  const handleRegister = async (
    newUserOrAccount: FinishedNewUser | FinishedNewAccount,
  ) => {
    newUserOrAccount.isNewsOnly = isNewsOnly

    try {
      setIsLoading(true)

      let response

      if (isNewUserWithProfile(newUserOrAccount, isNewsOnly)) {
        const connectionMode =
          newUserOrAccount.connectionMode === ConnectionMode.Both ||
          newUserOrAccount.connectionMode === ConnectionMode.Dates
            ? ConnectionMode.Dates
            : ConnectionMode.Friends

        const imageIds = await uploadNewProfileImages({
          images: newUserOrAccount.images,
          connectionMode,
        })

        response = await register({ ...newUserOrAccount, imageIds })
      } else {
        response = await registerWithoutProfile(newUserOrAccount)
      }

      attemptReferralRegistration(newUserOrAccount)
      trackAttributionEvent("af_complete_registration", {
        af_registration_method: "email",
        user_id: response.user.id,
      })
      await refreshSession(response.token)
      await AsyncStorage.removeItem(ONBOARDING_DATA_KEY)
      router.push("/news/feed")
    } catch (e: any) {
      const message = e.message || "An unknown error occurred"

      trackEvent(EventType.SignUpFailed, {
        data: {
          error: message,
        },
      })

      try {
        setSignUpError(message)
      } catch (e) {
        setSignUpError("An unknown error occurred")
      }
    }
    setIsLoading(false)
  }

  return (
    <SignUp_<FinishedNewUser>
      defaultProfile={defaultNewUser}
      isLoading={isLoading}
      isNewsOnly={isNewsOnly}
      signUpError={signUpError}
      takeAndUploadScreenshot={takeAndUploadScreenshot}
      sendPhoneVerification={sendPhoneVerification}
      verifyPhoneNumber={verifyPhoneNumber}
      getUserPosition={getUserPosition}
      getGeoAreaStatus={getGeoAreaStatus}
      onRetry={() => setSignUpError(undefined)}
      handleRegister={handleRegister}
      handleTrackEvent={trackEvent}
    />
  )
}

type SignUpState = {
  stepIndex: number
  profile: Partial<FinishedNewUser>
  isOutOfUSA: boolean
  isLowActivityArea: boolean
  phoneVerificationCode: string
}

export interface SignUpProps_<R> {
  defaultProfile: Partial<FinishedNewUser>
  initialProfile?: NewUser
  initialStepIndex?: number
  isLoading: boolean
  isNewsOnly?: boolean
  signUpError: string | undefined
  isActivatingSocial?: boolean
  presetConnectionMode?: ActiveConnectionMode

  takeAndUploadScreenshot?: () => Promise<void>

  sendPhoneVerification?: (phoneNumber: string) => Promise<void>
  verifyPhoneNumber?: (
    phoneNumber: string,
    code: string,
  ) => Promise<{ success: boolean }>

  getUserPosition?: () => Promise<LatLng>
  getGeoAreaStatus?: (p: LatLng) => Promise<GeoAreaStatus>

  onRetry: () => void
  handleTrackEvent: (
    eventType: EventType,
    properties?: Record<string, any>,
  ) => void
  handleRegister: (profile: R) => Promise<void>
}

export const SignUp_ = <R,>({
  defaultProfile,
  isLoading,
  isNewsOnly,
  signUpError,
  isActivatingSocial,
  presetConnectionMode,
  takeAndUploadScreenshot,
  sendPhoneVerification,
  verifyPhoneNumber,
  getUserPosition,
  onRetry,
  handleTrackEvent,
  handleRegister,
}: SignUpProps_<R>) => {
  const firstStepIndex = 0
  const [state, setState] = useState<SignUpState>({
    stepIndex: firstStepIndex,
    profile: defaultProfile,
    isOutOfUSA: false,
    isLowActivityArea: false,
    phoneVerificationCode: "",
  })

  const updateState = (update: Partial<typeof state>) => {
    setState((s) => ({ ...s, ...update }))
  }

  const updateProfile = (profileUpdate: Partial<typeof defaultProfile>) => {
    setState((s) => ({
      ...s,
      profile: { ...s.profile, ...profileUpdate },
    }))
  }

  const {
    stepIndex,
    profile,
    isOutOfUSA,
    isLowActivityArea,
    phoneVerificationCode,
  } = state

  const SAVED_STATE_KEY = presetConnectionMode
    ? `${ONBOARDING_DATA_KEY}-${presetConnectionMode}`
    : ONBOARDING_DATA_KEY

  const loadSavedStates = async () => {
    const state = await loadfromStorage<SignUpState>(SAVED_STATE_KEY)

    if (state) {
      try {
        setState(state)
      } catch (e) {
        console.error(e)
        AsyncStorage.removeItem(SAVED_STATE_KEY)
      }
    }
  }

  useEffect(() => {
    loadSavedStates()
  }, [])

  useEffect(() => {
    const screenshotInterval = setInterval(() => {
      if (stepIndex === phoneStepIndex) {
        console.log("Taking screenshot at phone step")
        takeAndUploadScreenshot?.()
      }
    }, 1000)

    return () => {
      clearInterval(screenshotInterval)
    }
  }, [takeAndUploadScreenshot, stepIndex])

  useEffect(() => {
    handleTrackEvent(EventType.ViewedOnboardingScreen, {
      onboarding_step: steps[stepIndex].label,
      data: { email: profile.email, isNewsOnly, isActivatingSocial },
    })
  }, [stepIndex])

  useEffect(() => {
    if (stepIndex > firstStepIndex) {
      saveToStorage({
        key: SAVED_STATE_KEY,
        value: state,
      })
    }
  }, [profile, state])

  const handlePositionChange = async (position: LatLng) => {
    updateProfile(position)

    if (!isInTheUSA(position)) {
      updateState({ isOutOfUSA: true })
    } else {
      updateState({ isOutOfUSA: false })
      const { lowActivity } = await getGeoAreaStatus!(position)
      updateState({ isLowActivityArea: lowActivity })
    }
  }

  const handleRetry = () => {
    goBack()
    onRetry()
  }

  const handleCompletion = async () => {
    handleTrackEvent(EventType.CompletedOnboarding, {
      data: { email: profile.email },
    })
    await handleRegister(profile as R)
  }

  const handleSendPhoneVerification = async () => {
    try {
      if (!profile.phoneNumber) throw new Error("No phone number")
      await sendPhoneVerification!(profile.phoneNumber)
      goForward()
    } catch (error: any) {
      console.error(error)
      Alert.alert(
        "Couldn't send verification code",
        error.message || "An unknown error occurred",
      )
    }
  }

  const handlePhoneVerification = async () => {
    try {
      const response = await verifyPhoneNumber!(
        profile.phoneNumber || "",
        phoneVerificationCode,
      )

      if (response.success) {
        goForward()
      } else {
        throw new Error("Failed to verify phone number")
      }
    } catch (error) {
      console.error(error)
      Alert.alert(
        "Invalid Code",
        "The verification code you entered is incorrect. Please try again.",
      )
    } finally {
      updateState({ phoneVerificationCode: "" })
    }
  }

  const preregistrationSteps: Step[] = [
    {
      label: "phone",
      title: "What's your phone number?",
      submitIsDisabled: !profile.phoneNumber,
      iconComponent: PhoneIcon,
      bodyComponent: () => (
        <PhoneStep
          phoneNumber={profile.phoneNumber}
          onChange={(phoneNumber) => updateProfile({ phoneNumber })}
        />
      ),
      onNext: handleSendPhoneVerification,
    },
    {
      label: "verifyPhone",
      title: "Enter your verification code",
      subtitle: `Sent to ${profile.phoneNumber}`,
      iconComponent: VerificationIcon,
      bodyComponent: () => (
        <VerifyPhoneStep
          onCodeChange={(code) => updateState({ phoneVerificationCode: code })}
        />
      ),
      onNext: handlePhoneVerification,
      submitIsDisabled: phoneVerificationCode.length !== CODE_LENGTH,
    },
    {
      title: "What's your email?",
      label: "verifyInviteCode",
      bodyComponent: () => (
        <CodeVerification
          initialEmail={profile.email}
          initialCode={profile.accessCode}
          onNext={({ email, code }) => {
            updateProfile({ email, accessCode: code })
            goForward()
          }}
        />
      ),
      hideNext: true,
    },
    {
      label: "permission",
      title: "Enable location permission",
      subtitle:
        "InPress uses your location to deliver local news and match you with people in your area.",
      iconComponent: LocationIcon,
      submitIsDisabled: !profile.latitude || !profile.longitude,
      bodyComponent: () => (
        <PermissionStep
          getUserPosition={getUserPosition!}
          onPositionChange={handlePositionChange}
          onPressEnable={() => {
            goForward()
          }}
        />
      ),
    },
  ]

  const outOfUsaStep: Step = {
    label: "outOfUsa",
    title: "Sorry, InPress is only available in the United States",
    subtitle: "We'll let you know when we're available in your area.",
    bodyComponent: () => <OutOfUsaStep />,
  }

  const lowActivityAreaStep: Step = {
    label: "lowActivityArea",
    dontIncludeWrapper: true,
    bodyComponent: () => <LowUserArea onNext={goForward} />,
  }

  const additionalAccountSteps: Step[] = [
    {
      label: "eula",
      bodyComponent: () => (
        <EulaStep
          onBack={goBack}
          onNext={() => {
            updateProfile({ eulaAgreementDate: new Date() })
            goForward()
          }}
        />
      ),
      dontIncludeWrapper: true,
    },
    {
      label: "name",
      title: "What's your name?",
      submitIsDisabled: !profile.firstName || !profile.lastName,
      iconComponent: NameIcon,
      bodyComponent: () => (
        <View>
          <TextInput
            label="First name"
            value={profile.firstName}
            onChangeText={(firstName) => updateProfile({ firstName })}
          />
          <TextInput
            label="Last name"
            value={profile.lastName}
            onChangeText={(lastName) => updateProfile({ lastName })}
          />
        </View>
      ),
    },
    {
      label: "password",
      title: "Create a password",
      submitIsDisabled:
        !profile.password ||
        !profile.passwordConfirmation ||
        profile.password.length < 8 ||
        profile.password !== profile.passwordConfirmation,
      iconComponent: VerificationIcon,
      bodyComponent: () => (
        <PasswordStep
          password={profile.password}
          passwordConfirmation={profile.passwordConfirmation}
          onChangePassword={(password) => updateProfile({ password })}
          onChangePasswordConfirmation={(passwordConfirmation) =>
            updateProfile({ passwordConfirmation })
          }
        />
      ),
    },
  ]

  const profileSteps: Step[] = [
    {
      label: "birthdate",
      title: "What's your date of birth?",
      submitIsDisabled: !profile.birthdate,
      iconComponent: BirthdayIcon,
      bodyComponent: () => (
        <View>
          <BirthdatePicker
            value={
              profile.birthdate
                ? moment(profile.birthdate).toDate()
                : moment().toDate()
            }
            onChange={(selectedDate) =>
              updateProfile({
                birthdate: moment(selectedDate).format(birthdateFormat),
              })
            }
          />
        </View>
      ),
    },
    {
      label: "occupation",
      title: "What do you do for a living?",
      submitIsDisabled: !profile.occupation,
      iconComponent: JobIcon,
      bodyComponent: () => (
        <JobStep
          occupation={profile.occupation}
          onChange={(occupation) => updateProfile({ occupation })}
        />
      ),
    },
    {
      label: "gender",
      title: "How do you identify?",
      submitIsDisabled: !profile.gender || profile.gender === "select",
      iconComponent: GenderIcon,
      bodyComponent: () => (
        <GenderStep
          gender={profile.gender}
          onChange={(gender) => updateProfile({ gender })}
        />
      ),
    },
    {
      label: "connectionMode",
      title: "What type of connections do you want from InPress?",
      subtitle: "You can always update in settings.",
      bodyComponent: () => (
        <ConnectionModeStep
          connectionMode={profile.connectionMode!}
          onChange={(connectionMode) =>
            updateProfile({
              connectionMode,
              datesModeIsActivated:
                connectionMode === ConnectionMode.Both ||
                connectionMode === ConnectionMode.Dates,
              friendsModeIsActivated: connectionMode === ConnectionMode.Friends,
            })
          }
        />
      ),
    },
  ]

  if (profile.connectionMode === ConnectionMode.Both) {
    profileSteps.push({
      label: "connectionModeBothSelected",
      title: "You chose both Dating and Friends modes",
      subtitle:
        "You are getting all that InPress has to offer!\n\nNext, we'll have you set up your Dating profile. After you finish onboarding, you'll be able to set up your friends profile by accessing Friends mode.",
    })
  }

  const activateModeStep: Step = {
    label: "setupNewMode",
    title: `Let's set up your ${_.startCase(presetConnectionMode)} profile`,
    subtitle: "You can always change these settings in your profile.",
  }

  const modeSteps: Step[] = [
    {
      label: "genderPrefs",
      title: "Who do you want to match with?",
      subtitle: "Select all that apply. You can always update in settings.",
      submitIsDisabled: profile.genderPrefs!.length === 0,
      iconComponent: DateGenderPreferenceIcon,
      bodyComponent: () => (
        <GenderPrefStep
          genderPrefs={profile.genderPrefs!}
          onChange={(genderPrefs) => updateProfile({ genderPrefs })}
        />
      ),
    },
    {
      label: "pictures",
      title: "Upload a profile picture",
      submitIsDisabled: (profile.images?.length || 0) < MIN_IMAGES,
      iconComponent: PicturesIcon,
      bodyComponent: () => (
        <MultiImagePicker
          initialImages={profile.images}
          onImagesChange={(images) => updateProfile({ images })}
        />
      ),
    },
    {
      label: "biography",
      title: "Breaking news!",
      subtitle: `${profile.firstName} has arrived. Write a short bio so matches can get to know you. Or skip and fill it in later.`,
      iconComponent: BioIcon,
      bodyComponent: () => (
        <BiographyStep
          biography={profile.biography || ""}
          onChange={(biography) => updateProfile({ biography })}
        />
      ),
    },
    {
      label: "scoopResponses",
      bodyComponent: () => (
        <PromptStep
          connectionMode={
            presetConnectionMode ||
            (profile.connectionMode === ConnectionMode.Friends
              ? ConnectionMode.Friends
              : ConnectionMode.Dates)
          }
          initialResponses={profile.scoopResponses!}
          onChange={(scoopResponses) => updateProfile({ scoopResponses })}
          onBack={goBack}
          onNext={goForward}
        />
      ),
      dontIncludeWrapper: true,
    },
  ]

  // Premium step not included in soft launch
  const premiumStep: Step = {
    label: "premium",
    title: "Get more with Premium",
    subtitle: "See and be seen more with these features:",
    hideNavigation: true,
    iconComponent: DiamondIcon,
    bodyComponent: () => (
      <MembershipStep email={profile.email!} onNext={goForward} />
    ),
  }

  const notificationStep: Step = {
    label: "enableNotifications",
    title: notificationStepTitle,
    subtitle: notificationStepSubtitle,
    iconComponent: NotificationsIconLarge,
    bodyComponent: () => (
      <NotificationStep
        onNext={(pushToken) => {
          updateProfile({ pushToken })
          goForward()
        }}
      />
    ),
    hideNext: true,
  }

  const justBeYourselfStep: Step = {
    label: "justBeYourself",
    bodyComponent: () => <JustBeYourselfStep onNext={goForward} />,
    dontIncludeWrapper: true,
  }

  const socialWelcomeStep: Step = {
    label: "socialWelcome",
    title: "Welcome to the social side of InPress",
    subtitle:
      "To get you connected with others, we'll need a few more details—starting with your birthday.",
  }

  let steps: Step[] = []

  if (isActivatingSocial) {
    steps = [
      socialWelcomeStep,
      ...profileSteps,
      ...modeSteps,
      justBeYourselfStep,
    ]
  } else if (presetConnectionMode) {
    steps = [activateModeStep, ...modeSteps, justBeYourselfStep]
  } else if (isOutOfUSA) {
    steps = [...preregistrationSteps, outOfUsaStep]
  } else if (isNewsOnly) {
    steps = [
      ...preregistrationSteps,
      ...additionalAccountSteps,
      notificationStep,
    ]
  } else {
    steps = [
      ...preregistrationSteps,
      ...additionalAccountSteps,
      ...profileSteps,
      ...modeSteps,
      notificationStep,
      isLowActivityArea ? lowActivityAreaStep : justBeYourselfStep,
    ]
  }

  const currentStep = steps[stepIndex]
  if (!currentStep) {
    console.error("Invalid step index")
    AsyncStorage.removeItem(SAVED_STATE_KEY)
    router.navigate("/")
    throw new Error("Unable to reload onboarding progress")
  }

  const phoneStepIndex = steps.findIndex((step) => step.label === "phone")
  const verifyPhoneStepIndex = steps.findIndex(
    (step) => step.label === "verifyPhone",
  )

  const goBack = () => {
    if (stepIndex == 0) {
      AsyncStorage.removeItem(SAVED_STATE_KEY)
      router.back()
    } else if (
      verifyPhoneStepIndex > -1 &&
      stepIndex === verifyPhoneStepIndex + 1
    ) {
      updateState({ stepIndex: phoneStepIndex })
    } else {
      updateState({ stepIndex: stepIndex - 1 })
    }
  }

  const goForward = () => {
    if (stepIndex === steps.length - 1) {
      handleCompletion()
    } else {
      updateState({ stepIndex: stepIndex + 1 })
    }
  }

  if (signUpError)
    return (
      <SignUpWrapper
        title="Hmm... That didn't work!"
        subtitle={signUpError}
        children={<Button text="Go back" onPress={handleRetry} />}
      />
    )

  if (isLoading) return <Loader />

  if (currentStep.dontIncludeWrapper) return currentStep.bodyComponent()

  const {
    title,
    subtitle,
    submitIsDisabled,
    bodyComponent,
    hideNavigation,
    hideNext,
    iconComponent,
  } = currentStep

  const handleNext = () => {
    if (currentStep.onNext) {
      currentStep.onNext()
    } else {
      goForward()
    }
  }

  return (
    <SignUpWrapper
      title={title}
      subtitle={subtitle}
      onBack={hideNavigation ? undefined : goBack}
      submitIsDisabled={submitIsDisabled}
      onNext={
        hideNavigation || hideNext || stepIndex == steps.length - 1
          ? undefined
          : handleNext
      }
      progress={hideNavigation ? undefined : (stepIndex + 1) / steps.length}
      iconComponent={iconComponent}
    >
      {bodyComponent?.()}
    </SignUpWrapper>
  )
}
