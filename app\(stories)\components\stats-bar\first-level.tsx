import StatsBar from "@/components/news/statsBar/StatsBar"
import { Screen } from "@/components/Themed"
import { useLevels } from "@/context/LevelContext"
import { useEffect } from "react"

export default function Story() {
  const { setStats } = useLevels()
  useEffect(() => {
    setStats((prev) => ({
      ...prev!,
      points: 80,
    }))
  }, [])

  return (
    <Screen style={{ justifyContent: "center" }}>
      <StatsBar />
    </Screen>
  )
}
