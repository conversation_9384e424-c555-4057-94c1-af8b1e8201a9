import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { View, Text, StyleSheet } from "react-native"
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
} from "@gorhom/bottom-sheet"
import { Button } from "../Button"
import { BoldText, LargeText } from "../StyledText"
import { EventType, trackEvent } from "@/utils/tracking"
import LottieView from "lottie-react-native"
import { useSharedValue } from "react-native-reanimated"
import { fontStyles } from "@/styles"

interface WelcomePopUpProps {
  onClose: () => void
}

export default function WelcomeDrawer({ onClose }: WelcomePopUpProps) {
  const bottomSheetRef = useRef<BottomSheet>(null)
  const animationRef = useRef<LottieView>(null)
  const sharedValue = useSharedValue(1)

  const [showConfetti, setShowConfetti] = useState(true)

  useEffect(() => {
    animationRef.current?.play()
  }, [])

  useEffect(() => {
    trackEvent(EventType.WelcomeSeen)
  }, [])

  const handleClose = useCallback(() => {
    bottomSheetRef.current?.close()
  }, [])

  const AnimatedView = useMemo(() => {
    return (
      <LottieView
        ref={animationRef}
        source={require("../../assets/animation/welcomeConfetti.json")}
        style={styles.animation}
        resizeMode="cover"
        loop={false}
        onAnimationFinish={() => {
          setShowConfetti(false)
        }}
      />
    )
  }, [])

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={1}
        onPress={onClose}
        animatedIndex={sharedValue}
      />
    ),
    [],
  )

  return (
    <>
      {showConfetti ? AnimatedView : null}
      <BottomSheet
        ref={bottomSheetRef}
        index={0}
        snapPoints={[380]}
        enablePanDownToClose
        onClose={onClose}
        backdropComponent={renderBackdrop}
      >
        <View style={styles.container}>
          <Text style={styles.title}>Welcome to a new way to news!</Text>
          <LargeText style={styles.description}>
            Your daily Newsfeed is a hand-curated mix of factual, accountable
            journalism from outlets you can trust. It's news without the echo
            chambers.
          </LargeText>
          <LargeText style={styles.description}>
            Use the <BoldText>"Rate this article"</BoldText> button when reading
            stories to boost your InScore!
          </LargeText>
          <Button
            style={{ marginTop: 20 }}
            text="Got it, let's go!"
            onPress={handleClose}
          />
        </View>
      </BottomSheet>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center",
  },
  title: {
    fontSize: 40,
    textAlign: "center",
    marginBottom: 10,
    lineHeight: 45,
    ...fontStyles.editorial,
  },
  description: {
    textAlign: "center",
    marginVertical: 10,
  },
  animation: {
    width: "100%",
    height: "100%",
    position: "absolute",
    zIndex: 1,
  },
})
