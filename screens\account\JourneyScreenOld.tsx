import JourneyIconLarge from "@/components/icons/levels/JourneyIconLarge"
import JourneyStep from "@/components/levels/JourneyStep"
import { Screen } from "@/components/Themed"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { screenSubtitleStyle } from "@/components/widgets/styles"
import Colors from "@/constants/Colors"
import { useLevels } from "@/context/LevelContext"
import { ScrollView, StyleSheet, Text } from "react-native"

type JourneyScreenOldProps = {
  points: number
}

export const JourneyScreenOld = ({ points }: JourneyScreenOldProps) => {
  const { levels, loading } = useLevels()

  if (loading || !levels) return null

  const title = "More insights, more interest"
  const subtitle = (
    <Text style={screenSubtitleStyle}>
      <Text style={styles.subtitlePreamble}>
        Higher InScore = Higher quality Leads.
      </Text>
      <Text>{"\n"}</Text>
      <Text>
        Show off your intellectual curiosity and get noticed by people who value
        real substance.
      </Text>
    </Text>
  )

  return (
    <ScrollView style={{ backgroundColor: Colors.light.background }}>
      <Screen style={{ paddingVertical: 20, gap: 8 }}>
        <ScreenHeader
          iconComponent={() => <JourneyIconLarge fill="black" />}
          title={title}
          subtitle={subtitle}
        />
        {levels.map((level, index) => (
          <JourneyStep
            key={level.id}
            level={level}
            points={points}
            previousLevel={levels[index - 1]}
          />
        ))}
      </Screen>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  subtitlePreamble: {
    fontFamily: "InterTight-SemiBold",
  },
})
