import { Text, TouchableOpacity } from "react-native"
import { Stack, router } from "expo-router"
import { Ionicons } from "@expo/vector-icons"

export default function SettingsRoute() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          headerTitle: "General Settings",
          headerTitleAlign: "center", 
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 10 }}>
              <Ionicons name="arrow-back" size={24} color="black" />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="change-password"
        options={{
          headerTitle: "Reset password",
        }}
      />
    </Stack>
  )
}
