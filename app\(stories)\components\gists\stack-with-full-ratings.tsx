import { Screen } from "@/components/Themed"
import GistsStack from "@/components/gists/GistsStack"
import { NEWSFEED_ARTICLES } from "../../(news)/feed/news"
import { Article } from "@/types/news"
import { StoryNewsProvider } from "../../story-components/StoryNewsProvider"
import { SubmitGistRatingProps } from "@/apiQueries/newsFeed"

export default function Story() {
  const initialArticles: Article[] = [
    { ...NEWSFEED_ARTICLES[0], isSurveyed: false },
    { ...NEWSFEED_ARTICLES[1], isSurveyed: true },
    { ...NEWSFEED_ARTICLES[2], isSurveyed: false },
  ]

  const submitGistRating = async ({
    token,
    articleId,
    rating,
  }: SubmitGistRatingProps) => {
    const articleIndex = initialArticles.findIndex(
      (article) => article.id === articleId,
    )
    console.log(`Rating for article ${articleIndex}: ${rating}`)
  }

  return (
    <StoryNewsProvider
      initialArticles={initialArticles}
      submitGistRating={submitGistRating}
    >
      <Screen>
        <GistsStack />
      </Screen>
    </StoryNewsProvider>
  )
}
