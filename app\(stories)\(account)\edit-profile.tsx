import EditProfileScreen from "@/app/(app)/account/edit-profile"
import { SETTINGS, USER } from "./account"
import { promptChoices } from "../(signInOrUp)/prompt-select"
import _ from "lodash"

export const PROFILE_SAVE_HANDLER: () => Promise<void> = () =>
  new Promise((resolve) =>
    setTimeout(() => {
      alert("Saved!")
      resolve()
    }, 1000),
  )

export default function Story() {
  return (
    <EditProfileScreen
      initialUser={USER}
      userSettings={{ ...SETTINGS, autoUpdateLocation: false }}
      promptChoices={_.range(20).map((i) => ({
        ...promptChoices[i % promptChoices.length],
        id: i,
      }))}
      onSave={PROFILE_SAVE_HANDLER}
    />
  )
}
