import { View, Text, StyleSheet } from "react-native"
import { sectionTitleStyle } from "./constants"
import { ScoopResponse } from "@/types/user"

interface ScoopResponseProps {
  scoopResponse: ScoopResponse
}

export const Scoop = ({ scoopResponse }: ScoopResponseProps) => (
  <View>
    <Text style={styles.prompt}>{scoopResponse.prompt}</Text>
    <Text style={styles.response}>{scoopResponse.text}</Text>
  </View>
)

export const styles = StyleSheet.create({
  prompt: sectionTitleStyle,
  response: {
    fontSize: 32,
    fontFamily: "InterTight-SemiBold",
  },
})
