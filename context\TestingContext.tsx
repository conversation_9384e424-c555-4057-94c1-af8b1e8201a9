import React, { createContext, useContext } from "react"
import { StorageKey, usePersistentState } from "@/app/hooks/usePersistentState"
import { useFeatureFlag } from "@/utils/featureFlags"
import { trackEvent } from "@/utils/tracking"
import { OtherEventTypes } from "@/types/other"

export enum Feature {
  Gists = "gists",
}

interface TestingContextProps {
  features: Feature[] | null
  featureIsOn: (feature: Feature) => boolean
  toggleFeature: (feature: Feature) => void
  featuresReady: boolean
}

const TestingContext = createContext<TestingContextProps | undefined>(undefined)

export type TestingProviderProps = {
  children: React.ReactNode
}

export const TestingProvider: React.FC<TestingProviderProps> = ({
  children,
}) => {
  const featuresState = usePersistentState<Feature[]>(StorageKey.Features, [])
  const [features, setFeatures, featuresReady] = featuresState

  const featureIsOn = (feature: Feature) => {
    const state = features.includes(feature)
    const flag = useFeatureFlag(feature)
    return state || flag
  }

  const toggleFeature = (feature: Feature) => {
    trackEvent(OtherEventTypes.FeatureToggled, {
      data: { feature, toggledTo: !featureIsOn(feature) },
    })

    setFeatures((prev) => {
      if (prev.includes(feature)) {
        return prev.filter((f) => f !== feature)
      } else {
        return [...prev, feature]
      }
    })
  }

  return (
    <TestingContext.Provider
      value={{
        features,
        featureIsOn,
        toggleFeature,
        featuresReady,
      }}
    >
      {children}
    </TestingContext.Provider>
  )
}

export const useTestingContext = () => {
  const context = useContext(TestingContext)
  if (!context) {
    throw new Error("useTestingContext must be used within a TestingProvider")
  }
  return context
}
