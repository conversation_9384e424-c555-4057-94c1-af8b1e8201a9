import { But<PERSON> } from "@/components/Button"
import { use<PERSON><PERSON>back, useEffect, useRef } from "react"
import MultiImagePicker, {
  LocalOrRemoteImage,
} from "@/components/MultiImagePicker"
import { TextInput } from "@/components/TextInput"
import { Screen, Text } from "@/components/Themed"
import { useState } from "react"
import { View, ScrollView, StyleSheet, Modal } from "react-native"
import { textInputProps } from "@/components/signInOrUp/BiographyStep"
import { SetLocationSection } from "@/screens/account/SetLocationSection"
import PromptStep_, {
  UsageContext,
} from "@/components/signInOrUp/promptStep/PromptStep_"
import { Prompt } from "@/components/signInOrUp/SignUp"
import { Responses } from "@/components/signInOrUp/promptStep/Responses"
import { CheckboxRow } from "@/components/CheckboxRow"
import { genderOptions } from "@/components/signInOrUp/GenderStep"
import { MIN_IMAGES, MIN_PROMPTS } from "@/components/signInOrUp/constants"
import { useNavigation } from "@react-navigation/native"
import { router, useFocusEffect } from "expo-router"
import { indexScreenOptions, profileScreenOptions } from "./_layout"
import { TouchableOpacity } from "react-native-gesture-handler"
import { disabledOpacity } from "@/components/constants"
import {
  isUserWithProfile,
  UnknownTypeUserWithPrivateData,
  UserWithPrivateData,
} from "@/types/user"
import { Avatar } from "@/components/widgets/Avatar"
import { pickImage } from "@/utils/images"
import { UserSettings } from "@/apiQueries/userSettings"

export interface SaveProps {
  user: UnknownTypeUserWithPrivateData
  images: LocalOrRemoteImage[]
}

export interface EditProfileScreenProps {
  initialUser: UnknownTypeUserWithPrivateData
  userSettings: UserSettings
  promptChoices: Prompt[]
  onSave: (props: SaveProps) => Promise<void>
}

const EditProfileScreen = ({
  initialUser,
  userSettings,
  promptChoices,
  onSave,
}: EditProfileScreenProps) => {
  const [user, setUser] = useState(initialUser)
  const [images, setImages] = useState<LocalOrRemoteImage[]>([])
  const [isEditingPrompts, setIsEditingPrompts] = useState<boolean>()
  const scoopsRef = useRef<View>(null)
  const scrollRef = useRef<ScrollView>(null)
  const [scrollEnabled, setScrollEnabled] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const navigation = useNavigation()

  const saveDisabled =
    isSaving ||
    user.firstName.length === 0 ||
    user.lastName.length === 0 ||
    (isUserWithProfile(user) &&
      ((images.length < MIN_IMAGES &&
        images.length < initialUser.images.length) ||
        user.occupation.length === 0 ||
        user.scoopResponses.length < MIN_PROMPTS ||
        !user.gender))

  const handleCameraPress = async () => {
    const uri = await pickImage()
    if (uri) {
      setImages([{ uri }])
    }
  }

  useFocusEffect(
    useCallback(() => {
      navigation.setOptions({
        ...profileScreenOptions,
        headerLeft: () => (
          <TouchableOpacity
            onPress={() => {
              router.back()
            }}
            style={{ marginLeft: 10 }}
          >
            <Text style={styles.navText}>Cancel</Text>
          </TouchableOpacity>
        ),
        headerRight: () => (
          <TouchableOpacity
            disabled={saveDisabled}
            onPress={() => {
              setIsSaving(true)
              onSave({ user, images }).finally(() => {
                setIsSaving(false)
              })
            }}
          >
            <Text
              style={[
                styles.navText,
                { opacity: saveDisabled ? disabledOpacity : 1 },
              ]}
            >
              Done
            </Text>
          </TouchableOpacity>
        ),
      })
      return () => {
        navigation.setOptions(indexScreenOptions)
      }
    }, [navigation, user, images, isSaving]),
  )

  useEffect(() => {
    if (
      scrollRef?.current &&
      scoopsRef?.current &&
      isEditingPrompts === false
    ) {
      scoopsRef.current.measure((x, y, width, height, pageX, pageY) => {
        scrollRef.current?.scrollTo({ y: pageY, animated: false })
      })
    }
  }, [scrollRef, scoopsRef, isEditingPrompts])

  if (isEditingPrompts && isUserWithProfile(user)) {
    return (
      <Modal visible={isEditingPrompts}>
        <PromptStep_
          initialResponses={user.scoopResponses}
          promptChoices={promptChoices}
          usageContext={UsageContext.EditProfile}
          onChange={(scoopResponses) => setUser({ ...user, scoopResponses })}
          onBack={() => setIsEditingPrompts(false)}
        />
      </Modal>
    )
  }

  const renderLocationSection = () => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>Location</Text>
      <Text style={styles.inputSubtext}>
        Click on the map to update your location
      </Text>
      <SetLocationSection
        defaultLocation={{
          latitude: user.latitude,
          longitude: user.longitude,
        }}
        onSaveLocation={async (location) => {
          setUser({
            ...user,
            latitude: location.latitude,
            longitude: location.longitude,
          })
        }}
      />
    </View>
  )

  const renderProfileFields = (user: UserWithPrivateData) => (
    <>
      <View style={styles.inputContainer}>
        <TextInput
          {...textInputProps}
          value={user.biography}
          onChangeText={(biography) => setUser({ ...user, biography })}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>My photos</Text>
        <MultiImagePicker
          initialImages={user.images}
          onImagesChange={(images) => setImages(images)}
          onDragChanged={(isDragging) => setScrollEnabled(!isDragging)}
        />
      </View>

      <View style={styles.inputContainer}>
        <TextInput
          defaultValue={user?.occupation}
          label="Occupation"
          onChangeText={(occupation) => setUser({ ...user, occupation })}
        />
      </View>
      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, styles.genderInputLabel]}>Gender</Text>
        <View style={styles.checkboxContainer}>
          <CheckboxRow
            options={genderOptions}
            initialValues={[user.gender]}
            maxChecked={1}
            onChange={(newGender) => setUser({ ...user, gender: newGender[0] })}
          />
        </View>
      </View>

      <View style={styles.inputContainer} ref={scoopsRef}>
        <Text style={styles.inputLabel}>Scoops</Text>
        <Responses responses={user.scoopResponses} />
        <Button
          style={styles.button}
          text="Edit scoops"
          onPress={() => setIsEditingPrompts(true)}
        />
      </View>
    </>
  )

  return (
    <Screen>
      <ScrollView scrollEnabled={scrollEnabled} ref={scrollRef}>
        <View style={styles.container}>
          {user.isNewsOnly ? (
            <View style={styles.profilePicContainer}>
              <Avatar
                user={user}
                image={images[0]}
                size={160}
                isEditable
                handleCameraPress={handleCameraPress}
              />
            </View>
          ) : null}
          <TextInput
            label="First name"
            value={user.firstName}
            onChangeText={(firstName) => setUser({ ...user, firstName })}
          />
          <TextInput
            label="Last name"
            value={user.lastName}
            onChangeText={(lastName) => setUser({ ...user, lastName })}
            subtext="Only your first name will be shown"
          />
          {isUserWithProfile(user) && renderProfileFields(user)}
          {!userSettings.autoUpdateLocation && renderLocationSection()}
        </View>
      </ScrollView>
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 30,
  },
  inputContainer: {
    marginTop: 30,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 10,
  },
  inputSubtext: {
    fontSize: 12,
    color: "gray",
    marginBottom: 10,
  },
  genderInputLabel: {
    fontSize: 16,
    marginBottom: -10,
  },
  button: {
    marginTop: 20,
    width: 300,
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  navText: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 16,
  },
  profilePicContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
})

export default EditProfileScreen
