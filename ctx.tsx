import { StreamChat, Default<PERSON><PERSON><PERSON> } from "stream-chat"
import React, { useState } from "react"
import { useStorageState } from "./useStorageState"
import { setSession as setGlobalSession } from "./context/session"
import _ from "lodash"
import * as Sentry from "@sentry/react-native"
import { usePostHog } from "posthog-react-native"
import { FinishedNewAccount } from "./components/signInOrUp/SignUp"
import {
  FinishedNewUserWithImageIds,
  register,
  registerWithoutProfile,
} from "./apiQueries/registerUser"
import { Session } from "./types/user"
import { getSession, impersonate, login } from "./apiQueries/auth"

export interface ImpersonateProps {
  email: string
}

export type SignInProps = {
  email: string
  password: string
}

export interface AuthContextProps {
  signIn: ({ email, password }: SignInProps) => Promise<Session>
  impersonate: ({ email }: ImpersonateProps) => Promise<void>
  signOut: () => Promise<void>
  register: (profile: FinishedNewUserWithImageIds) => Promise<Session>
  registerWithoutProfile: (account: FinishedNewAccount) => Promise<Session>
  refreshSession: (token: string) => Promise<void>
  overwriteSession: (session: Session) => void
  session?: Session | null
  isLoading: boolean
}

const AuthContext = React.createContext<AuthContextProps>({
  signIn: async () => ({} as Session),
  signOut: async () => {},
  impersonate: async () => {},
  register: async () => ({} as Session),
  registerWithoutProfile: async () => ({} as Session),
  refreshSession: async () => {},
  overwriteSession: async () => {},
  session: null,
  isLoading: false,
})

// This hook can be used to access the user info.
export function useSession() {
  const value = React.useContext(AuthContext)
  if (process.env.NODE_ENV !== "production") {
    if (!value) {
      throw new Error("useSession must be wrapped in a <SessionProvider />")
    }
  }

  return value
}

export function SessionProvider(props: React.PropsWithChildren) {
  const [[isLoading, sessionJson], setSession] = useStorageState("session")
  const [client] = useState<StreamChat<DefaultGenerics> | null>(null)

  const posthog = usePostHog()

  let session: Session | null = null
  if (sessionJson) {
    console.log("Loading session from storage")
    try {
      session = JSON.parse(sessionJson)

      if (
        !_.has(session, "version") ||
        session.version !== process.env.EXPO_PUBLIC_SESSION_VERSION
      ) {
        session = null
      }

      setGlobalSession(session)
    } catch (e) {
      console.error("Failed to parse session", e)
      updateSession(null)
    }
  }

  function updateSession(newSession: Session | null) {
    setSession(JSON.stringify(newSession))
    setGlobalSession(newSession)

    if (newSession) {
      Sentry.setUser({
        id: newSession.user.id,
        first_name: newSession.user.firstName,
      })
    } else {
      Sentry.setUser(null)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        signIn: async ({ email, password }) => {
          const session = await login({ email, password })
          posthog.identify(session.user.id.toString())
          updateSession(session)
          return session
        },
        impersonate: async ({ email }) => {
          const session = await impersonate({ email })
          updateSession(session)
        },
        signOut: async () => {
          client?.disconnectUser()
          posthog.reset()
          updateSession(null)
        },
        register: async (profile) => {
          const session = await register(profile)
          posthog.identify(session.user.id.toString())
          updateSession(session)
          return session
        },
        registerWithoutProfile: async (account: FinishedNewAccount) => {
          const session = await registerWithoutProfile(account)
          posthog.identify(session.user.id.toString())
          updateSession(session)
          return session
        },
        refreshSession: async (token) => {
          const newSession = await getSession(token)
          if (!_.isEqual(newSession, session)) {
            if (newSession) posthog.identify(newSession.user.id.toString())
            updateSession(newSession)
          }
        },
        overwriteSession: (session: Session) => {
          posthog.identify(session.user.id.toString())
          updateSession(session)
        },
        session,
        isLoading: isLoading,
      }}
    >
      {props.children}
    </AuthContext.Provider>
  )
}
