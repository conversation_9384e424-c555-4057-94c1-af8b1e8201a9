import { GenericNotificationItem } from "./GenericNotificationItem"
import { Image } from "expo-image"
import { styles } from "../notificationStyles"
import { LevelUpNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { shareLevelUp } from "@/utils/sharing"
import { useLevels } from "@/context/LevelContext"

export function LevelUpNotificationItem({
  item,
}: {
  item: LevelUpNotification
}) {
  const { loading, levels } = useLevels()

  if (loading || !levels) {
    return null
  }

  const handleShare = async () => {
    const level = levels.find((l) => l.id === item.level.id)!
    await shareLevelUp(level)
  }

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={
        <Image
          source={item.level.badgeUrl}
          contentFit="contain"
          contentPosition={"center"}
          style={styles.imageWithoutBorderRadius}
        />
      }
      TextComponent={
        <NormalText>
          Congrats! You've unlocked{` `}
          <BoldText>{item.level.name}</BoldText>!{` `}
          {item.nextLevel
            ? `Keep rating articles to reach ${item.nextLevel.name}.`
            : `You've reached the highest level!`}
        </NormalText>
      }
      primaryButton={{
        text: "Share",
        onPress: handleShare,
      }}
    />
  )
}
