import { StyleSheet, Text, ScrollView, View } from "react-native"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import { Button } from "../Button"
import { useState } from "react"
import { eula } from "./eula"
import { SignUpWrapper } from "./SignUpWrapper"
import { fontStyles } from "@/styles"

interface EulaStepProps {
  onBack: () => void
  onNext: () => void
}

export const EulaStep = ({ onBack, onNext }: EulaStepProps) => {
  const [hasScrolledToEnd, setHasScrolledToEnd] = useState(false)

  const handleScroll = (event: any) => {
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent
    const isScrolledToEnd =
      contentOffset.y + layoutMeasurement.height >= contentSize.height - 50

    if (isScrolledToEnd) setHasScrolledToEnd(true)
  }

  return (
    <SignUpWrapper>
      <View style={styles.container}>
        <Text style={styles.title}>End-User License Agreement</Text>
        <ScrollView
          style={styles.scrollView}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          <Text style={styles.eulaText}>{eula}</Text>
        </ScrollView>
        <Button
          text="I Agree"
          style={styles.button}
          onPress={onNext}
          disabled={!hasScrolledToEnd}
        />
        <Text style={styles.pleaseReadText}>
          Please read the entire EULA before agreeing
        </Text>
        <Text style={styles.backText} onPress={onBack}>
          Go back
        </Text>
      </View>
    </SignUpWrapper>
  )
}

export const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    marginBottom: 16,
    ...fontStyles.editorial,
  },
  scrollView: {
    height: hp("50%"),
    marginBottom: 16,
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
  },
  eulaText: {
    fontSize: 16,
  },
  button: {
    alignSelf: "flex-end",
  },
  pleaseReadText: {
    marginTop: 16,
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.5)",
    ...fontStyles.editorial,
  },
  backText: {
    marginTop: 16,
    fontSize: 16,
    textDecorationLine: "underline",
    textAlign: "center",
  },
})
