import { ChatProvider } from "@/chatContext"
import { LevelsProvider } from "./LevelContext"
import { NewsProvider } from "./NewsContext"
import { SocialProvider } from "./SocialContext"

export const AppProviders = ({ children }: { children: React.ReactNode }) => (
  <ChatProvider>
    <LevelsProvider>
      <NewsProvider>
        <SocialProvider>{children}</SocialProvider>
      </NewsProvider>
    </LevelsProvider>
  </ChatProvider>
)
