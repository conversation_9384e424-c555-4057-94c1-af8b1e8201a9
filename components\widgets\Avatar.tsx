import {
  View,
  Text,
  ImageStyle,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from "react-native"
import { TimedImage } from "@/components/TrackedImage"
import { BROWNSTONE } from "@/constants/Colors"
import { UnknownTypeUser, UserPreview } from "@/types/user"
import Feather from "@expo/vector-icons/Feather"
import { LocalOrRemoteImage } from "../MultiImagePicker"

interface AvatarProps {
  user: UserPreview | UnknownTypeUser
  image?: LocalOrRemoteImage
  size?: number
  isEditable?: boolean
  handleCameraPress?: () => void
}

export const Avatar = ({
  user,
  image,
  size = 160,
  isEditable = false,
  handleCameraPress,
}: AvatarProps) => {
  type ContainerStyle = ImageStyle & ViewStyle

  const containerStyle: ContainerStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
    backgroundColor: BROWNSTONE,
    justifyContent: "center",
    alignItems: "center",
  }

  const initialFontSize = size / 2

  const isPreview = (
    user: UserPreview | UnknownTypeUser,
  ): user is UserPreview => {
    return !("lastName" in user)
  }

  let imageUrl: string | undefined

  if (image) {
    imageUrl = image.uri || image.url
  } else if (isPreview(user)) {
    imageUrl = user.image?.url
  } else {
    imageUrl = user.images?.[0]?.url
  }

  const getInitial = (): string => {
    return user.firstName?.slice(0, 1) || ""
  }

  const renderCameraButton = () => {
    if (!isEditable) return null

    return (
      <TouchableOpacity
        style={[
          styles.cameraButton,
          {
            top: 0,
            right: 0,
          },
        ]}
        onPress={handleCameraPress}
      >
        <Feather name="camera" size={20} color="black" />
      </TouchableOpacity>
    )
  }

  return (
    <>
      {imageUrl ? (
        <TimedImage
          name="avatar-image"
          source={{ uri: imageUrl }}
          style={containerStyle}
        />
      ) : (
        <View style={containerStyle}>
          <Text style={[styles.initialText, { fontSize: initialFontSize }]}>
            {getInitial()}
          </Text>
        </View>
      )}
      {renderCameraButton()}
    </>
  )
}

const styles = StyleSheet.create({
  initialText: {
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  cameraButton: {
    backgroundColor: "white",
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
  },
})
