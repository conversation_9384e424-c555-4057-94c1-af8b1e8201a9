import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { StyleSheet } from "react-native"
import { fontStyles } from "@/styles"

export const completionStepStyles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: 10,
  },
  title: {
    lineHeight: 41 + 5,
    fontSize: 41,
    marginBottom: 10,
    ...fontStyles.editorial,
  },
  button: {
    height: 56,
    borderRadius: 32,
    justifyContent: "center",
    backgroundColor: "black",
    width: wp(85),
  },
  buttonContent: {
    flexDirection: "row-reverse",
  },
  buttonText: {
    fontFamily: "InterTight-SemiBold",
    color: "white",
    fontSize: 16,
  },
})
