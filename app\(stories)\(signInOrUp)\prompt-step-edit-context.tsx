import { promptChoices } from "./prompt-select"
import _ from "lodash"
import PromptStep_, {
  UsageContext,
} from "@/components/signInOrUp/promptStep/PromptStep_"
import { router } from "expo-router"

export default function Story() {
  return (
    <PromptStep_
      initialResponses={[]}
      promptChoices={_.range(20).map((i) => ({
        ...promptChoices[i % promptChoices.length],
        id: i,
      }))}
      usageContext={UsageContext.EditProfile}
      onBack={() => {
        router.back()
      }}
      onNext={() => {
        console.log("Next")
      }}
      onChange={(prompts) => {
        console.log(prompts)
      }}
    />
  )
}
