import React, { useEffect, useState } from "react"
import { StyleSheet, View } from "react-native"

import { heightPercentageToDP as hp } from "react-native-responsive-screen"

import { GistsStack_ } from "../gists/GistsStack"
import { NormalText } from "../StyledText"
import { Article } from "@/types/news"

type Props = {
  articles: Article[]
  onComplete: () => void
}

const GistsStep: React.FC<Props> = ({ articles, onComplete }) => {
  type Step = "right" | "left" | "done"
  const [step, setStep] = useState<Step>("right")

  useEffect(() => {
    if (step === "done") {
      onComplete()
    }
  }, [step])

  return (
    <View style={styles.container}>
      <View style={styles.gistsView}>
        <GistsStack_
          articles={articles}
          demoMode
          swiperProps={{
            disableLeftSwipe: step !== "left",
            disableRightSwipe: step !== "right",
          }}
          onSwipe={async ({ rating }) => {
            if (rating === "like") {
              setStep("left")
            } else if (rating === "dislike") {
              setStep("done")
            }
          }}
        />
      </View>
      <NormalText style={styles.bottomText}>
        {step === "right" && "Swipe right"}
        {step === "left" && "Swipe left"}
      </NormalText>
    </View>
  )
}

export default GistsStep

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gistsView: {
    marginTop: hp(2),
    flex: 1,
  },
  bottomText: {
    textAlign: "center",
  },
})
