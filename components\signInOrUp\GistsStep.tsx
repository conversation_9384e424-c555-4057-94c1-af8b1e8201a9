import React, { useEffect, useState } from "react"
import { StyleSheet, View } from "react-native"

import { heightPercentageToDP as hp } from "react-native-responsive-screen"

import { GistsStack_ } from "../gists/GistsStack"
import { NormalText } from "../StyledText"
import { BEIGE } from "@/constants/Colors"
import { S3_URL } from "@/constants/Links"
import { Article } from "@/types/news"
import { NewsProvider } from "@/context/NewsContext"
import _ from "lodash"
import { LevelsProvider, LevelsProviderProps } from "@/context/LevelContext"
import { Level } from "@/types/levels"

export const TITLE = "You get the Gist"
export const SUBTITLE =
  "Gists help you get straight to the point while earning points to track how much you learn."

export const LEVELS: Level[] = [
  {
    id: 2,
    name: "Learner",
    place: 2,
    color: "#A8FFC0",
    pointsRequired: 100,
    description:
      "You’re unlocking 20 daily Leads! Keep growing sharper and aim for <PERSON>!",
    levelUpTitle: "Nice, <PERSON><PERSON>!",
    levelUpSubtitle: "Nice job, keep it up! Go go go!",
    badgeUrl: `${S3_URL}/levels/learner-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/learner-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/learner-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-learner-on-inpress.png`,
  },
  {
    id: 3,
    name: "Explorer",
    place: 3,
    color: "#E0E6C3",
    pointsRequired: 250,
    description:
      "You’re on the Explorer journey! Keep climbing for deeper Lead insights!",
    levelUpTitle: "You’re an Explorer!",
    levelUpSubtitle: "You’re doing great! Keep it up!",
    badgeUrl: `${S3_URL}/levels/explorer-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/explorer-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/explorer-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-explorer-on-inpress.png`,
  },
]

const demoLevelData: LevelsProviderProps["initialData"] = {
  levels: LEVELS,
  streaks: [],
  stats: {
    points: 220,
    articlesRead: 82,
    articlesRated: 38,
    streakDays: 4,
  },
}

const articles: Article[] = [
  {
    id: 0,
    title: "Mediterranean Diet Boosts Heart Health For Kids Too, Study Reveals",
    source: "Study Finds",
    publishedAt: "",
    url: "",
    imageUrl: "",
    localImage: require("@/assets/images/onboarding/salad-article.jpg"),
    frontpageSection: "health",
    faviconUrl: `${S3_URL}/static_assets/study-finds-favicon.png`,
    summaryPoints: [
      "Helps kids maintain healthier cholesterol and blood sugar levels.",
      "Supports long-term heart health starting from a young age.",
    ],
    isOpened: false,
    isSurveyed: false,
    gistRating: null,
    position: null,
  },
  {
    id: 1,
    title: "Model Suggests Voter Turnout Can Predict Election Victory Margins",
    source: "Phys.org",
    publishedAt: "",
    url: "",
    imageUrl: "",
    localImage: require("@/assets/images/onboarding/voting-article.jpg"),
    frontpageSection: "politics",
    faviconUrl: `${S3_URL}/static_assets/phys-favicon.png`,
    summaryPoints: [
      "Holds at municipal to national levels, across countries.",
      "Empirical data covers hundreds of elections over 7 decades.",
    ],
    isOpened: false,
    isSurveyed: false,
    gistRating: null,
    position: null,
  },
]

type Props = {
  onComplete: () => void
}

const GistsStep = ({ onComplete }: Props) => {
  type Step = "right" | "left" | "done"
  const [step, setStep] = useState<Step>("right")

  useEffect(() => {
    if (step === "done") {
      onComplete()
    }
  }, [step])

  return (
    <LevelsProvider initialData={demoLevelData}>
      <NewsProvider initialArticles={[]}>
        <View style={styles.container}>
          <View style={styles.gistsView}>
            <GistsStack_
              articles={articles}
              demoMode
              swiperProps={{
                disableLeftSwipe: step !== "left",
                disableRightSwipe: step !== "right",
              }}
              onSwipe={async ({ rating }) => {
                if (rating === "like") {
                  setStep("left")
                } else if (rating === "dislike") {
                  setStep("done")
                }
              }}
            />
          </View>
          <NormalText style={styles.bottomText}>
            {step === "right" && "Swipe right"}
            {step === "left" && "Swipe left"}
          </NormalText>
        </View>
      </NewsProvider>
    </LevelsProvider>
  )
}

export default GistsStep

export const BOTTOM_TEXT_HEIGHT = 30

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gistsView: {
    marginTop: hp(2),
    flex: 1,
  },
  bottomText: {
    height: BOTTOM_TEXT_HEIGHT,
    backgroundColor: BEIGE,
    paddingTop: 10,
    textAlign: "center",
  },
})
