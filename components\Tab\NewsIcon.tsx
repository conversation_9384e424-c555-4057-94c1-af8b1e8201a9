import * as React from "react"
import Svg, { Svg<PERSON><PERSON>, Path } from "react-native-svg"
const NewsIcon = (props: SvgProps) => (
  <Svg width={21} height={20} fill="none" {...props}>
    <Path
      fill="#000"
      d="M17.5 20h-14a3 3 0 0 1-3-3V1a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v12h4v4a3 3 0 0 1-3 3Zm-1-5v2a1 1 0 1 0 2 0v-2h-2Zm-2 3V2h-12v15a1 1 0 0 0 1 1h11ZM4.5 5h8v2h-8V5Zm0 4h8v2h-8V9Zm0 4h5v2h-5v-2Z"
    />
  </Svg>
)
export default NewsIcon

export const NewsSelectedIcon = (props: SvgProps) => (
  <Svg width={21} height={20} fill="none" {...props}>
    <Path
      fill="#000"
      d="M17.5 20h-14a3 3 0 0 1-3-3V1a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v7h4v9a3 3 0 0 1-3 3Zm-1-10v7a1 1 0 1 0 2 0v-7h-2Zm-13-6v6h6V4h-6Zm0 7v2h10v-2h-10Zm0 3v2h10v-2h-10Zm2-8h2v2h-2V6Z"
    />
  </Svg>
)
