import { ScrollView, StyleSheet, View } from "react-native"
import { ARTICLE_CARD_HEIGHT, ArticleCard } from "./ArticleCard"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import _ from "lodash"
import { useFeatureFlag } from "@/utils/featureFlags"
import { newsfeedMarginHorizontal } from "./constants"
import { Article } from "@/types/news"

const MARGIN = 5
const ARTICLE_GAP = 10
const SHADOW_BUFFER = 2

export const LIST_LAYOUT_HEIGHT_FN = (isNewReadRated: boolean) => {
  const articlesPerColumn = isNewReadRated ? 2 : 4
  return (
    ARTICLE_CARD_HEIGHT * articlesPerColumn +
    ARTICLE_GAP * (articlesPerColumn - 1) +
    (isNewReadRated ? SHADOW_BUFFER : 0)
  )
}

export const ArticleListLayout = ({ articles }: { articles: Article[] }) => {
  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")
  const moreNewsLayoutsFlag = useFeatureFlag("more_news_layouts")

  if (moreNewsLayoutsFlag) {
    const pageWidth = wp(88) + MARGIN * 2

    return (
      <ScrollView
        contentContainerStyle={{
          height: LIST_LAYOUT_HEIGHT_FN(newReadRatedFlag),
          paddingHorizontal: MARGIN,
        }}
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={pageWidth}
        snapToAlignment="start"
        decelerationRate="fast"
      >
        {_.chunk(articles, 2).map((articlePair, pageIndex) => (
          <View
            key={pageIndex}
            style={[styles.container, { width: pageWidth - MARGIN * 2 }]}
          >
            {articlePair.map((article) => (
              <View key={article.id} style={styles.articleContainer}>
                <ArticleCard article={article} />
              </View>
            ))}
          </View>
        ))}
      </ScrollView>
    )
  } else {
    return (
      <View
        style={{
          height: LIST_LAYOUT_HEIGHT_FN(newReadRatedFlag),
          marginHorizontal: newsfeedMarginHorizontal,
          gap: ARTICLE_GAP,
        }}
      >
        {articles.slice(0, 4).map((article, i) => (
          <View key={article.id}>
            <ArticleCard article={article} />
          </View>
        ))}
      </View>
    )
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginHorizontal: MARGIN,
    gap: ARTICLE_GAP,
  },
  articleContainer: {
    width: "100%",
  },
})

export { MARGIN }
