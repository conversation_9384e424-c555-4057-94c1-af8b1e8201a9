import { GenericNotificationItem } from "./GenericNotificationItem"
import {
  HitDayOrWeekStreakNotification,
  HitLongTermStreakNotification,
  HitMonthStreakNotification,
  NotificationType,
} from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { InPressLogo } from "../InPressLogo"
import { useLevels } from "@/context/LevelContext"
import { shareFile } from "@/utils/sharing"
import { EventType } from "@/utils/tracking"

export function HitStreakNotificationItem({
  item,
}: {
  item:
    | HitDayOrWeekStreakNotification
    | HitMonthStreakNotification
    | HitLongTermStreakNotification
}) {
  const { loading, streaks } = useLevels()

  if (loading || !streaks) {
    return null
  }

  const streak = streaks.find((s) => s.type === item.streakType)!

  const handleShare = async () => {
    await shareFile({
      url: streak.shareableUrl,
      fallbackMessage: `I just hit a ${streak.title} on InPress! www.inpress.app`,
      eventType: EventType.StreakSharePressed,
      trackingData: { streakType: streak.type },
    })
  }

  let textContent

  if (item.type === NotificationType.HitDayOrWeekStreak) {
    textContent = (
      <NormalText>
        Congrats on your <BoldText>{streak.title}</BoldText> streak! You earned{" "}
        {item.pointsEarned} points. Keep it going!
      </NormalText>
    )
  } else if (item.type === NotificationType.HitMonthStreak) {
    textContent = (
      <NormalText>
        Amazing! You've reached a 1-month streak and earned 300 points. Keep it
        alive for bigger rewards!
      </NormalText>
    )
  } else if (item.type === NotificationType.HitLongTermStreak) {
    textContent = (
      <NormalText>
        Wow! Your <BoldText>{streak!.title}</BoldText> streak just earned you{" "}
        {item.pointsEarned} points. You're on fire!
      </NormalText>
    )
  } else {
    console.error("Invalid streak type")
    textContent = <NormalText>You hit a streak!</NormalText>
  }

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={textContent}
      primaryButton={{
        text: "Share Streak",
        onPress: handleShare,
      }}
    />
  )
}
