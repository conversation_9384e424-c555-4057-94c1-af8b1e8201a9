import React, { useState } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from "react-native"

// Story categories and their screens
const storyCategories = [
  {
    title: "Components",
    stories: [
      { name: "Buttons", path: "buttons" },
      { name: "Gist<PERSON><PERSON>", path: "gist-card" },
      {name: "GistsStack", path: "gists-stack"},
      {name: "GistStack-web", path: "gists-stack-web"},
      { name: "Text Inputs", path: "text-inputs" },
      { name: "Cards", path: "cards" },
    ]
  },
  {
    title: "Screens",
    stories: [
      { name: "News Feed", path: "news-feed" },
      { name: "Profile", path: "profile" },
      { name: "Cha<PERSON>", path: "chat" },
    ]
  },
  {
    title: "Utils",
    stories: [
      { name: "API Queries", path: "api-queries" },
      { name: "Constants", path: "constants" },
    ]
  }
]

interface StoriesAppProps {}

const StoriesApp: React.FC<StoriesAppProps> = () => {
  const [currentStory, setCurrentStory] = useState<string | null>(null)

  const renderStoryMenu = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>InPress Web Stories</Text>
        <Text style={styles.subtitle}>Development & Testing Interface</Text>
      </View>
      
      <ScrollView style={styles.menuContainer}>
        {storyCategories.map((category, categoryIndex) => (
          <View key={categoryIndex} style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>{category.title}</Text>
            {category.stories.map((story, storyIndex) => (
              <TouchableOpacity
                key={storyIndex}
                style={styles.storyItem}
                onPress={() => setCurrentStory(story.path)}
              >
                <Text style={styles.storyText}>{story.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </ScrollView>
    </View>
  )

  const renderCurrentStory = () => {
    if (!currentStory) return null

    return (
      <View style={styles.container}>
        <View style={styles.storyHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setCurrentStory(null)}
          >
            <Text style={styles.backButtonText}>← Back to Menu</Text>
          </TouchableOpacity>
          <Text style={styles.storyTitle}>
            {storyCategories
              .flatMap(cat => cat.stories)
              .find(story => story.path === currentStory)?.name || currentStory}
          </Text>
        </View>
        
        <View style={styles.storyContent}>
          <StoryRenderer storyPath={currentStory} />
        </View>
      </View>
    )
  }

  return currentStory ? renderCurrentStory() : renderStoryMenu()
}
const StoryRenderer: React.FC<{ storyPath: string }> = ({ storyPath }) => {
  try {
    switch (storyPath) {
      case "buttons":
        const ButtonStory = require("./components/ButtonStory").default
        return <ButtonStory />

      case "gist-card":
        const GistCardStory = require("./components/GistCardStory").default
        return <GistCardStory />

      case "gists-stack":
        const GistsStackStory = require("./components/GistsStackStory").default
        return <GistsStackStory />
      
      case "gists-stack-web":
        const GistsStackWebStory = require("./components/GistsStackWebStory").default
        return <GistsStackWebStory />

      case "text-inputs":
      case "cards":
      case "news-feed":
      case "profile":
      case "chat":
      case "api-queries":
      case "constants":
      default:
        return (
          <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>Story: {storyPath}</Text>
            <Text style={styles.placeholderSubtext}>
              This story component will be implemented soon.
            </Text>
          </View>
        )
    }
  } catch (error) {
    return (
      <View style={styles.placeholderContainer}>
        <Text style={styles.placeholderText}>Error loading story</Text>
        <Text style={styles.placeholderSubtext}>
          {error instanceof Error ? error.message : "Unknown error"}
        </Text>
      </View>
    )
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    alignItems: "center",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
  menuContainer: {
    flex: 1,
    padding: 20,
  },
  categoryContainer: {
    marginBottom: 30,
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    paddingBottom: 10,
  },
  storyItem: {
    padding: 12,
    marginVertical: 4,
    backgroundColor: "#f8f9fa",
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: "#007AFF",
  },
  storyText: {
    fontSize: 16,
    color: "#333",
  },
  storyHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    marginRight: 15,
    padding: 8,
    backgroundColor: "#007AFF",
    borderRadius: 6,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "500",
  },
  storyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  storyContent: {
    flex: 1,
    padding: 20,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 40,
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 10,
  },
  placeholderSubtext: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
})

export default StoriesApp
