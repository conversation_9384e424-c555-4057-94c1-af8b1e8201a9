import { Screen } from "@/components/Themed"
import { articles } from "../feed/news"
import MiniArticleCard from "@/components/news/MiniArticleCard"
import { Text } from "react-native"
import { Article } from "@/types/news"

export default function Story() {
  const article: Article = {
    ...articles[0],
    gistRating: "like",
  }

  return (
    <Screen style={{ alignItems: "center", gap: 8 }}>
      <Text>Gist Rated</Text>
      <MiniArticleCard article={{ ...article, gistRating: "like" }} />
      <Text>Gist Rated and Opened</Text>
      <MiniArticleCard
        article={{ ...article, gistRating: "like", isOpened: true }}
      />
      <Text>Gist Rated, Opened, and Full Rated</Text>
      <MiniArticleCard
        article={{
          ...article,
          isOpened: true,
          isSurveyed: true,
          gistRating: "like",
        }}
      />
    </Screen>
  )
}
