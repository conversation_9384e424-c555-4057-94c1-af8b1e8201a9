export enum NewsEventType {
  NewsTabChanged = "news_tab_changed",
  StatsBarTapped = "stats_bar_tapped",
  FullRatingSubmitted = "full_rating_submitted",
  GistsForceLoaded = "gists_force_loaded",
  GistViewed = "gist_viewed",
  GistSwipingStarted = "gist_swiping_started",
  GistSwipingStopped = "gist_swiping_stopped",
  GistSwiped = "gist_swiped",
  GistAutoSwiped = "gist_auto_swiped",
  GistUnmounted = "gist_unmounted",
  ArticleOpenedFromGist = "article_opened_from_gist",
  SectionSelectorOpened = "section_selector_opened",
  GistSectionSelected = "gist_section_selected",
  ShuffleToggled = "shuffle_toggled",
  GistsEmptyStateViewed = "gists_empty_state_viewed",
  GistsBackToNewsFeedTapped = "gists_back_to_newsfeed_tapped",
  FinishReadingArticleTapped = "finish_reading_article_tapped",
  EmptyNewsFeedSeen = "empty_news_feed_seen",
}

export interface Article {
  id: number
  title: string
  source: string
  publishedAt: string
  url: string
  imageUrl: string
  videoURL?: string
  frontpageSection: string
  faviconUrl?: string
  localImage?: any
  summaryPoints: string[] | null
  isOpened: boolean
  isSurveyed: boolean
  gistRating: GistSwipeType | null
  position: number | null
}

export interface RawArticle
  extends Pick<Article, "id" | "title" | "source" | "url"> {
  published_at: string
  image_url: string
  frontpage_section: string
  favicon_url?: string
  summary_points: Article["summaryPoints"]
  is_opened: boolean
  is_surveyed: boolean
  is_gist_rated: boolean
  gist_rating: Article["gistRating"]
  position: Article["position"]
}

export type ArticleReport = {
  reason: string
  note: string
  articleId: number
}

export type GistSwipeType = "dislike" | "like" | "skip"
