import { But<PERSON> } from "@/components/Button"
import { Text } from "@/components/Themed"
import { ProfileItem, ProfileList } from "@/components/settings/ProfileItems"
import { FlatList, Linking, StyleSheet, View } from "react-native"
import React, { useEffect, useState } from "react"
import { useSession } from "@/ctx"
import NotificationsSection from "./NotificationsSection"
import { Loader } from "../widgets/Loader"
import { useChatContext } from "@/chatContext"
import DeactivateAccountModal, { Deactivation } from "./DeactivateAccountModal"
import SettingsSwitch from "./SettingsSwitch"
import {
  getUserSettings,
  updateUserSettings,
  UserSettings,
} from "@/apiQueries/userSettings"
import { registerForPushNotificationsAsync } from "@/utils/pushNotifications"
import { useFeatureFlag } from "@/utils/featureFlags"
import { archiveAccount } from "@/apiQueries/auth"
import { Session } from "@/types/user"
import { PRIVACY_URL, TOS_URL } from "@/constants/Links"
import { LocationSwitch } from "./LocationSwitch"
import { trackEvent } from "@/utils/tracking"
import { ProfileEventType } from "@/types/profile"

type Section = {
  title: string
  component: React.JSX.Element
}

export default function GeneralSettingsSection() {
  const { session, refreshSession, signOut } = useSession()
  const { disconnectClient } = useChatContext()
  const [settings, setSettings] = useState<UserSettings | null>(null)

  const fetchUserSettings = async () => {
    try {
      const settings = await getUserSettings({
        token: session!.token,
      })
      setSettings(settings)
    } catch (error) {
      console.error("Error fetching settings:", error)
    }
  }

  useEffect(() => {
    if (!session) return
    fetchUserSettings()
  }, [session])

  if (!session || !settings) {
    return <Loader />
  }

  const handleSettingChange = async (
    setting: keyof UserSettings,
    value: boolean,
  ) => {
    await updateUserSettings({
      token: session.token,
      [setting]: value,
    })
    await refreshSession(session.token)
    await fetchUserSettings()
  }

  const handleSignOut = async () => {
    disconnectClient()
    signOut()
  }

  const handleDeactivate = async (d: Deactivation) => {
    if (!session) return
    await archiveAccount(session.token, d)
    handleSignOut()
  }

  return (
    <GeneralSettingsSection_
      settings={settings}
      session={session}
      onSettingChange={handleSettingChange}
      onSignOut={handleSignOut}
      onDeactivate={handleDeactivate}
    />
  )
}

type GeneralSettingsSectionProps_ = {
  settings: UserSettings
  session: Session
  onSettingChange: (
    setting: keyof UserSettings,
    value: boolean,
  ) => Promise<void>
  onSignOut: () => void
  onDeactivate: (d: Deactivation) => void
}

export const GeneralSettingsSection_ = ({
  settings,
  session,
  onSettingChange,
  onSignOut,
  onDeactivate,
}: GeneralSettingsSectionProps_) => {
  const showMatchMoods = useFeatureFlag("match_moods")
  const showSoundHapticToggle = useFeatureFlag("rating_sounds_and_haptics")

  const [modalVisible, setModalVisible] = useState(false)

  useEffect(() => {
    registerForPushNotificationsAsync()
  }, [])

  const handleCancel = () => {
    setModalVisible(false)
  }

  const sections: Section[] = [
    {
      title: "Personal Details",
      component: <ProfileList items={personalDetailsItems} />,
    },
    {
      title: "Location",
      component: (
        <LocationSwitch
          value={settings.autoUpdateLocation}
          onChange={(value) => onSettingChange("autoUpdateLocation", value)}
        />
      ),
    },
    {
      title: "Notifications",
      component: (
        <NotificationsSection
          session={session}
          onToggleOptOut={(value) =>
            onSettingChange("nonessentialNotificationsDisabled", value)
          }
        />
      ),
    },
    ...(showSoundHapticToggle
      ? [
          {
            title: "Sounds & Haptics",
            component: (
              <SettingsSwitch
                title="Rating Sounds & Haptics"
                initialValue={settings.ratingSoundsAndHaptics}
                onChange={(value) =>
                  onSettingChange("ratingSoundsAndHaptics", value)
                }
              />
            ),
          },
        ]
      : []),
    ...(showMatchMoods
      ? [
          {
            title: "Match Moods in Newsfeed",
            component: (
              <SettingsSwitch
                title="Disable Match Moods"
                subtitle="Don't show your Match's article ratings"
                initialValue={settings.matchMoodsOptOut}
                onChange={(value) => onSettingChange("matchMoodsOptOut", value)}
              />
            ),
          },
        ]
      : []),
    {
      title: "Legal",
      component: <ProfileList items={legalItems} />,
    },
    {
      title: "Help & Feedback",
      component: <ProfileList items={contactUSItems} />,
    },
  ]

  const renderItem = ({ item, index }: { item: Section; index: number }) => {
    return (
      <View
        style={[styles.sectionContainer, { marginTop: index == 0 ? 24 : 0 }]}
      >
        <Text style={styles.sectionTitle}>{item.title}</Text>
        {item.component}
      </View>
    )
  }

  const listFooterComponent = () => {
    return (
      <View style={styles.sectionContainer}>
        <Button
          text="Log out"
          style={styles.button}
          textStyle={{ color: "black" }}
          onPress={onSignOut}
        />
        <Button
          text="Delete account"
          style={styles.button}
          textStyle={{ color: "red" }}
          onPress={() => setModalVisible(true)}
        />
      </View>
    )
  }

  return (
    <>
      <View style={{ flex: 1 }}>
        <FlatList
          data={sections}
          renderItem={renderItem}
          keyExtractor={(item) => item.title}
          ListFooterComponent={listFooterComponent}
        />
      </View>
      <DeactivateAccountModal
        visible={modalVisible}
        onDeactivate={onDeactivate}
        onClose={handleCancel}
      />
    </>
  )
}

const styles = StyleSheet.create({
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
    color: "gray",
    fontWeight: "500",
    fontSize: 14,
  },
  button: {
    width: "100%",
    backgroundColor: "white",
    borderColor: "black",
    marginBottom: 10,
    borderRadius: 32,
  },
})

const personalDetailsItems: ProfileItem[] = [
  {
    title: "Change password",
    href: "/(app)/account/settings/change-password",
  },
]

const legalItems: ProfileItem[] = [
  {
    title: "Privacy policy",
    href: PRIVACY_URL,
  },
  {
    title: "Terms of service",
    href: TOS_URL,
  },
]

const contactUSItems: ProfileItem[] = [
  {
    title: "Contact the InPress team",
    onPressed: () => {
      Linking.openURL("mailto:<EMAIL>")
      trackEvent(ProfileEventType.GeneralSettingOptionPressed, {
        data: { option: "contact-support" },
      })
    },
  },
]
