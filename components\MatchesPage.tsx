import { router, useFocusEffect } from "expo-router"
import React, { useCallback, useEffect, useRef, useState } from "react"
import {
  Channel<PERSON>ist,
  ChannelListMessenger,
  DefaultStreamChatGenerics,
} from "stream-chat-expo"
import { StyleSheet } from "react-native"
import type { Channel } from "stream-chat"
import { useSession } from "@/ctx"
import { Match, getMatches } from "@/apiQueries/apiQueries"
import { EmptyState } from "./widgets/EmptyState"
import { Loader } from "./widgets/Loader"
import { Screen } from "./Themed"
import ChatPreview, { ChatPreview_ } from "./ChatPreview"
import {
  ActiveConnectionMode,
  useActiveConnectionMode,
} from "@/context/ModeContext"
import _ from "lodash"
import { ConnectionMode } from "./signInOrUp/ConnectionModeStep"
import ConnectionModeNotReady from "./ConnectionModeNotReady"
import { useChatContext } from "@/chatContext"
import { useAppState } from "@/app/hooks/useAppState"
import { EventType, trackEvent } from "@/utils/tracking"
import { TouchableWithoutFeedback } from "react-native-gesture-handler"
import { markAllNewMessagesAsRead } from "@/apiQueries/notifications"
import { isUserWithProfile, User } from "@/types/user"
import { ProfileMissing } from "./widgets/ProfileMissing"

const API_KEY = process.env.EXPO_PUBLIC_STREAM_API_KEY

if (!API_KEY) {
  throw new Error("Missing Stream API key")
}

export const filterChannels = ({
  matches,
  channels,
}: {
  matches: Match[]
  channels: Channel<DefaultStreamChatGenerics>[]
}) => {
  return channels.filter((channel) => {
    const members = channel.state.members
    const memberIds = Object.keys(members)
    return memberIds.some((id) =>
      matches.some((match) => match.user.id.toString() === id),
    )
  })
}

const MatchesPage = () => {
  const { session } = useSession()
  const { activeConnectionMode } = useActiveConnectionMode()
  const {
    client,
    triggerInitializeChat,
    refreshNeeded,
    setRefreshNeeded,
    setChannel,
  } = useChatContext()
  const [datesMatches, setDatesMatches] = useState<Match[]>()
  const [friendsMatches, setFriendsMatches] = useState<Match[]>()
  const matches =
    activeConnectionMode === ConnectionMode.Dates
      ? datesMatches
      : friendsMatches

  const [isLoading, setIsLoading] = useState(true)
  const refreshMatches = async (connectionMode: ActiveConnectionMode) => {
    console.log("Refreshing matches")

    setIsLoading(true)
    getMatches({
      token: session!.token,
      connectionMode: activeConnectionMode,
    })
      .then((matches) => {
        if (connectionMode === ConnectionMode.Dates) setDatesMatches(matches)
        else setFriendsMatches(matches)

        const countWithoutScore = matches.filter((m) => !m.score).length
        if (countWithoutScore > 0) {
          trackEvent(EventType.MatchWithoutLeadSeen, {
            data: { countWithoutScore },
          })
        }
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  const refreshData = async () => {
    await refreshMatches(activeConnectionMode)
    markAllNewMessagesAsRead(session!.token)
  }

  useFocusEffect(
    useCallback(() => {
      refreshData()
    }, [session, activeConnectionMode]),
  )

  useAppState({
    onForeground: refreshData,
  })

  useEffect(() => {
    if (refreshNeeded) {
      console.log("Refreshing matches")
      refreshData()
      setRefreshNeeded(false)
    }
  }, [refreshNeeded])

  const handleSelect = (channel: Channel) => {
    setChannel(channel)
    router.navigate("/(app)/matches/chat")
  }

  if (isLoading) {
    return (
      <Loader
        connectionMode={activeConnectionMode}
        errorTag="matches isLoading"
      />
    )
  }

  if (!isUserWithProfile(session!.user)) {
    return <ProfileMissing />
  } else {
    return (
      <MatchesPage_
        matches={matches}
        activeConnectionMode={activeConnectionMode}
        user={session!.user}
        onSelectChannel={handleSelect}
      />
    )
  }
}

type MatchesPageProps_ = {
  matches: Match[] | undefined
  activeConnectionMode: ActiveConnectionMode
  user: User | undefined
  testMode?: boolean
  onSelectChannel: (channel: Channel) => void
}

const MatchesPage_ = ({
  matches,
  activeConnectionMode,
  user,
  testMode = false,
  onSelectChannel,
}: MatchesPageProps_) => {
  if (
    (activeConnectionMode === ConnectionMode.Dates &&
      !user?.datesModeIsActivated) ||
    (activeConnectionMode === ConnectionMode.Friends &&
      !user?.friendsModeIsActivated)
  )
    return <ConnectionModeNotReady connectionMode={activeConnectionMode} />

  if (!user || !matches) {
    return (
      <Loader
        connectionMode={activeConnectionMode}
        errorTag="!user || !matches"
      />
    )
  }

  if (matches.length === 0) {
    return (
      <EmptyState
        title="Where are they?"
        subtitle="Swipe through your Leads to start getting connections. You're almost there!"
        buttonText="Go to Leads"
        onButtonPress={() => router.push("/leads")}
      />
    )
  }

  const renderPreview = (channel: Channel) => {
    const match = matches.find((match) =>
      _.some(
        channel.state.members,
        (member) => member.user?.id.toString() === match.user.id.toString(),
      ),
    )

    if (!match?.user) return null

    return (
      <TouchableWithoutFeedback
        key={channel.id}
        onPress={() => onSelectChannel(channel)}
      >
        <ChatPreview
          key={channel.id}
          user={user}
          otherUser={match.user}
          score={match.score}
          channel={channel}
        />
      </TouchableWithoutFeedback>
    )
  }

  return (
    <Screen style={styles.channelListContainer}>
      {testMode ? (
        matches.map((match) => (
          <ChatPreview_
            key={match.user.id}
            user={user}
            otherUser={match.user}
            score={match.score}
            lastMessage={null}
            connectionMode={activeConnectionMode}
          />
        ))
      ) : (
        <ChannelList
          List={(props) => (
            <ChannelListMessenger
              {...props}
              additionalFlatListProps={{ style: { flexGrow: 1 } }}
            />
          )}
          Preview={({ channel }) => renderPreview(channel)}
          filters={{
            type: "messaging",
            id: {
              $in: matches
                .map((match) => match.chatChannelId)
                .filter((id) => !!id),
            },
          }}
          channelRenderFilterFn={(channels) =>
            filterChannels({ matches, channels })
          }
          sort={{ last_updated: -1 }}
        />
      )}
    </Screen>
  )
}

const styles = StyleSheet.create({
  channelListContainer: {
    paddingHorizontal: 0,
    flex: 1,
  },
})

export default MatchesPage
export { MatchesPage_ }
