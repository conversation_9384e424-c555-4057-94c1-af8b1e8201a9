import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetScrollView,
} from "@gorhom/bottom-sheet"
import { useCallback, useRef } from "react"
import { View, StyleSheet } from "react-native"
import { useSharedValue } from "react-native-reanimated"
import { SurveyStepWrapper } from "./SurveyStepWrapper"
import { Step } from "./ArticleSurvey"

type RatingDrawerProps = {
  step: Step
  stepIndex: number
  totalSteps: number
  onNext?: () => void
  onBack?: () => void
  onClose: () => void
}

export const RatingDrawer = ({
  step,
  stepIndex,
  totalSteps,
  onNext,
  onBack,
  onClose,
}: RatingDrawerProps) => {
  const { title, subtitle, nextIsDisabled, children } = step
  const bottomSheetRef = useRef<BottomSheet>(null)

  const sharedValue = useSharedValue(1)

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={1}
        onPress={onClose}
        animatedIndex={sharedValue}
      />
    ),
    [],
  )

  return (
    <BottomSheet
      ref={bottomSheetRef}
      onClose={onClose}
      enablePanDownToClose
      enableDynamicSizing
      backdropComponent={renderBackdrop}
    >
      <BottomSheetScrollView
        contentContainerStyle={styles.bottomSheetContainer}
      >
        <View style={styles.expandedContainer}>
          <SurveyStepWrapper
            title={title}
            subtitle={subtitle}
            nextIsDisabled={nextIsDisabled}
            onNext={onNext}
            onBack={onBack}
            onClose={onClose}
            stepIndex={stepIndex}
            totalSteps={totalSteps}
          >
            {children}
          </SurveyStepWrapper>
        </View>
      </BottomSheetScrollView>
    </BottomSheet>
  )
}

const styles = StyleSheet.create({
  expandedContainer: {
    height: "100%",
    minHeight: 300,
    flexDirection: "column",
    justifyContent: "center",
    backgroundColor: "white",
    borderRadius: 8,
    width: 360,
    elevation: 5,
  },
  bottomSheetContainer: {
    alignItems: "center",
  },
})
