import { GenericNotificationItem } from "./GenericNotificationItem"
import { AlmostLevelUpNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { newsfeedButtonProps } from "../buttonProps"
import { Image } from "expo-image"
import { styles } from "../notificationStyles"

export function AlmostLevelUpNotificationItem({
  item,
}: {
  item: AlmostLevelUpNotification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={
        <Image
          source={item.nextLevel.grayscaleBadgeUrl}
          contentFit="contain"
          contentPosition={"center"}
          style={styles.imageWithoutBorderRadius}
        />
      }
      TextComponent={
        <NormalText>
          You're only <BoldText>{item.pointsNeeded} points</BoldText> away from{" "}
          <BoldText>{item.nextLevel.name}</BoldText>! One more article can take
          you there.
        </NormalText>
      }
      primaryButton={newsfeedButtonProps}
    />
  )
}
