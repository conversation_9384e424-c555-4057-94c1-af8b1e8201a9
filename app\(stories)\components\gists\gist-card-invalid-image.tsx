import { Screen } from "@/components/Themed"
import { articles } from "../../(news)/feed/news"
import { Article } from "@/types/news"
import GistsStack from "@/components/gists/GistsStack"
import { StoryNewsProvider } from "../../story-components/StoryNewsProvider"

export default function Story() {
  const article: Article = {
    ...articles[0],
    imageUrl: "invalid url",
    isSurveyed: false,
    gistRating: null,
  }

  const articlesWithoutImages = [
    { ...article, id: 1 },
    { ...article, id: 2 },
  ]

  return (
    <StoryNewsProvider initialArticles={articlesWithoutImages}>
      <Screen>
        <GistsStack />
      </Screen>
    </StoryNewsProvider>
  )
}
