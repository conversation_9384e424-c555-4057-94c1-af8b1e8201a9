import { StyleSheet, View, Text } from "react-native"
import PasswordInput from "../PasswordInput"
import { useEffect, useState } from "react"
import { findPasswordIssues } from "@/utils/password"

interface PasswordStepProps {
  password?: string
  passwordConfirmation?: string
  onChangePassword: (password: string) => void
  onChangePasswordConfirmation: (passwordConfirmation: string) => void
}

export const PasswordStep = ({
  password,
  passwordConfirmation,
  onChangePassword,
  onChangePasswordConfirmation,
}: PasswordStepProps) => {
  const [passwordVisible, setPasswordVisible] = useState(false)
  const [passwordConfirmationVisible, setPasswordConfirmationVisible] =
    useState(false)
  const [error, setError] = useState<string | null>()

  useEffect(() => {
    const newError = findPasswordIssues(password, passwordConfirmation)
    setError(newError)
  }, [password, passwordConfirmation])

  return (
    <View>
      <PasswordInput
        label="Password"
        value={password}
        onChange={(password) => onChangePassword(password)}
        onVisibilityChange={() => setPasswordVisible(!passwordVisible)}
        secureTextEntry={!passwordVisible}
      />

      <PasswordInput
        label="Confirm password"
        value={passwordConfirmation}
        onChange={(passwordConfirmation) =>
          onChangePasswordConfirmation(passwordConfirmation)
        }
        onVisibilityChange={() =>
          setPasswordConfirmationVisible(!passwordConfirmationVisible)
        }
        secureTextEntry={!passwordConfirmationVisible}
      />
      <Text style={styles.passwordRequirements}>
        Password must be at least 8 characters long
      </Text>
      <Text style={[styles.errorText, error ? {} : styles.hidden]}>
        {error}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  passwordRequirements: {
    color: "gray",
    fontSize: 12,
    marginTop: 5,
  },
  errorText: {
    textAlign: "center",
    marginTop: 15,
    color: "darkred",
  },
  hidden: {
    opacity: 0,
  },
})
