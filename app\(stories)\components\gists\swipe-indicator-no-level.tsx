import { SwipeIndicator } from "@/components/gists/SwipeIndicator"
import { Screen, View } from "@/components/Themed"
import { useLevels } from "@/context/LevelContext"
import { useEffect } from "react"

export default function Story() {
  const { setStats } = useLevels()
  useEffect(() => {
    setStats((prev) => ({
      ...prev!,
      points: 0,
    }))
  }, [])

  return (
    <Screen>
      <SwipeIndicator type="like" overlayStyle={{}} />
    </Screen>
  )
}
