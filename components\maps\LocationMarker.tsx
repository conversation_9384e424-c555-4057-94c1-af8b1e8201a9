import { BROWNSTONE } from "@/constants/Colors"
import { IMAGES } from "@/constants/Images"
import { ScreenHeight } from "@/utils/location"
import { Image } from "expo-image"
import { Platform, View, Text, StyleSheet } from "react-native"

interface LocationMarkerProps {
  address: string
}
export const LocationMarker = ({ address }: LocationMarkerProps) => (
  <View style={styles.markerContainer}>
    <View style={styles.locationText}>
      <Text
        numberOfLines={1}
        style={{
          fontSize: 14,
          color: "white",
          fontFamily: "InterTight-Regular",
        }}
      >
        {address}
      </Text>
    </View>
    <Image style={styles.locationIcon} source={IMAGES.CaretDown} />
  </View>
)

const styles = StyleSheet.create({
  markerContainer: {
    height: 65,
    position: "absolute",
    alignSelf: "center",
    top: ScreenHeight / 8,
    zIndex: 1,
  },
  locationText: {
    backgroundColor: BROWNSTONE,
    paddingHorizontal: 40,
    padding: 10,
    borderRadius: 30,
  },
  locationIcon: {
    position: "absolute",
    top: Platform.OS == "android" ? 34 : 30,
    alignSelf: "center",
    tintColor: BROWNSTONE,
    resizeMode: "contain",
    height: 20,
    width: 20,
  },
})
