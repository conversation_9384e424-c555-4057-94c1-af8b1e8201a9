import { Screen } from "@/components/Themed"
import GistsStack from "@/components/gists/GistsStack"
import { useNewsContext } from "@/context/NewsContext"
import { Button } from "@/components/Button"
import { FinishedSurvey } from "@/components/ratings/ArticleSurvey"
import { NEWSFEED_ARTICLES } from "../../(news)/feed/news"
import { useState } from "react"

export default function Story() {
  const { handleFullRating } = useNewsContext()

  const [stackVisible, setStackVisible] = useState(false)

  const submitFullRating = async () => {
    const fullRating: FinishedSurvey = {
      feelings: ["happy"],
      importanceRating: 5,
      interestRating: 5,
    }
    return await handleFullRating({
      token: "test-token",
      articleId: NEWSFEED_ARTICLES[0].id,
      survey: fullRating,
    })
  }

  return (
    <Screen>
      <Button text="Submit Full Rating" onPress={() => submitFullRating()} />
      <Button
        text={stackVisible ? "Hide Gists Stack" : "Show Gists Stack"}
        onPress={() => setStackVisible(!stackVisible)}
      />
      {stackVisible && <GistsStack />}
    </Screen>
  )
}
