import { SignUp_ } from "@/components/signInOrUp/SignUp"
import { SIGN_UP_PROPS } from "./normal"
import { useEffect, useState } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

export default function SignUpStory() {
  const [saving, setSaving] = useState(true)

  useEffect(() => {
    AsyncStorage.setItem(
      "onboardingData",
      JSON.stringify({
        profile: SIGN_UP_PROPS.defaultProfile,
        stepIndex: 2,
      }),
    ).then(() => setSaving(false))
  })

  if (saving) return null
  return <SignUp_ {...SIGN_UP_PROPS} />
}
