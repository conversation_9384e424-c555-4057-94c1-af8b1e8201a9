import { useEffect, useRef, useState } from "react"
import { WebView } from "react-native-webview"
import { View } from "./Themed"
import {
  ArticleSurvey,
  ArticleSurveyProps,
  FinishedSurvey,
} from "./ratings/ArticleSurvey"
import { useSession } from "@/ctx"
import { useLocalSearchParams } from "expo-router"
import { EventType, trackEvent } from "@/utils/tracking"
import { Loader } from "./widgets/Loader"
import { getUserSettings } from "@/apiQueries/userSettings"
import { useActiveConnectionMode } from "@/context/ModeContext"
import { getLeaderboard } from "@/apiQueries/levels"
import { navigateWithParams } from "@/utils/localParams"
import { articlePath } from "@/utils/deepLinks"
import { useNewsContext } from "@/context/NewsContext"
import { NewsEventType } from "@/types/news"

const LOADER_THRESHOLD = 0.2
const LOADER_TIMEOUT_MS = 2000

export type ArticlePageParams = {
  idStr: string
  url: string
}

export const openArticle = ({ id, url }: { id: number; url: string }) => {
  navigateWithParams<ArticlePageParams>({
    pathname: articlePath,
    params: { idStr: id.toString(), url },
  })
}

export const ArticlePage = () => {
  const { handleFullRating } = useNewsContext()

  const { idStr, url } = useLocalSearchParams<ArticlePageParams>()
  const id = parseInt(idStr)
  const { session } = useSession()
  const { activeConnectionMode } = useActiveConnectionMode()

  const [soundsAndHapticsOn, setSoundsAndHapticsOn] = useState(true)

  const handleSurveyComplete = async (survey: FinishedSurvey) => {
    const response = await handleFullRating({
      token: session!.token,
      articleId: id,
      survey,
    })

    trackEvent(NewsEventType.FullRatingSubmitted, {
      data: {
        article_id: id,
        survey,
      },
    })

    return response
  }

  const handleFetchLeaderboard = async () => {
    return await getLeaderboard({
      token: session!.token,
      connectionMode: activeConnectionMode,
    })
  }

  const fetchSetting = async () => {
    const settings = await getUserSettings({ token: session!.token })
    setSoundsAndHapticsOn(settings.ratingSoundsAndHaptics)
  }

  useEffect(() => {
    fetchSetting()
  }, [])

  return (
    <ArticlePage_
      id={id.toString()}
      url={url}
      initialUserPoints={session!.user.points}
      soundsAndHapticsOn={soundsAndHapticsOn}
      onSurveyComplete={handleSurveyComplete}
      onFetchLeaderboard={handleFetchLeaderboard}
    />
  )
}

interface ArticlePageProps_ {
  id: string
  url: string
  initialUserPoints: ArticleSurveyProps["initialPoints"]
  soundsAndHapticsOn: ArticleSurveyProps["soundsAndHapticsOn"]
  onSurveyComplete: ArticleSurveyProps["onSurveyComplete"]
  onFetchLeaderboard: ArticleSurveyProps["onFetchLeaderboard"]
}

export const ArticlePage_ = ({
  id,
  url,
  initialUserPoints,
  soundsAndHapticsOn,
  onSurveyComplete,
  onFetchLeaderboard,
}: ArticlePageProps_) => {
  const [showLoader, setShowLoader] = useState(true)
  const maxScrollPercentage = useRef(0)

  useEffect(() => {
    trackEvent(EventType.OpenedArticle, {
      opened_article_id: id,
      data: {
        article_id: id,
      },
    })

    const timeOpened = new Date().getTime()

    return () => {
      const timeClosed = new Date().getTime()
      trackEvent(EventType.ClosedArticle, {
        data: {
          article_id: id,
          max_scroll_percentage: maxScrollPercentage.current,
          time_read_ms: timeClosed - timeOpened,
        },
      })
    }
  }, [])

  useEffect(() => {
    setTimeout(() => setShowLoader(false), LOADER_TIMEOUT_MS)
  }, [])

  const onLoadProgress = (progress: number) => {
    if (progress > LOADER_THRESHOLD && showLoader) setShowLoader(false)
  }

  return (
    <View style={{ flex: 1 }}>
      <WebView
        onScroll={(s) => {
          const scrollPercentage =
            (s.nativeEvent.contentOffset.y / s.nativeEvent.contentSize.height) *
            100

          maxScrollPercentage.current = Math.max(
            scrollPercentage,
            maxScrollPercentage.current,
          )
        }}
        source={{ uri: url }}
        allowsInlineMediaPlayback={true}
        decelerationRate="normal"
        onLoadProgress={({ nativeEvent }) =>
          onLoadProgress(nativeEvent.progress)
        }
        containerStyle={{ display: showLoader ? "none" : undefined }}
      />
      {showLoader ? (
        <Loader />
      ) : (
        <ArticleSurvey
          initialPoints={initialUserPoints}
          soundsAndHapticsOn={soundsAndHapticsOn}
          onSurveyComplete={onSurveyComplete}
          onFetchLeaderboard={onFetchLeaderboard}
        />
      )}
    </View>
  )
}

export default ArticlePage
