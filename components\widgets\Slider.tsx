import { StyleSheet } from "react-native"
import MultiSlider, {
  MultiSliderProps,
} from "@ptomasroos/react-native-multi-slider"
import { BROWNSTONE } from "@/constants/Colors"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"

interface SliderProps extends MultiSliderProps {
  disabled?: boolean
}
export const Slider = ({ disabled = false, ...props }: SliderProps) => (
  <MultiSlider
    enabledOne={!disabled}
    enabledTwo={!disabled}
    sliderLength={wp(75)}
    trackStyle={styles.track}
    stepMarkerStyle={styles.stepMarker}
    stepLabelStyle={styles.stepLabel}
    markerContainerStyle={styles.markerContainer}
    selectedStyle={{ backgroundColor: disabled ? "lightgray" : BROWNSTONE }}
    {...props}
  />
)

const styles = StyleSheet.create({
  track: {
    backgroundColor: "lightgray",
    height: 5,
    borderRadius: 5,
    zIndex: 3,
  },
  stepMarker: {
    backgroundColor: "#DDD",
    height: 10,
    width: 10,
    marginTop: -2.5,
    paddingTop: 5,
    borderRadius: 5,
    zIndex: 1,
  },
  stepLabel: {
    marginTop: 8,
    marginLeft: -7,
    fontFamily: "InterTight-SemiBold",
  },
  markerContainer: {
    zIndex: 15,
  },
  marker: {
    backgroundColor: "#C16449",
    zIndex: 15,
    borderWidth: 0,
    marginTop: 2.5,
    shadowOpacity: 0.0,
  },
})
