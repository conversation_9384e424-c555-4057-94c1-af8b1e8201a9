import React from "react"
import { View, Text } from "react-native"
import { useActiveConnectionMode } from "@/context/ModeContext"
import { LIME_GREEN, BROWNSTONE } from "@/constants/Colors"
import { Channel, FormatMessageResponse, DefaultGenerics } from "stream-chat"
import { ConnectionMode } from "./signInOrUp/ConnectionModeStep"
import { StyleSheet } from "react-native"
import { TimedImage } from "./TrackedImage"
import { User } from "@/types/user"

interface ChatPreviewProps {
  user: User
  otherUser: User
  score: number
  channel: Channel
}

export default function ChatPreview({
  user,
  otherUser,
  score,
  channel,
}: ChatPreviewProps) {
  const activeConnectionMode = useActiveConnectionMode().activeConnectionMode
  const lastMessage = channel.state.messages[channel.state.messages.length - 1]

  return (
    <ChatPreview_
      user={user}
      otherUser={otherUser}
      score={score}
      lastMessage={lastMessage}
      connectionMode={activeConnectionMode}
    />
  )
}

export type Message = FormatMessageResponse<DefaultGenerics>

export interface ChatPreviewProps_ extends Omit<ChatPreviewProps, "channel"> {
  lastMessage: Message | null
  connectionMode: ConnectionMode
}

export const ChatPreview_ = ({
  user,
  otherUser,
  score,
  lastMessage,
  connectionMode,
}: ChatPreviewProps_) => {
  const lastMessageIsFromThisUser =
    lastMessage && lastMessage.user?.id === user.id.toString()

  let previewText

  if (!lastMessage) {
    previewText = `Start the conversation with ${otherUser.firstName}`
  } else if ((lastMessage.attachments || []).length > 0) {
    previewText = lastMessageIsFromThisUser ? "You sent a gif" : "Sent a gif"
  } else {
    previewText = lastMessage.text
  }

  return (
    <View style={[styles.channelPreview]}>
      <View style={styles.channelPreviewRow}>
        <View style={styles.leftContainer}>
          <TimedImage
            name="chat preview avatar"
            source={{
              uri: otherUser.images[0].url,
            }}
            style={styles.avatar}
          />
          {score ? (
            <View style={styles.scoreLabelContainer}>
              <Text style={styles.scoreLabelText}>
                {(score * 100).toFixed(0)}%
              </Text>
            </View>
          ) : null}
        </View>
        <View style={styles.rightContainer}>
          <View style={styles.topContainer}>
            <Text style={styles.username}>{otherUser.firstName}</Text>
            <View
              style={[
                styles.chatStatusContainer,
                {
                  display:
                    lastMessage && lastMessageIsFromThisUser ? "none" : "flex",
                  backgroundColor: !lastMessage
                    ? connectionMode === ConnectionMode.Dates
                      ? BROWNSTONE
                      : LIME_GREEN
                    : !lastMessageIsFromThisUser
                    ? "black"
                    : "none",
                },
              ]}
            >
              <Text
                style={[
                  styles.chatStatusLabel,
                  { color: !lastMessage ? "black" : "white" },
                ]}
              >
                {!lastMessage
                  ? "Start chat"
                  : !lastMessageIsFromThisUser && "Your move"}
              </Text>
            </View>
          </View>
          <View style={styles.bottomContainer}>
            <Text style={styles.lastMessage} numberOfLines={1}>
              {previewText}
            </Text>
          </View>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  channelPreview: {
    width: "100%",
    height: 100,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: "white",
    flexDirection: "row",
    flex: 6,
    paddingVertical: 5,
    paddingHorizontal: 10,
    alignItems: "center",
    justifyContent: "space-between",
  },
  channelPreviewRow: {
    width: "100%",
    columnGap: 22,
    flexDirection: "row",
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 100,
  },
  username: {
    fontSize: 16.5,
    fontFamily: "Intertight-Semibold",
    textAlignVertical: "center",
  },
  lastMessage: {
    fontSize: 13,
  },
  leftContainer: {
    position: "relative",
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  scoreLabelContainer: {
    position: "absolute",
    bottom: -5,
    right: -12,
    backgroundColor: BROWNSTONE,
    paddingVertical: 3.5,
    paddingHorizontal: 5,
    borderRadius: 100,
    marginLeft: 10,
  },
  scoreLabelText: {
    color: "black",
    fontSize: 9,
  },
  rightContainer: {
    flex: 5,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
  },
  chatStatusContainer: {
    borderRadius: 30,
    paddingHorizontal: 9,
    paddingVertical: 5,
  },
  chatStatusLabel: {
    fontSize: 10,
  },
  topContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 6,
  },
  bottomContainer: {
    flexDirection: "row",
  },
})
