import React, { useState, useRef, forwardRef, useImperativeHandle } from "react"
import R<PERSON><PERSON><PERSON>, {
  SwiperProps as RNSwiperProps,
} from "react-native-deck-swiper"
import {
  withSpring,
  useSharedValue,
  useAnimatedStyle,
} from "react-native-reanimated"

type SwiperRef<T> = RNSwiper<T> | null

export type SwiperInnerProps<T> = RNSwiperProps<T> & {
  thresholdMultiplier: number
  renderRejectComponent: (n: any) => React.ReactNode
  renderAcceptComponent: (n: any) => React.ReactNode
  renderSwipeUpComponent?: (n: any) => React.ReactNode
}

function SwiperInner<T>(
  props: SwiperInnerProps<T>,
  ref: React.Ref<SwiperRef<T>>,
) {
  const swiperRef = useRef<SwiperRef<T>>(null)

  useImperativeHandle(ref, () => swiperRef.current)

  const {
    thresholdMultiplier,
    verticalSwipe,
    renderRejectComponent,
    renderAcceptComponent,
    renderSwipeUpComponent,
  } = props

  const overlayThreshold = 10
  const swipeXOffset = useSharedValue(0)
  const swipeYOffset = useSharedValue(0)

  const [isSwipingUp, setIsSwipingUp] = useState(false)

  const rejectOverlay = useAnimatedStyle(() => {
    const isSwipingLeft =
      swipeXOffset.value <= -thresholdMultiplier * overlayThreshold
    return {
      transform: [{ scale: withSpring(isSwipingLeft && !isSwipingUp ? 1 : 0) }],
    }
  }, [swipeXOffset])

  const acceptOverlay = useAnimatedStyle(() => {
    const isSwipingRight =
      swipeXOffset.value >= thresholdMultiplier * overlayThreshold
    return {
      transform: [
        {
          scale: withSpring(isSwipingRight && !isSwipingUp ? 1 : 0),
        },
      ],
    }
  }, [swipeXOffset])

  const swipeUpOverlay = useAnimatedStyle(() => {
    const isSwipingLeft =
      swipeXOffset.value <= -thresholdMultiplier * overlayThreshold
    const isSwipingRight =
      swipeXOffset.value >= thresholdMultiplier * overlayThreshold

    return {
      transform: [
        {
          scale: withSpring(
            !isSwipingRight && !isSwipingLeft && isSwipingUp ? 1 : 0,
          ),
        },
      ],
    }
  }, [swipeYOffset, isSwipingUp])

  const updateSwipeXOffset = (x = 0, y = 0) => {
    swipeYOffset.value = y
    setIsSwipingUp(Boolean(verticalSwipe) && y < -20)
    swipeXOffset.value = x
  }

  const handleSwiping = (x: number, y: number) => {
    updateSwipeXOffset(x, y)
    props.onSwiping?.(x, y)
  }

  const handleSwipedAborted = () => {
    updateSwipeXOffset()
    props.onSwipedAborted?.()
  }

  return (
    <>
      <RNSwiper
        {...props}
        ref={swiperRef}
        onSwiping={handleSwiping}
        onSwipedAborted={handleSwipedAborted}
        cardIndex={0}
        stackSize={3}
        cardVerticalMargin={0}
        onSwiped={(cardIndex) => {
          swipeXOffset.value = 0
          setIsSwipingUp(false)
          props.onSwiped?.(cardIndex)
        }}
      />
      {renderRejectComponent(rejectOverlay)}
      {renderAcceptComponent(acceptOverlay)}
      {renderSwipeUpComponent ? renderSwipeUpComponent(swipeUpOverlay) : null}
    </>
  )
}

type SwiperComponent = <T>(
  props: SwiperInnerProps<T> & { ref?: React.Ref<SwiperRef<T>> },
) => ReturnType<typeof SwiperInner>

export const Swiper = forwardRef(SwiperInner) as SwiperComponent
