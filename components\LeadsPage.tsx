import {
  Swipe,
  SwipeType,
  getLeads,
  reportLeadUser,
  swipeLead,
} from "@/apiQueries/apiQueries"
import { useSession } from "@/ctx"
import { useCallback, useRef, useState } from "react"
import { StyleSheet, View } from "react-native"
import { Screen } from "./Themed"
import { Swiper } from "./Swiper"
import { EmptyState } from "./widgets/EmptyState"
import { router, useFocusEffect } from "expo-router"
import { Loader } from "./widgets/Loader"
import { AntDesign, Feather } from "@expo/vector-icons"
import {
  ActiveConnectionMode,
  useActiveConnectionMode,
} from "@/context/ModeContext"
import PersonCard from "./leads/PersonCard"
import { ConnectionMode } from "./signInOrUp/ConnectionModeStep"
import ConnectionModeNotReady from "./ConnectionModeNotReady"
import {
  datesModePalette,
  friendsModePalette,
  RED,
  SAGE_BRIGHT,
} from "@/constants/Colors"
import { EventType, trackEvent } from "@/utils/tracking"
import { useAppState } from "@/app/hooks/useAppState"
import MatchCelebrationModal from "./leads/MatchCelebrationModal"
import { datesLead, friendsLead } from "./leads/testLeads"
import { useTestMode } from "@/utils/testModeCtx"
import _ from "lodash"
import Animated, {
  withSpring,
  useSharedValue,
  useAnimatedStyle,
} from "react-native-reanimated"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"
import { CountdownIndicator } from "./leads/CountdownIndicator"
import { matchesPath } from "@/utils/deepLinks"
import { isUserWithProfile, User } from "@/types/user"
import { LeadsTeaser } from "@/screens/leads/LeadsTeaser"
import { Lead } from "@/types/social"

export const LeadsPage = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [leads, setLeads] = useState<Lead[]>([])
  const [maxLeadsReached, setMaxLeadsReached] = useState(false)
  const { session } = useSession()
  const { activeConnectionMode } = useActiveConnectionMode()
  const { testMode } = useTestMode()
  const requestVersionRef = useRef(0)

  const fetchLeads = async () => {
    if (!isUserWithProfile(session!.user)) {
      return
    }

    requestVersionRef.current += 1
    const requestVersion = requestVersionRef.current

    setIsLoading(true)
    getLeads({
      token: session!.token,
      connectionMode: activeConnectionMode,
    })
      .then(({ leads, maxLeadsReached }) => {
        if (requestVersion !== requestVersionRef.current) {
          return
        }

        console.log(`Got ${leads.length} leads`)
        console.log(`Max leads reached: ${maxLeadsReached}`)

        setLeads(leads)
        setMaxLeadsReached(maxLeadsReached)
        trackEvent(EventType.LeadsFetched, {
          data: {
            leadCount: leads?.length,
            connectionMode: activeConnectionMode,
            maxLeadsReached,
          },
        })
      })
      .catch((error) => {
        console.error("Error fetching leads:", error)
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  useFocusEffect(
    useCallback(() => {
      fetchLeads()
    }, [session, activeConnectionMode]),
  )

  useAppState({
    onForeground: fetchLeads,
  })

  const handleSwipe = async (swipe: Swipe) => {
    const { isMutual } = await swipeLead({
      token: session!.token,
      swipe,
      connectionMode: activeConnectionMode,
    })
    return { isMutual }
  }

  const handleHideAndReport = async (userId: number) => {
    await reportLeadUser(session!.token, userId)
    await swipeLead({
      token: session!.token,
      swipe: { swipedUserId: userId, type: "pass" },
      connectionMode: activeConnectionMode,
    })
    setLeads((leads) => leads?.filter((l) => l.user.id !== userId))
  }

  if (!isUserWithProfile(session!.user)) {
    return <LeadsTeaser />
  }

  if (isLoading || !session) {
    return <Loader connectionMode={activeConnectionMode} />
  }

  if (!testMode) {
    return (
      <LeadsPage_
        leads={leads}
        maxLeadsReached={maxLeadsReached}
        user={session.user}
        connectionMode={activeConnectionMode}
        onSwipe={handleSwipe}
        onHideAndReport={handleHideAndReport}
      />
    )
  } else {
    return (
      <LeadsPage_
        leads={
          activeConnectionMode === ConnectionMode.Dates
            ? [datesLead]
            : [friendsLead]
        }
        maxLeadsReached={false}
        user={session.user}
        connectionMode={activeConnectionMode}
        onSwipe={async () => ({ isMutual: false })}
        onHideAndReport={async () => _.noop()}
      />
    )
  }
}

export interface LeadsPageProps_ {
  leads: Lead[]
  maxLeadsReached: boolean
  user: User
  connectionMode: ActiveConnectionMode
  onSwipe: (swipe: Swipe) => Promise<{ isMutual: boolean }>
  onHideAndReport: (userId: number) => Promise<void>
}

export const LeadsPage_ = ({
  leads,
  maxLeadsReached,
  user,
  connectionMode,
  onSwipe,
  onHideAndReport,
}: LeadsPageProps_) => {
  const widthOffset = wp(5)

  const [allLeadsAreRated, setAllLeadsAreRated] = useState(false)
  const [match, setMatch] = useState<User | null>(null)

  const backgroundColor =
    connectionMode === ConnectionMode.Dates
      ? datesModePalette.backgroundColor
      : friendsModePalette.backgroundColor

  const [countRemaining, setCountRemaining] = useState<number>(leads?.length)

  const handleSwipe = async (cardIndex: number, type: SwipeType) => {
    const lead = leads[cardIndex]

    const { isMutual } = await onSwipe({
      swipedUserId: lead.user.id,
      type,
    })

    trackEvent(EventType.SwipedLead, {
      data: {
        connection_mode: connectionMode,
        swipe_type: type,
        is_mutual: isMutual,
        match_percentage: lead.score,
      },
    })

    if (isMutual) {
      setMatch(lead.user)
    }

    setCountRemaining(countRemaining - 1)
  }

  const handleReport = async (userId: number) => {
    await onHideAndReport(userId)
    setCountRemaining(countRemaining - 1)
  }

  const renderCelebrationModal = () =>
    !_.isNil(match) ? (
      <MatchCelebrationModal
        visible={true}
        matchProfileImageUrl={match.images[0].url}
        currentUser={user}
        connectionMode={connectionMode}
        onGoToMatches={() => {
          setMatch(null)
          router.push(matchesPath)
        }}
        onClose={() => {
          setMatch(null)
        }}
      />
    ) : null

  if (
    (connectionMode === ConnectionMode.Dates && !user.datesModeIsActivated) ||
    (connectionMode === ConnectionMode.Friends && !user.friendsModeIsActivated)
  ) {
    return <ConnectionModeNotReady connectionMode={connectionMode} />
  }

  if (!leads.length || allLeadsAreRated) {
    let title, subtitle

    if (maxLeadsReached) {
      title = "That's all for today!"
      subtitle = `You've reached your Leads limit for today. Check back tomorrow for more!`
    } else if (allLeadsAreRated) {
      title = "No more Leads!"
      subtitle =
        "Read more articles to get Leads. Or, adjust your match preferences in Settings."
    } else {
      title = "No Leads yet"
      subtitle =
        "Read more articles to get Leads. The more you rate, the better your Leads become!"
    }

    return (
      <View style={{ flex: 1 }}>
        <CountdownIndicator
          countRemaining={countRemaining}
          connectionMode={connectionMode}
        />
        <EmptyState
          title={title}
          subtitle={subtitle}
          buttonText="Back to News"
          backgroundColor={backgroundColor}
          onButtonPress={() => router.push("/news/feed")}
        />
        {renderCelebrationModal()}
      </View>
    )
  }

  return (
    <Screen style={{ backgroundColor }}>
      <CountdownIndicator
        countRemaining={countRemaining}
        connectionMode={connectionMode}
      />
      <Swiper<Lead>
        cards={leads.map((l) => l)}
        verticalSwipe={false}
        renderCard={(lead) => {
          return (
            <PersonCard
              lead={lead}
              recipientUser={user}
              onHideAndReport={handleReport}
            />
          )
        }}
        backgroundColor={backgroundColor}
        cardIndex={0}
        stackSize={3}
        cardVerticalMargin={0}
        thresholdMultiplier={1}
        renderRejectComponent={(rejectOverlay) => (
          <Animated.View
            style={[
              styles.overlay,
              { backgroundColor: RED, left: widthOffset },
              rejectOverlay,
            ]}
          >
            <AntDesign name="close" size={32} color="white" />
          </Animated.View>
        )}
        renderAcceptComponent={(acceptOverlay) => (
          <Animated.View
            style={[
              styles.overlay,
              { backgroundColor: SAGE_BRIGHT, right: widthOffset },
              acceptOverlay,
            ]}
          >
            <Feather name="check" size={32} color="black" />
          </Animated.View>
        )}
        onSwipedLeft={async (cardIndex) => handleSwipe(cardIndex, "pass")}
        onSwipedRight={async (cardIndex) => handleSwipe(cardIndex, "like")}
        onSwipedAll={() => {
          setAllLeadsAreRated(true)
        }}
      />
      {renderCelebrationModal()}
    </Screen>
  )
}

const styles = StyleSheet.create({
  overlay: {
    width: 64,
    height: 64,
    borderRadius: 32,
    position: "absolute",
    top: hp(25),
    alignItems: "center",
    justifyContent: "center",
    opacity: 0.8,
  },
})
