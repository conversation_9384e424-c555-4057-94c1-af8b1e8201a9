import React from "react"
import TinderCard from "react-tinder-card"
import { GistCard } from "@/components/gists/GistCard"
import { Article } from "@/types/news"

interface CardSwiperProps {
  articles: Article[]
  onSwipe: (direction: string, article: Article) => void
}

export const CardSwiper = ({ articles, onSwipe }: CardSwiperProps) => {
  return (
    <div style={styles.deck}>
      {articles.map((article, index) => (
        <TinderCard
          key={article.id}
          onSwipe={(direction) => onSwipe(direction, article)}
          preventSwipe={["up", "down"]}
        >
          <div style={styles.cardWrapper}>
            {/* Inner wrapper for stacking effect (doesn't conflict with swipe transform) */}
            <div
              style={{
                ...styles.innerStack,
                transform: `scale(${1 - index * 0.02}) translateY(${
                  index * 8
                }px)`,
              }}
            >
              <GistCard
                article={article}
                isVisible={true}
                demoMode={true}
                onImageLoaded={() =>
                  console.log("Image loaded:", article.imageUrl)
                }
                onTimerCompleted={() => console.log("Timer completed")}
              />
            </div>
          </div>
        </TinderCard>
      ))}
    </div>
  )
}

const styles = {
  deck: {
    position: "absolute" as const,
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "350px", // Fixed size for now
    height: "500px",
  },
  cardWrapper: {
    position: "absolute" as const,
    width: "100%",
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  innerStack: {
    width: "100%",
    height: "100%",
    transition: "transform 0.3s ease", // smooth stacking transition
  },
}
