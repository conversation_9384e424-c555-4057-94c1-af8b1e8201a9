import React from "react"
import { TouchableOpacity, StyleSheet, View } from "react-native"
import { Text } from "../Themed"

export const RatingRadioGroup = ({
  ratingOptions,
  selectedRating,
  onRatingChange,
}: {
  ratingOptions: string[]
  selectedRating?: number
  onRatingChange: (rating: number) => void
}) => {
  return (
    <View style={styles.optionsContainer}>
      {ratingOptions
        .map((option, index) => ({ option, index: index + 1 }))
        .map(({ option, index }) => (
          <TouchableOpacity
            key={index}
            onPress={() => onRatingChange(index)}
            style={styles.optionButton}
          >
            <View style={styles.optionContent}>
              <View
                style={[
                  styles.radioButton,
                  {
                    borderColor: index === selectedRating ? "blue" : "gray",
                  },
                ]}
              >
                {index === selectedRating && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
              <Text style={styles.optionLabel}>{option}</Text>
            </View>
          </TouchableOpacity>
        ))}
    </View>
  )
}

const styles = StyleSheet.create({
  optionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  optionButton: {
    alignItems: "center",
    width: 62,
  },
  optionContent: {
    alignItems: "center",
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "blue",
  },
  optionLabel: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 10,
    marginTop: 5,
    textAlign: "center",
  },
})
