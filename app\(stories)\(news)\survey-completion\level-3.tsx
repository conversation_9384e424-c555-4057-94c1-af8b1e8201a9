import { Step } from "@/components/ratings/ArticleSurvey"
import { RatingDrawer } from "@/components/ratings/RatingDrawer"
import _ from "lodash"
import { SoundLoadedPointsCompletionStep } from "@/components/ratings/SoundLoadedPointsCompletionStep"
import { PointEvent } from "@/types/levels"

export const POINT_EVENTS: PointEvent[] = [
  {
    label: "Article Rating",
    points: 10,
    type: "article_rated",
    subtype: null,
  },
  {
    label: "1-Week Rating Streak",
    points: 40,
    type: "streak_bonus",
    subtype: "7_day",
  },
  {
    label: "3 Ratings in 24 Hours",
    points: 10,
    type: "rating_bonus",
    subtype: null,
  },
]

export default function Story() {
  const step: Step = {
    children: (
      <SoundLoadedPointsCompletionStep
        initialPoints={110}
        pointEvents={POINT_EVENTS}
        leaderboardUsers={undefined}
      />
    ),
  }

  return (
    <RatingDrawer step={step} stepIndex={3} totalSteps={4} onClose={_.noop} />
  )
}
