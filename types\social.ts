import { convertRawUser, RawUser, User } from "./user"

export enum SocialEventType {
  LeadWithoutTopicRootIdSeen = "lead_without_topic_root_id_seen",
}

export type Topic = {
  name: string
  rootId: number | null
}

export type RawTopic = {
  name: Topic["name"]
  root_id: Topic["rootId"]
}

export const convertRawTopic = (rawTopic: RawTopic): Topic => ({
  name: rawTopic.name,
  rootId: rawTopic.root_id,
})

export const combineTopicFields = (
  topics: string[],
  topicsWithRoots: RawTopic[] | null,
): Topic[] => {
  let combinedTopics = []

  if (topicsWithRoots) {
    combinedTopics = topicsWithRoots.map(convertRawTopic)
  } else if (topics) {
    combinedTopics = topics.map((name: string) => ({
      name,
      rootId: null,
    }))
  } else {
    return []
  }

  return combinedTopics
}

export interface Lead {
  id: number
  score: number
  topics: Topic[]
  user: User
}

export interface RawLead extends Omit<Lead, "topics" | "user"> {
  user: RawUser
  topics: string | null
  topics_with_roots: string | null
}

export const convertRawLead = (rawLead: RawLead): Lead => {
  const topics = combineTopicFields(
    JSON.parse(rawLead.topics || "[]"),
    rawLead.topics_with_roots ? JSON.parse(rawLead.topics_with_roots) : null,
  )
  return {
    id: rawLead.id,
    score: rawLead.score,
    topics,
    user: convertRawUser(rawLead.user),
  }
}
