import { Screen, Text } from "@/components/Themed"
import {
  StyleSheet,
  View,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from "react-native"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import ShareReminder from "@/components/news/ShareReminder"
import { useEffect, useState } from "react"
import { getReferralCode } from "@/apiQueries/apiQueries"
import { useSession } from "@/ctx"

export default function Route() {
  const { session } = useSession()
  const [referralCode, setReferralCode] = useState<string | null>(null)
  useEffect(() => {
    getReferralCode(session!.token).then(setReferralCode)
  }, [session])

  if (!referralCode) {
    return null
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <Screen>
        <ScreenHeader
          title={"Invite friends, get free premium access"}
          subtitle={
            "Get a free week of premium for every friend that signs up with your unique code!"
          }
        />
        <View style={styles.container}>
          <Text style={styles.referralCodeLabel}>Your referral code</Text>
          <TouchableOpacity
            style={styles.referralCodeButton}
            activeOpacity={0.8}
          >
            <Text>{referralCode}</Text>
          </TouchableOpacity>
        </View>
        <ShareReminder referralCode={referralCode} displayMode="Button" />
      </Screen>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 28,
    marginBottom: 16,
  },
  referralCodeLabel: {
    marginBottom: 8,
  },
  referralCodeButton: {
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#d8dadc",
    padding: 16,
    borderRadius: 10,
  },
})
