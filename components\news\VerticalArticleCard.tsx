import { View, StyleSheet, Dimensions } from "react-native"
import { Card, Title } from "react-native-paper"
import ArticleSource from "./ArticleSource"
import Footer, { FOOTER_HEIGHT } from "./Footer"
import { gridArticleMargin, newsStyles } from "./constants"
import { useFeatureFlag } from "@/utils/featureFlags"
import { ReadRatedCover } from "./ReadRatedCover"
import { Image } from "expo-image"
import ReportArticleModal from "./ReportArticleModal"
import { useState } from "react"
import { openArticle } from "../ArticlePage"
import { Article } from "@/types/news"

export const VerticalArticleCard = ({ article }: { article: Article }) => {
  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")

  const [isReporting, setIsReporting] = useState(false)

  const handlePress = async () => {
    openArticle(article)
  }

  return (
    <Card
      style={[
        styles.card,
        { height: VERTICAL_ARTICLE_CARD_HEIGHT_FN(newReadRatedFlag) },
      ]}
      onPress={handlePress}
      onLongPress={() => setIsReporting(true)}
    >
      <View style={styles.imageContainer}>
        {newReadRatedFlag && <ReadRatedCover article={article} />}
        <Image source={{ uri: article.imageUrl }} style={styles.image} />
      </View>
      <Card.Content style={{ height: CONTENT_HEIGHT_FN(newReadRatedFlag) }}>
        <View style={styles.titleContainer}>
          <ArticleSource article={article} />
          <Title style={styles.title} numberOfLines={4}>
            {article.title}
          </Title>
        </View>
        {!newReadRatedFlag && (
          <Footer
            isRead={article.isOpened}
            isRated={article.isSurveyed}
            articleId={article.id}
          />
        )}
        <ReportArticleModal
          articleId={article.id}
          modalVisible={isReporting}
          onClose={() => setIsReporting(false)}
        />
      </Card.Content>
    </Card>
  )
}

const { width: windowWidth } = Dimensions.get("window")
const cardWidth = windowWidth / 2 - gridArticleMargin * 2

const CONTENT_HEIGHT_FN = (isNewReadRated: boolean) =>
  isNewReadRated ? 130 : 156

export const VERTICAL_ARTICLE_CARD_HEIGHT_FN = (isNewReadRated: boolean) =>
  106 + CONTENT_HEIGHT_FN(isNewReadRated) + FOOTER_HEIGHT

export const styles = StyleSheet.create({
  card: {
    elevation: 3,
    width: cardWidth,
    backgroundColor: "white",
  },
  imageContainer: {
    marginHorizontal: 12,
    marginBottom: 16,
    marginTop: 14,
  },
  image: {
    height: 96,
    borderRadius: 10,
  },
  titleContainer: {
    flex: 1,
    marginBottom: 14,
  },
  title: {
    ...newsStyles.smallArticleTitle,
    marginBottom: 14,
  },
})
