import { StyleSheet, View } from "react-native"
import { Text } from "@/components/Themed"
import { fontStyles } from "@/styles"
import { NormalText } from "./StyledText"
import { LIGHT_GREY, MEDIUM_GREY } from "@/constants/Colors"
import { But<PERSON> } from "./Button"
import { useEffect } from "react"
import { trackEvent } from "@/utils/tracking"
import { NewsEventType } from "@/types/news"
import { useNewsContext } from "@/context/NewsContext"

export const NoMoreGists = () => {
  const { setActiveTab } = useNewsContext()

  useEffect(() => {
    trackEvent(NewsEventType.GistsEmptyStateViewed)
  }, [])

  const handleBackToNewsfeed = () => {
    trackEvent(NewsEventType.GistsBackToNewsFeedTapped)
    setActiveTab("newsfeed")
  }

  return (
    <View style={styles.container}>
      <View style={{ gap: 8 }}>
        <Text style={styles.title}>No more Gists</Text>
        <NormalText style={styles.text}>
          No worries - you can find full articles on the Newsfeed.
        </NormalText>
      </View>
      <Button
        style={styles.backButton}
        textStyle={styles.bottomButtonText}
        text="Back to Newsfeed"
        onPress={handleBackToNewsfeed}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    padding: 25,
    borderRadius: 15,
    borderColor: LIGHT_GREY,
    borderWidth: 1,
    gap: 24,
  },
  title: {
    ...fontStyles.editorial,
    fontSize: 32,
  },
  text: {
    color: MEDIUM_GREY,
  },
  backButton: {
    backgroundColor: "white",
    borderColor: LIGHT_GREY,
    width: 275,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: "center",
  },
  bottomButtonText: {
    color: "black",
    textAlign: "center",
    fontSize: 14,
    fontFamily: "InterTight-Regular",
  },
})
