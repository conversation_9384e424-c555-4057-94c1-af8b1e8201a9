import { FriendRequestAcceptedNotification } from "@/apiQueries/notificationTypes"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { HandleNavigateParams } from "../NotificationFeed"
import { NowFriendsNotificationItem } from "./NowFriendsNotificationItem"

export function FriendRequestAcceptedNotificationItem({
  item,
  onNavigate,
}: {
  item: FriendRequestAcceptedNotification
  onNavigate: (params: HandleNavigateParams) => void
}) {
  const handleOpenChat = () => {
    const streamId = item.chatChannel.streamId
    onNavigate({
      href: {
        pathname: "/(app)/matches/[channelId]",
        params: { channelId: streamId },
      },
      connectionMode: ConnectionMode.Friends,
    })
  }

  return <NowFriendsNotificationItem item={item} onOpenChat={handleOpenChat} />
}
