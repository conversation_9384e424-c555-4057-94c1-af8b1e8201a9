import { useEffect, useMemo, useState } from "react"
import { View, Text, StyleSheet } from "react-native"
import * as Contacts from "expo-contacts"
import { Contact } from "expo-contacts"
import { Button } from "@/components/Button"
import * as SMS from "expo-sms"
import {
  checkContactStatuses,
  createConnectRequests,
  StatusByContactHash,
} from "@/apiQueries/connectRequests"
import { useSession } from "@/ctx"
import _ from "lodash"
import ContactsListItem from "./ContactsListItem"
import { Searchbar } from "react-native-paper"
import { ScrollView } from "react-native-gesture-handler"
import { normalizePhoneNumber } from "@/utils/phoneNumbers"
import { sha256 } from "@/utils/hash"
import { Loader } from "@/components/widgets/Loader"

type ContactPossiblyWithoutName = Omit<Contact, "name"> & {
  name?: string
}
type ContactWithHash = Contact & {
  phoneNumber: string
  hash: string
}

export default function ContactsList() {
  const [isInitializing, setIsInitializing] = useState(true)
  const [contacts, setContacts] = useState<ContactWithHash[]>([])
  const [statusByContactHash, setStatusByContactHash] =
    useState<StatusByContactHash>({})
  const { session } = useSession()

  const refreshContacts = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync()
      if (status === "granted") {
        const { data: rawContacts } = (await Contacts.getContactsAsync({
          fields: [
            Contacts.Fields.PhoneNumbers,
            Contacts.Fields.Image,
            Contacts.Fields.ImageAvailable,
          ],
        })) as { data: ContactPossiblyWithoutName[] }

        const contacts = rawContacts.filter((c) => !!c.name) as Contact[]
        const contactsWithHashesPromises = contacts
          .filter((contact) => contact.phoneNumbers?.length)
          .map(async (contact) => {
            const number = contact.phoneNumbers![0].number!
            const normalizedNumber = normalizePhoneNumber(number)
            const hash = await sha256(normalizedNumber)
            return { ...contact, phoneNumber: normalizedNumber, hash }
          })

        const contactsWithHashes = await Promise.all(contactsWithHashesPromises)
        setContacts(contactsWithHashes)
      }
    } catch (error) {
      console.error("Error fetching contacts", error)
    }
  }

  const refreshContactStatuses = async () => {
    if (contacts.length === 0) {
      return
    }

    try {
      const statusByContactHash = await checkContactStatuses({
        token: session!.token,
        phoneHashes: contacts.map((c) => c.hash),
      })
      setStatusByContactHash(statusByContactHash)
    } catch (error) {
      console.error("Error checking contact statuses", error)
    }

    if (isInitializing) {
      setIsInitializing(false)
    }
  }

  useEffect(() => {
    refreshContacts()
  }, [])

  useEffect(() => {
    refreshContactStatuses()
  }, [contacts])

  const handleSendRequest = async (contact: ContactWithHash) => {
    await createConnectRequests({
      token: session!.token,
      phoneNumbers: [contact.phoneNumber],
    })

    await refreshContactStatuses()
  }

  const handleSendSmsRequest = async (contacts: ContactWithHash[]) => {
    const isAvailable = await SMS.isAvailableAsync()

    if (isAvailable) {
      const phoneNumbers = contacts
        .map((contact) => contact.phoneNumber)
        .filter((phoneNumber) => !!phoneNumber) as string[]

      const { result } = await SMS.sendSMSAsync(
        phoneNumbers,
        `Hey, I just invited you to InPress, the first news-based dating and friends app, so that we can see our unique shared interests. Create an account so we can found out what they are! https://inpress.app/`,
      )

      if (result === "sent") {
        await createConnectRequests({ token: session!.token, phoneNumbers })
        await refreshContactStatuses()
      }
    } else {
      console.log("SMS is not available on this device")
    }
  }

  if (isInitializing) {
    return <Loader errorTag="loading contact statuses" />
  }

  return (
    <ContactsList_
      contacts={contacts}
      latestStatusesByHash={statusByContactHash}
      onSendRequest={handleSendRequest}
      onSendSmsRequest={handleSendSmsRequest}
    />
  )
}

interface ContactsListProps_ {
  contacts: ContactWithHash[]
  latestStatusesByHash: StatusByContactHash
  onSendRequest: (contact: ContactWithHash) => void
  onSendSmsRequest: (contacts: ContactWithHash[]) => void
}
const ContactsList_ = ({
  contacts,
  latestStatusesByHash,
  onSendRequest,
  onSendSmsRequest,
}: ContactsListProps_) => {
  const [statusesByHash, setStatusesByHash] =
    useState<StatusByContactHash>(latestStatusesByHash)
  const [selectedContacts, setSelectedContacts] = useState<ContactWithHash[]>(
    [],
  )
  const [searchQuery, setSearchQuery] = useState("")
  const filteredContactsWithHashes = useMemo(
    () =>
      contacts.filter((c) => {
        const lowerCaseQuery = searchQuery.toLowerCase()

        return (
          c.name.toLowerCase().includes(lowerCaseQuery) ||
          c.phoneNumber.includes(searchQuery)
        )
      }),
    [contacts, searchQuery],
  )

  useEffect(() => {
    setStatusesByHash(latestStatusesByHash)
  }, [latestStatusesByHash])

  const [existingContacts, nonExistingContacts] = useMemo(
    () =>
      _.partition(filteredContactsWithHashes, (contact) => {
        const status = statusesByHash[contact.hash]
        return status?.showAsExisting
      }),
    [filteredContactsWithHashes, statusesByHash],
  )

  const toggleContactSelection = (contact: ContactWithHash) => {
    if (selectedContacts.includes(contact)) {
      setSelectedContacts(selectedContacts.filter((c) => c !== contact))
    } else {
      setSelectedContacts([...selectedContacts, contact])
    }
  }

  const renderContact = (contact: ContactWithHash) => {
    const status = statusesByHash[contact.hash]
    let buttonText

    if (status?.connected) {
      buttonText = "Connected"
    } else if (status?.requestSent) {
      buttonText = "Request sent"
    } else {
      buttonText = selectedContacts.includes(contact)
        ? "Selected"
        : "Send request"
    }

    let buttonHandler

    if (!status?.connected && !status?.requestSent) {
      if (status?.showAsExisting) {
        buttonHandler = () => {
          setStatusesByHash((prev) => ({
            ...prev,
            [contact.hash]: { ...prev[contact.hash], requestSent: true },
          }))
          onSendRequest(contact)
        }
      } else {
        buttonHandler = () => toggleContactSelection(contact)
      }
    }

    return (
      <ContactsListItem
        key={`${contact.id}-${contact.hash}`}
        contact={contact}
        buttonText={buttonText}
        isSelected={selectedContacts.includes(contact)}
        onPress={buttonHandler}
      />
    )
  }

  return (
    <View style={styles.container}>
      <Searchbar
        value={searchQuery}
        onChangeText={setSearchQuery}
        style={{ backgroundColor: "white" }}
        selectionColor="gray"
      />
      <ScrollView style={styles.contactsContainer}>
        {existingContacts.length > 0 && (
          <View>
            <Text style={styles.header}>Contacts on InPress</Text>
            <View style={styles.contactList}>
              {existingContacts.map(renderContact)}
            </View>
          </View>
        )}
        {nonExistingContacts.length > 0 && (
          <View>
            <Text style={styles.header}>Invite your contacts</Text>
            <View style={styles.contactList}>
              {nonExistingContacts.map(renderContact)}
              <View style={{ height: 60 }} />
            </View>
          </View>
        )}
        {filteredContactsWithHashes.length === 0 && (
          <Text style={styles.noContactsText}>No contacts found</Text>
        )}
      </ScrollView>

      <Button
        style={styles.submitButton}
        text={
          selectedContacts.length > 0
            ? `Invite ${selectedContacts.length} contact(s)`
            : "Add contacts to invite"
        }
        disabled={selectedContacts.length === 0}
        onPress={() => onSendSmsRequest(selectedContacts)}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: { gap: 6, paddingVertical: 12 },
  contactsContainer: { height: "93%" },
  header: { fontSize: 14, paddingTop: 12, fontWeight: "600" },
  contactList: { gap: 12, paddingVertical: 12 },
  submitButton: { position: "absolute", bottom: 20, width: "100%" },
  noContactsText: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginTop: 16,
  },
})

export { ContactWithHash, ContactsList, ContactsList_ }
