import { NewLikeNotification } from "@/apiQueries/notificationTypes"
import { Image } from "expo-image"
import { Platform } from "react-native"
import { GenericNotificationItem } from "./GenericNotificationItem"
import { HandleNavigateParams } from "../NotificationFeed"
import { BoldText, NormalText } from "@/components/StyledText"
import { styles } from "../notificationStyles"
import { leadsPath } from "@/utils/deepLinks"

export function NewLikeNotificationItem({
  item,
  onNavigate,
}: {
  item: NewLikeNotification
  onNavigate: (params: HandleNavigateParams) => void
}) {
  const handleOpenLeads = () => {
    onNavigate({
      href: { pathname: leadsPath },
      connectionMode: item.connectionMode,
    })
  }

  const picBlurRadius = Platform.OS === "ios" ? 85 : 5
  const placeholderBlurRadius = Platform.OS === "ios" ? 5 : 1

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={
        item.user ? (
          <Image
            source={item.user.image.url}
            contentFit="cover"
            contentPosition={"center"}
            style={styles.image}
            blurRadius={picBlurRadius}
          />
        ) : (
          <Image
            source={require("../../../assets/images/leads.png")}
            contentFit="contain"
            contentPosition={"center"}
            style={styles.imageWithoutBorderRadius}
            blurRadius={placeholderBlurRadius}
          />
        )
      }
      TextComponent={
        <NormalText>
          <BoldText>Someone is interested in you!</BoldText> Open your leads to
          see if you match
        </NormalText>
      }
      primaryButton={{
        text: "View Leads",
        onPress: handleOpenLeads,
      }}
    />
  )
}
