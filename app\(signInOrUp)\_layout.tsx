import { Stack } from "expo-router"

export default function SignInOrUp() {
  return (
    <Stack
      screenOptions={{
        headerBackTitle: "Back",
        headerTitle: "",
        headerTransparent: true,
      }}
    >
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="signup" options={{ headerShown: false }} />
      <Stack.Screen name="activate-mode" options={{ headerShown: false }} />
    </Stack>
  )
}
