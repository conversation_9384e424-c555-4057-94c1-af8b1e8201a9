import { CheckboxRow } from "@/components/CheckboxRow"
import { Screen } from "@/components/Themed"
import { Text } from "react-native"

export default function Story() {
  return (
    <Screen style={{ gap: 16 }}>
      <Text>Min checked 1</Text>
      <CheckboxRow
        options={[
          { value: "male", label: "Male" },
          { value: "female", label: "Female" },
          { value: "non-binary", label: "Non-binary" },
        ]}
        minChecked={1}
        onChange={(checkedValues) => console.log(checkedValues)}
      />
      <Text>Max checked 2</Text>
      <CheckboxRow
        options={[
          { value: "male", label: "Male" },
          { value: "female", label: "Female" },
          { value: "non-binary", label: "Non-binary" },
        ]}
        maxChecked={2}
        onChange={(checkedValues) => console.log(checkedValues)}
      />
      <Text>Max checked 1</Text>
      <CheckboxRow
        options={[
          { value: "male", label: "Male" },
          { value: "female", label: "Female" },
          { value: "non-binary", label: "Non-binary" },
        ]}
        maxChecked={1}
        onChange={(checkedValues) => console.log(checkedValues)}
      />
    </Screen>
  )
}
