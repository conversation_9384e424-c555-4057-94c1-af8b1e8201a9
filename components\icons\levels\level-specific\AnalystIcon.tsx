import Svg, { Path, SvgProps } from "react-native-svg"
export const AnalystIcon = (props: SvgProps) => (
  <Svg width={20} height={20} viewBox="0 0 24 24" fill="none">
    <Path
      d="M22.7913 20.9708L17.7025 15.8801C19.2283 13.8918 19.9406 11.3977 19.695 8.90357C19.4494 6.40946 18.2643 4.10214 16.38 2.44966C14.4957 0.797192 12.0533 -0.0766905 9.54838 0.00528705C7.04343 0.0872646 4.66345 1.11896 2.89123 2.8911C1.11902 4.66323 0.0872686 7.04311 0.00528729 9.54794C-0.076694 12.0528 0.797229 14.495 2.44978 16.3792C4.10233 18.2634 6.40975 19.4485 8.90398 19.6941C11.3982 19.9397 13.8925 19.2274 15.8808 17.7016L20.974 22.7956C21.0936 22.9152 21.2356 23.0101 21.3919 23.0748C21.5481 23.1396 21.7156 23.1729 21.8848 23.1729C22.0539 23.1729 22.2214 23.1396 22.3777 23.0748C22.534 23.0101 22.676 22.9152 22.7956 22.7956C22.9152 22.676 23.0101 22.534 23.0748 22.3778C23.1396 22.2215 23.1729 22.054 23.1729 21.8848C23.1729 21.7157 23.1396 21.5482 23.0748 21.3919C23.0101 21.2357 22.9152 21.0937 22.7956 20.9741L22.7913 20.9708ZM2.59338 9.87957C2.59338 8.43848 3.02073 7.02974 3.8214 5.83151C4.62207 4.63329 5.76009 3.69938 7.09155 3.1479C8.42301 2.59641 9.88811 2.45212 11.3016 2.73326C12.7151 3.01441 14.0134 3.70836 15.0325 4.72737C16.0515 5.74638 16.7455 7.04468 17.0267 8.45808C17.3078 9.87149 17.1635 11.3365 16.612 12.6679C16.0605 13.9993 15.1266 15.1373 13.9283 15.9379C12.73 16.7386 11.3212 17.1659 9.88003 17.1659C7.9481 17.1639 6.09587 16.3956 4.72978 15.0296C3.3637 13.6636 2.59536 11.8114 2.59338 9.87957Z"
      fill={props.fill || "black"}
    />
  </Svg>
)
