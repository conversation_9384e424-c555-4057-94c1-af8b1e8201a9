import FontAwesome from "@expo/vector-icons/FontAwesome"
import {
  DarkTheme,
  DefaultTheme,
  Navigation<PERSON><PERSON>r,
  ThemeProvider,
} from "@react-navigation/native"
import { useFonts } from "expo-font"
import { Slot } from "expo-router"
import * as SplashScreen from "expo-splash-screen"
import { useEffect } from "react"
import { OverlayProvider } from "stream-chat-expo"

import { useColorScheme } from "@/components/useColorScheme"
import "react-native-gesture-handler"
import { GestureHandlerRootView } from "react-native-gesture-handler"
import { SessionProvider } from "@/ctx"
import {
  EventType,
  initTracking,
  enableTracking,
  trackEvent,
} from "@/utils/tracking"
import * as Sentry from "@sentry/react-native"
import { StatusBar } from "expo-status-bar"
import { TestModeProvider } from "@/utils/testModeCtx"
import { PostHogProvider } from "posthog-react-native"
import { posthog, shouldTrackWithPosthog } from "@/services/posthog"
import { TestingProvider } from "@/context/TestingContext"

initTracking()

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router"

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync()

function RootLayout() {
  const [fontsLoaded, fontLoadingError] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    "PPEditorialOld-Regular": require("../assets/fonts/PPEditorialOld-Regular.otf"),
    "PPEditorialOld-Italic": require("../assets/fonts/PPEditorialOld-Italic.otf"),
    "Inter-Regular": require("../assets/fonts/Inter-Regular.ttf"),
    "Inter-Medium": require("../assets/fonts/Inter-Medium.ttf"),
    "Inter-SemiBold": require("../assets/fonts/Inter-SemiBold.ttf"),
    "InterTight-Regular": require("../assets/fonts/InterTight-Regular.ttf"),
    "InterTight-Medium": require("../assets/fonts/InterTight-Medium.ttf"),
    "InterTight-SemiBold": require("../assets/fonts/InterTight-SemiBold.ttf"),
    ...FontAwesome.font,
  })

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (fontLoadingError) throw fontLoadingError
  }, [fontLoadingError])

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync()
    }
    trackEvent(EventType.LoadedApp)
  }, [fontsLoaded])

  if (!fontsLoaded) {
    return null
  }

  return (
    <PostHogProvider
      client={posthog}
      options={{ disabled: shouldTrackWithPosthog }}
    >
      <RootLayoutNav />
    </PostHogProvider>
  )
}

function RootLayoutNav() {
  const colorScheme = useColorScheme()

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <OverlayProvider>
        <ThemeProvider
          value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
        >
          <SessionProvider>
            <TestingProvider>
              <TestModeProvider>
                <Slot />
              </TestModeProvider>
            </TestingProvider>
          </SessionProvider>
        </ThemeProvider>
      </OverlayProvider>

      <StatusBar style={colorScheme === "dark" ? "light" : "dark"} />
    </GestureHandlerRootView>
  )
}

let finalExport: any

if (enableTracking) {
  finalExport = Sentry.wrap(RootLayout)
} else {
  finalExport = RootLayout
}

export default finalExport
