import {
  Scanned<PERSON>atch,
  ScannedMatchModal_,
  ScannedMatchModalProps_,
} from "@/components/leads/ScannedMatchModal"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { USER } from "../(account)/account"
import { router } from "expo-router"
import { User } from "@/types/user"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { LAST_VISITED_PATH_KEY } from "@/utils/localStorage"
import { LEADS } from "../leads/leads"

const otherUser: User = {
  ...USER,
  firstName: "Adam",
  images: [
    {
      id: 0,
      url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male4.jpg",
    },
  ],
  friendsModeIsActivated: true,
}

const scannedMatch = {
  thisUser: { ...USER, friendsModeIsActivated: true },
  otherUser,
  score: 0.8,
  topics: LEADS[0].topics,
  alreadyConnected: false,
} as ScannedMatch

const defaultProps: ScannedMatchModalProps_ = {
  visible: true,
  scannedMatch,
  connectionMode: ConnectionMode.Friends,
  onSendFriendRequest: () => console.log("Send friend request"),
  onClose: () => {
    AsyncStorage.removeItem(LAST_VISITED_PATH_KEY)
    router.navigate("/")
  },
}

export default function Story() {
  return <ScannedMatchModal_ {...defaultProps} />
}

export { scannedMatch, defaultProps }
