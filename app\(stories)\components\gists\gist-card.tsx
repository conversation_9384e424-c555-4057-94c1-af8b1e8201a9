import { GistCard } from "@/components/gists/GistCard"
import { Screen } from "@/components/Themed"
import { articles } from "../../(news)/feed/news"
import { Article } from "@/types/news"

export default function Story() {
  const article: Article = {
    ...articles[0],
    summaryPoints: [
      "This is a sample bullet point that summarizes the gist of the article.",
      "Another bullet point for the gist, which is a summary of the article.",
      "Yet another point to summarize the gist of the article, providing insights.",
    ],
  }

  return (
    <Screen>
      <GistCard
        article={article}
        isVisible={true}
        onTimerCompleted={() => {}}
      />
    </Screen>
  )
}
