import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_ARTICLES, NEWSFEED_PROPS } from "./news"
import _ from "lodash"
import { useNewsContext } from "@/context/NewsContext"
import { useEffect } from "react"

export default function Story() {
  const { setArticles } = useNewsContext()

  useEffect(() => {
    setArticles(
      NEWSFEED_ARTICLES.map((article) => ({
        ...article,
        isOpened: _.random(0, 1) === 1,
        isSurveyed: _.random(0, 1) === 1,
        gistRating: _.sample(["like", "dislike", "skip"]),
      })),
    )
  }, [setArticles])

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} />
    </Screen>
  )
}
