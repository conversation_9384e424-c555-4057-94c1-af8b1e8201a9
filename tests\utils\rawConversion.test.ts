import { RawArticle } from "@/types/news"
import { convertRawArticle, titleCase } from "@/utils/rawConversion"

describe("Raw conversion", () => {
  describe("titleCase", () => {
    it("capitalizes basic sentence", () => {
      expect(titleCase("the quick brown fox")).toBe("The Quick Brown Fox")
    })

    it("preserves special characters like &", () => {
      expect(titleCase("tom & jerry")).toBe("Tom & Jerry")
    })

    it("handles single word", () => {
      expect(titleCase("hello")).toBe("Hello")
    })

    it("trims and collapses multiple spaces", () => {
      expect(titleCase("  hello   world  ")).toBe("Hello World")
    })

    it("does not affect already-capitalized words", () => {
      expect(titleCase("Already Capitalized")).toBe("Already Capitalized")
    })

    it("does not affect acronyms", () => {
      expect(titleCase("NASA And FBI")).toBe("NASA And FBI")
    })

    it("does not affect acronyms with dots", () => {
      expect(titleCase("U.S.A. And F.B.I.")).toBe("U.S.A. And F.B.I.")
    })

    it("handles quotes", () => {
      const title =
        "What happened on the 'poop cruise'? All the gory details from Netflix's disgusting documentary"
      const expected =
        "What Happened On The 'Poop Cruise'? All The Gory Details From Netflix's Disgusting Documentary"
      expect(titleCase(title)).toBe(expected)
    })

    it("handles hyphens", () => {
      const title =
        "'It was a near-death experience': How a California town blocked dangerous mudflows"
      const expected =
        "'It Was A Near-Death Experience': How A California Town Blocked Dangerous Mudflows"
      expect(titleCase(title)).toBe(expected)
    })
  })

  describe("Article conversion", () => {
    it("converts raw article with HTML title", () => {
      const rawArticle: Partial<RawArticle> = {
        id: 1,
        title: "New Tom &amp; Jerry Movie",
      }
      const article = convertRawArticle(rawArticle as RawArticle)
      expect(article.title).toBe("New Tom & Jerry Movie")
    })
  })
})
