import dotenv from "dotenv"
dotenv.config({
  path: ".env.staging",
})

import { INPRESS_URL, PROD_URL } from "@/apiQueries/constants"

if (INPRESS_URL === PROD_URL) {
  throw new Error("DO NOT RUN THIS SCRIPT ON PRODUCTION")
}

import { NEW_PROFILE } from "@/apiQueries/testConstants"
import {
  getLeads,
  getMatches,
  getNewsArticles,
  postTrackingEvent,
  submitSurvey,
  swipeLead,
} from "@/apiQueries/apiQueries"
import _ from "lodash"
import { register } from "@/apiQueries/registerUser"
import { login } from "@/apiQueries/auth"

const TRACKING_TOKEN = process.env.TRACKING_TOKEN

if (!TRACKING_TOKEN) {
  throw new Error("Missing tracking token")
}

const DELAY = 5000

const EVENT = {
  anonymous_user_id: "test",
  os_name: "test",
  os_version: "test",
  device_id: "test",
  device_brand: null,
  device_model: null,
  locale: "en-US",
  app_version: 0,
}

const wait = async (ms: number) =>
  new Promise((resolve) => {
    setTimeout(resolve, ms - (Math.random() * ms) / 2)
  })

async function simulateUserJourney(runId: number) {
  const log = (message: string) => {
    console.log(`[Run #${runId}] ${message}`)
  }

  log(`Starting!`)
  try {
    const randomId = Math.floor(Math.random() * 1000000)
    const email = `scale-test-${randomId}@inpress.com`

    const newAccount = {
      ...NEW_PROFILE,
      imageIds: [24, 25, 26],
      email,
    }

    await wait(DELAY)

    const { user, token } = await register(newAccount)
    log(`Registered as ${user.id}`)

    await wait(DELAY)
    const session = await login({ email, password: "password" })
    log(`Logged in as ${session.user.id}`)

    await postTrackingEvent(
      { ...EVENT, type: "LoadedApp" },
      TRACKING_TOKEN as string,
    )

    await wait(DELAY)
    const articles = await getNewsArticles(session.token)
    log(`Got ${articles.length} articles`)

    const articlesToSurvey = _.sampleSize(articles, 5)
    for await (const article of articlesToSurvey) {
      await wait(20000)
      await postTrackingEvent(
        {
          ...EVENT,
          type: "ViewedArticle",
          opened_article_id: article.id,
        },
        TRACKING_TOKEN as string,
      )
      log(`Surveying article ${article.id}`)
      await submitSurvey({
        token: session.token,
        articleId: article.id,
        survey: {
          feelings: _.sampleSize(
            ["Happy", "Sad", "Intrigued", "Excited", "Inspired"],
            3,
          ),
          importanceRating: Math.floor(Math.random() * 5) + 1,
          interestRating: Math.floor(Math.random() * 5) + 1,
        },
      })
    }

    await wait(DELAY)
    const { leads } = await getLeads({
      token: session.token,
      connectionMode: "dates" as any,
    })
    log(`Got ${leads.length} leads`)

    for await (const lead of leads) {
      await wait(DELAY)
      await swipeLead({
        token: session.token,
        swipe: {
          swipedUserId: lead.user.id,
          type: "like",
        },
        connectionMode: "dates" as any,
      })
    }

    await wait(DELAY)
    const matches = await getMatches({ token, connectionMode: "dates" as any })
    log(`Got ${matches.length} matches`)
  } catch (error) {
    console.error(error)
    log("Error!")
    log(error as string)
  }
  log("Done!")
}

async function simulateMultipleUserJourneys() {
  const journeyDelay = 1000
  for await (const runId of _.range(60000 / journeyDelay)) {
    simulateUserJourney(runId)
    await wait(journeyDelay)
  }
}

simulateMultipleUserJourneys()
