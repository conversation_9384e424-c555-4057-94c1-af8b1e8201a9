import { useEffect, useState, useCallback } from "react"
import { Platform } from "react-native"
import AsyncStorage from "@react-native-async-storage/async-storage"

export enum StorageKey {
  Features = "features",
  SwipeHintShown = "swipeHintShown",
}

export function usePersistentState<T>(
  key: StorageKey,
  defaultValue: T,
): [T, (value: T | ((prev: T) => T)) => void, boolean] {
  const [value, setValue] = useState<T>(defaultValue)
  const [hydrated, setHydrated] = useState(false)

  useEffect(() => {
    const load = async () => {
      try {
        const stored =
          Platform.OS === "web"
            ? localStorage.getItem(key)
            : await AsyncStorage.getItem(key)

        if (stored != null) {
          setValue(JSON.parse(stored))
        }
      } catch (e) {
        console.error("Failed to load from storage", e)
      } finally {
        setHydrated(true)
      }
    }

    load()
  }, [key])

  const setStateAndStorage = useCallback(
    (val: T | ((prev: T) => T)) => {
      const newValue =
        typeof val === "function" ? (val as (prev: T) => T)(value) : val
      setValue(newValue)
      try {
        const stringified = JSON.stringify(newValue)

        if (Platform.OS === "web") {
          localStorage.setItem(key, stringified)
        } else {
          AsyncStorage.setItem(key, stringified)
        }
      } catch (e) {
        console.error("Failed to save to storage", e)
      }
    },
    [key, value],
  )

  return [value, setStateAndStorage, hydrated]
}
