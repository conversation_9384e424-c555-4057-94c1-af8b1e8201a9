import { get, post } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { RawNotification, Notification } from "./notificationTypes"
import { convertRawUserPreview } from "@/types/user"
import { convertRawLevel } from "@/types/levels"
import { isImpersonating } from "@/utils/general"

type GetNotificationsResponse = {
  data: RawNotification[]
}

export const convertRawNotification = (
  rawNotification: RawNotification,
): Notification => {
  const {
    is_read,
    created_at,
    user,
    article_id,
    article_url,
    in_app_destination,
    external_url,
    connection_mode,
    connect_request,
    chat_channel,
    level,
    next_level,
    points_needed,
    streak_type_to_hit,
    reward_points,
    streak_type,
    points_earned,
    ratings_count,
    ...otherFields
  } = rawNotification

  let notification = {
    isRead: is_read,
    createdAt: created_at,
    articleId: article_id,
    articleUrl: article_url,
    inAppDestination: in_app_destination,
    externalUrl: external_url,
    connectionMode: connection_mode,
    pointsNeeded: points_needed,
    streakTypeToHit: streak_type_to_hit,
    rewardPoints: reward_points,
    streakType: streak_type,
    pointsEarned: points_earned,
    ratingsCount: ratings_count,
    ...otherFields,
  } as any

  if (user) {
    notification.user = {
      ...convertRawUserPreview(user),
      lastName: user.last_name,
      isArchived: user.is_archived,
    }
  }

  if (connect_request) {
    notification.connectRequest = {
      ...connect_request,
      connectionMode: connect_request.connection_mode,
    }
  }

  if (chat_channel) {
    notification.chatChannel = {
      streamId: chat_channel.stream_id,
    }
  }

  if (level) {
    notification.level = convertRawLevel(level)
  }

  if (next_level) {
    notification.nextLevel = convertRawLevel(next_level)
  }

  return notification
}

type GetNotificationsParams = {
  include_new_message: boolean
}

export const getNotifications = async (token: string) => {
  const { data: notifications } = await get<
    GetNotificationsParams,
    GetNotificationsResponse
  >(`${INPRESS_API_URL}/notifications`, token, {
    // Backwards compatibility (BC-8)
    include_new_message: true,
  })

  return notifications.map(convertRawNotification)
}

export const markNotificationsAsRead = async (ids: string[], token: string) => {
  if (isImpersonating) return

  await post<{ ids: string[] }, null>(
    `${INPRESS_API_URL}/notifications/read`,
    { ids },
    token,
  )
}

export const markAllNewMessagesAsRead = async (token: string) => {
  if (isImpersonating) return

  await post<null, null>(
    `${INPRESS_API_URL}/notifications/mark_new_messages_as_read`,
    null,
    token,
  )
}
