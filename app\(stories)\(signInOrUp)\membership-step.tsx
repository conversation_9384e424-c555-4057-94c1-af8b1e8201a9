import { MembershipStep_ } from "@/components/signInOrUp/MembershipStep"
import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { useState } from "react"
import { PurchasesPackage } from "react-native-purchases"

export const PLANS = [
  {
    identifier: "monthly",
    product: {
      price: 9.99,
      subscriptionPeriod: "P1M",
    },
  },
  {
    identifier: "yearly",
    product: {
      price: 99.99,
      subscriptionPeriod: "P1Y",
    },
  },
] as PurchasesPackage[]

export default function MembershipStepStory() {
  const [selectedPlan, setSelectedPlan] = useState<PurchasesPackage>()

  return (
    <SignUpWrapper
      title="Get more with Premium"
      subtitle="Earn more matches and access more news."
    >
      <MembershipStep_
        plans={PLANS}
        selectedPlan={selectedPlan}
        onSelectPlan={(plan) => setSelectedPlan(plan)}
        onConfirmPlan={() => console.log("onConfirmPlan")}
        onSkip={() => console.log("onSkip")}
      />
    </SignUpWrapper>
  )
}
