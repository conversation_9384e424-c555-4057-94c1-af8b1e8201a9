import { GistCard } from "@/components/gists/GistCard"
import { Screen } from "@/components/Themed"
import { articles } from "../../(news)/feed/news"
import { Article } from "@/types/news"

export default function Story() {
  const article: Article = {
    ...articles[0],
    summaryPoints: null,
  }

  return (
    <Screen>
      <GistCard
        article={article}
        isVisible={true}
        onTimerCompleted={() => {}}
      />
    </Screen>
  )
}
