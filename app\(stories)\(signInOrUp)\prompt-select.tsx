import SelectPrompt from "@/components/signInOrUp/promptStep/SelectPrompt"
import { Prompt } from "@/components/signInOrUp/SignUp"

export const promptChoices: Prompt[] = [
  {
    id: 1,
    text: "What is your favorite color?",
  },
  {
    id: 2,
    text: "Who's your favorite hero?",
  },
  {
    id: 3,
    text: "On a Sunday morning, I am...",
  },
]

export default function Story() {
  return (
    <SelectPrompt
      prompts={promptChoices}
      selectedPrompts={[]}
      onPressPrompt={(prompt) => {
        console.log(prompt)
      }}
    />
  )
}
