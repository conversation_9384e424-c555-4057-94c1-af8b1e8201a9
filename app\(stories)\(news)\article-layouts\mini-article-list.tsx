import { Screen } from "@/components/Themed"
import { articles } from "../feed/news"
import MiniArticleList from "@/components/news/MiniArticleList"

export default function Story() {
  const states = [
    { isOpened: false, isSurveyed: false },
    { isOpened: true, isSurveyed: false },
    { isOpened: true, isSurveyed: true },
  ]

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <MiniArticleList
        articles={articles.map((a, i) => ({
          ...a,
          ...states[i % states.length],
          gistRating: "like",
        }))}
      />
    </Screen>
  )
}
