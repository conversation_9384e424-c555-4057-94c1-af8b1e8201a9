import {
  sectionGroups as sectionGroups_,
  Section,
  SectionGroup,
} from "@/components/news/constants"
import { Article } from "@/types/news"
import _ from "lodash"

export const isUnratedArticle = (article: Article): boolean =>
  !article.gistRating && !article.isSurveyed

export const sortArticlesStartingWithSection = ({
  articles,
  startSection,
  sectionGroups = sectionGroups_,
}: {
  articles: Article[]
  startSection: Section
  sectionGroups?: SectionGroup[]
}): Article[] => {
  const sections = sectionGroups.flatMap((group) => group.sections)

  const sectionIndex = sections.findIndex(
    (section) => section.name === startSection.name,
  )

  const earlierSections = sections.slice(0, sectionIndex)
  const laterSections = sections.slice(sectionIndex + 1)
  const sortedSections = [startSection, ...laterSections, ...earlierSections]

  const sortedArticles = _.sortBy(articles, (article) => {
    return article.position ?? article.id
  })

  return sortedSections.flatMap((section) => {
    return sortedArticles.filter(
      (article) => article.frontpageSection === section.name,
    )
  })
}

export const rebuildArticles = ({
  articles,
  startSection,
  shuffleModeOn,
  sectionGroups = sectionGroups_,
}: {
  articles: Article[]
  startSection: Section
  shuffleModeOn: boolean
  sectionGroups?: SectionGroup[]
}): Article[] => {
  if (articles.length === 0) {
    return []
  }

  let rebuiltArticles = articles

  if (shuffleModeOn) {
    const firstUnratedArticle = articles.find(isUnratedArticle)
    if (!firstUnratedArticle) return []
    const otherArticles = articles.filter(
      (a) => a.id !== firstUnratedArticle.id,
    )
    rebuiltArticles = [firstUnratedArticle, ..._.shuffle(otherArticles)]
  } else {
    rebuiltArticles = sortArticlesStartingWithSection({
      articles,
      startSection: startSection,
      sectionGroups,
    })
  }

  return rebuiltArticles
}
