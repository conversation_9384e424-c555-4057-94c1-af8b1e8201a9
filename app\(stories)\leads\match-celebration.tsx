import { But<PERSON> } from "@/components/Button"
import MatchCelebrationModal from "@/components/leads/MatchCelebrationModal"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { Screen } from "@/components/Themed"
import { useState } from "react"
import { USER } from "../(account)/account"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { User } from "@/types/user"

const OTHER_USER: User = {
  ...USER,
  firstName: "Jane",
  images: [
    {
      id: 0,
      url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-female1.jpg",
    },
  ],
}

export default function Story() {
  const [activeMode, setActiveMode] = useState<ActiveConnectionMode | null>(
    null,
  )

  return (
    <Screen>
      <Button
        text="Show dates mode celebration"
        onPress={() => setActiveMode(ConnectionMode.Dates)}
      />
      <Button
        text="Show friends mode celebration"
        onPress={() => setActiveMode(ConnectionMode.Friends)}
      />
      <MatchCelebrationModal
        visible={!!activeMode}
        matchProfileImageUrl={USER.images[0].url}
        currentUser={OTHER_USER}
        connectionMode={activeMode!}
        onGoToMatches={() => setActiveMode(null)}
        onClose={() => setActiveMode(null)}
      />
    </Screen>
  )
}
