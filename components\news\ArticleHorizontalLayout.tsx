import { ScrollView, StyleSheet, View } from "react-native"
import {
  SPOTLIGHT_ARTICLE_HEIGHT_FN,
  SpotlightArticleCard,
} from "./SpotlightArticleCard"
import {
  newsfeedMarginHorizontal,
  SPOTLIGHT_IMAGE_HEIGHT as SPOTLIGHT_IMAGE_HEIGHT,
} from "./constants"
import { ArticleMatchPreview } from "@/apiQueries/newsFeed"
import { MATCH_MOODS_HEIGHT } from "./MatchMoodsWidget"
import { isFeatureEnabled } from "@/utils/featureFlags"
import { openArticle } from "../ArticlePage"
import { Article } from "@/types/news"

interface ArticleHorizontalLayoutProps {
  articles: Article[]
  matchPreviews: ArticleMatchPreview[]
  hideMatchMoods?: boolean
}

export const ArticleHorizontalLayout = ({
  articles,
  matchPreviews,
  hideMatchMoods = false,
}: ArticleHorizontalLayoutProps) => {
  const handlePress = async (article: Article) => {
    openArticle(article)
  }

  return (
    <View
      style={[
        {
          height:
            HEIGHT_FN(isFeatureEnabled("new_read_rated_design")) +
            (hideMatchMoods ? 0 : MATCH_MOODS_HEIGHT),
        },
      ]}
    >
      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: newsfeedMarginHorizontal,
        }}
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={ARTICLE_WIDTH + ARTICLE_MARGIN_RIGHT}
        snapToAlignment="start"
        decelerationRate="fast"
      >
        {articles.map((article, i) => (
          <View style={styles.article} key={i}>
            <SpotlightArticleCard
              article={article}
              imageHeight={SPOTLIGHT_IMAGE_HEIGHT}
              matchPreview={matchPreviews.find(
                (data) => data.articleId === article.id,
              )}
              hideMatchMoods={hideMatchMoods}
              onPress={handlePress}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  )
}

export const HEIGHT_FN = (isNewReadRated: boolean) =>
  SPOTLIGHT_ARTICLE_HEIGHT_FN(isNewReadRated)

const ARTICLE_WIDTH = 276
const ARTICLE_MARGIN_RIGHT = 20

const styles = StyleSheet.create({
  article: {
    width: ARTICLE_WIDTH,
    marginRight: ARTICLE_MARGIN_RIGHT,
  },
})
