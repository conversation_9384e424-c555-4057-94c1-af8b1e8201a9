import { View } from "react-native"
import InScoreSection from "../leads/InScoreSection"
import { S3_URL } from "@/constants/Links"
import { Leaderboard } from "../ratings/Leaderboard"
import { LeaderboardUser } from "@/types/user"
import { LEVELS } from "./GistsStep"

export const TITLE = "Track your media literacy journey"
export const SUBTITLE =
  "Welcome to InScore - the first ever gamified news system. Rate articles to earn points."

export const InScoreStep = () => {
  const users: LeaderboardUser[] = [
    {
      id: 1,
      firstName: "<PERSON>",
      points: 230,
    },
    {
      id: 2,
      firstName: "<PERSON>",
      points: 220,
    },
    {
      id: 5,
      firstName: "<PERSON>",
      points: 185,
    },
  ].map((user, index) => ({
    ...user,
    rank: index + 1,
    image: {
      id: index,
      url: `${S3_URL}/static_assets/avatar_${user.firstName.toLowerCase()}.png`,
    },
  }))

  return (
    <View>
      <InScoreSection
        points={220}
        currentLevel={LEVELS[0]}
        nextLevel={LEVELS[1]}
        articlesRead={82}
        articlesRated={38}
        streakDays={4}
        hideStats
      />
      <Leaderboard users={users} currentUserId={999} />
    </View>
  )
}
