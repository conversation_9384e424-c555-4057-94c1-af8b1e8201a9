import { Feature, useTestingContext } from "@/context/TestingContext"
import { StyleSheet, TouchableOpacity, View, Text } from "react-native"
import { Image } from "expo-image"
import { useState } from "react"

export const GISTS_MESSAGE =
  "Gists is now enabled! 🎉\nThanks for being a beta tester!"

export const SecretlyTappableLogo = () => {
  const { featureIsOn, toggleFeature } = useTestingContext()
  const [tapCount, setTapCount] = useState(0)

  const handlePress = () => {
    setTapCount((prevCount) => prevCount + 1)
    if (tapCount >= 8) {
      alert(GISTS_MESSAGE)
      if (!featureIsOn(Feature.Gists)) {
      } else {
        alert("Gists is now disabled")
      }
      toggleFeature(Feature.Gists)
      setTapCount(0)
    }
  }

  return (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={1}
      onPress={handlePress}
    >
      <Image
        source={require("../../app/assets/Logo.png")}
        style={styles.logo}
      />
      <View style={styles.titleContainer}>
        <Text style={styles.title}>Stay up to date</Text>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
  logo: {
    width: 199,
    height: 54,
    resizeMode: "contain",
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginTop: 20,
  },
  title: {
    fontSize: 24,
    color: "white",
  },
})
