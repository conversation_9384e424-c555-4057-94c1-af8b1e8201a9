import Colors from "@/constants/Colors"
import { TabButton } from "@/components/widgets/TabButton"
import { useState, useEffect, useMemo } from "react"
import {
  Alert,
  View,
  ActivityIndicator,
  StyleSheet,
  Platform,
} from "react-native"
import { SignU<PERSON><PERSON>rapper, SignUpWrapperPassedProps } from "../SignUpWrapper"
import FillPrompt from "./FillPrompt"
import { Responses } from "./Responses"
import _ from "lodash"
import { Prompt } from "../SignUp"
import SelectPrompt from "./SelectPrompt"
import { MIN_PROMPTS } from "../constants"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { isCompleteResponse } from "./utils"
import { ScoopResponse } from "@/types/user"

type ScreenType = "responses" | "selectPrompt" | "writeResponse"
const RESPONSES_SCREEN = "responses"
const SELECT_PROMPT_SCREEN = "selectPrompt"
const WRITE_RESPONSE_SCREEN = "writeResponse"

const MAX_PROMPTS = 3

export enum UsageContext {
  SignUp = "signUp",
  EditProfile = "editProfile",
}

export interface NewResponse extends Partial<ScoopResponse> {
  position: number
}

export interface PromptStepProps_ extends SignUpWrapperPassedProps {
  initialResponses: ScoopResponse[]
  promptChoices: Prompt[]
  usageContext?: UsageContext
  onBack: () => void
  onNext?: () => void
  onChange: (responses: ScoopResponse[]) => void
}

const PromptStep_ = ({
  initialResponses,
  promptChoices,
  usageContext = UsageContext.SignUp,
  onBack,
  onNext,
  onChange,
}: PromptStepProps_) => {
  const { top } = useSafeAreaInsets()

  const randomizedPrompts = useMemo(
    () => _.sampleSize(promptChoices, 10),
    [promptChoices],
  )

  const [currentScreen, setCurrentScreen] =
    useState<ScreenType>(RESPONSES_SCREEN)

  const [responses, setResponses] = useState<NewResponse[]>(
    _.range(MAX_PROMPTS).map(
      (i) =>
        initialResponses.find((response) => response.position === i) ?? {
          position: i,
        },
    ),
  )

  const [responseBeingEdited, setResponseBeingEdited] =
    useState<NewResponse | null>(null)

  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const completeResponses = responses.filter(isCompleteResponse)
    onChange(completeResponses)
  }, [responses])

  const onPressResponse = (response: NewResponse) => {
    setResponseBeingEdited(response)
    if (isCompleteResponse(response)) {
      setCurrentScreen(WRITE_RESPONSE_SCREEN)
    } else {
      setCurrentScreen(SELECT_PROMPT_SCREEN)
    }
  }

  const handleEditResponse = () => {
    if (!responseBeingEdited) {
      return
    }
    const newPrompts = responses.map((item) => {
      if (item.position === responseBeingEdited.position) {
        return responseBeingEdited
      }
      return item
    })
    setResponses(newPrompts)
    setCurrentScreen(RESPONSES_SCREEN)
  }

  const clearPrompt = (index: number) => {
    const newPrompts = responses.map((item) => {
      if (item.position === index) {
        return { position: index }
      }
      return item
    })
    setResponses(newPrompts)
  }

  const renderLeftTabButton = () => {
    switch (currentScreen) {
      case RESPONSES_SCREEN:
        return usageContext === UsageContext.SignUp ? null : (
          <TabButton title="Back" style={styles.tabButton} onPress={onBack} />
        )
      case SELECT_PROMPT_SCREEN:
        return (
          <TabButton
            title="Cancel"
            style={styles.tabButton}
            onPress={() => {
              if (responseBeingEdited?.text) {
                setCurrentScreen(WRITE_RESPONSE_SCREEN)
              } else {
                setCurrentScreen(RESPONSES_SCREEN)
              }
            }}
          />
        )
      case WRITE_RESPONSE_SCREEN:
        return (
          <TabButton
            title="Cancel"
            style={styles.tabButton}
            onPress={() => {
              if (responseBeingEdited?.text) {
                setCurrentScreen(RESPONSES_SCREEN)
              } else {
                setCurrentScreen(SELECT_PROMPT_SCREEN)
              }
            }}
          />
        )
    }
  }

  const renderRightTabButton = () => {
    switch (currentScreen) {
      case WRITE_RESPONSE_SCREEN:
        return (
          <TabButton
            title="Done"
            style={styles.tabButton}
            isDisabled={
              !responseBeingEdited?.text || !responseBeingEdited?.text.length
            }
            onPress={() => {
              if (!responseBeingEdited?.text) {
                Alert.alert("Please enter your answer!")
                return
              }

              handleEditResponse()
            }}
          />
        )
    }
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case RESPONSES_SCREEN:
        return (
          <Responses
            responses={responses}
            onPressResponse={onPressResponse}
            clearPrompt={clearPrompt}
          />
        )
      case SELECT_PROMPT_SCREEN:
        if (!responseBeingEdited) {
          return null
        }
        return (
          <SelectPrompt
            prompts={randomizedPrompts}
            selectedPrompts={responses}
            onPressPrompt={(prompt) => {
              setResponseBeingEdited({
                ...responseBeingEdited,
                promptId: prompt.id,
                prompt: prompt.text,
              })
              setCurrentScreen(WRITE_RESPONSE_SCREEN)
            }}
          />
        )
      case WRITE_RESPONSE_SCREEN:
        if (!responseBeingEdited) {
          return null
        }
        return (
          <FillPrompt
            initialResponse={responseBeingEdited}
            onChangeResponse={(response) => setResponseBeingEdited(response)}
            onChangePrompt={() => setCurrentScreen(SELECT_PROMPT_SCREEN)}
          />
        )
    }
  }

  const headerMarginTop = Platform.OS === "android" ? 90 : 0

  return (
    <View style={[styles.container, { paddingTop: top }]}>
      <View style={styles.editHeader}>
        {renderLeftTabButton()}
        {renderRightTabButton()}
      </View>

      <SignUpWrapper
        title={
          usageContext === UsageContext.SignUp &&
          currentScreen === RESPONSES_SCREEN
            ? "Write your scoops"
            : ""
        }
        screenStyle={{ paddingTop: 30 }}
        submitIsDisabled={
          responses.filter(isCompleteResponse).length < MIN_PROMPTS
        }
        onBack={
          usageContext === UsageContext.SignUp &&
          currentScreen === RESPONSES_SCREEN
            ? onBack
            : undefined
        }
        onNext={
          usageContext === UsageContext.SignUp &&
          currentScreen === RESPONSES_SCREEN
            ? onNext
            : undefined
        }
      >
        {loading ? (
          <ActivityIndicator size="large" color={Colors.light.text} />
        ) : (
          renderScreen()
        )}
      </SignUpWrapper>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  editHeader: {
    paddingHorizontal: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 20,
    paddingBottom: 10,
  },
  tabButton: {
    paddingHorizontal: 20,
  },
})
export default PromptStep_
