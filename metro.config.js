// This replaces `const { getDefaultConfig } = require('expo/metro-config');`
const { getSentryExpoConfig } = require("@sentry/react-native/metro")

// This replaces `const config = getDefaultConfig(__dirname);`
const config = getSentryExpoConfig(__dirname)

config.resolver.resolveRequest = (context, moduleName, platform) => {
  if (platform === "web") {
    // List of native modules to blacklist for web
    const nativeModules = [
      "react-native-maps",
      "@react-native-async-storage/async-storage",
      "react-native-device-info",
      "expo-location",
      "expo-camera",
      "react-native-permissions",
      "expo-router",
    ]

    if (nativeModules.includes(moduleName)) {
      return {
        type: "empty",
      }
    }
  }

  return context.resolveRequest(context, moduleName, platform)
}

module.exports = config
