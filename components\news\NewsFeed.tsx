import { StyleSheet, View, FlatList, Text } from "react-native"
import { useEffect, useMemo, useRef, useState } from "react"
import Colors, { BEI<PERSON>, DARK_GREY } from "@/constants/Colors"
import {
  NAVBAR_HEIGHT,
  NewsNavBar,
  NAVBAR_HEIGHT_WITH_PADDING,
} from "./NewsNavBar"
import { Layout, newsfeedSections, Section } from "./constants"
import { ArticleListLayout, LIST_LAYOUT_HEIGHT_FN } from "./ArticleListLayout"
import { ArticleGridLayout } from "./ArticleGridLayout"
import {
  ArticleSpotlightLayout,
  HEIGHT_FN as SPOTLIGHT_LAYOUT_HEIGHT_FN,
} from "./ArticleSpotlightLayout"
import {
  ArticleHorizontalLayout,
  HEIGHT_FN as HORIZONTAL_LAYOUT_HEIGHT_FN,
} from "./ArticleHorizontalLayout"
import { GRID_LAYOUT_HEIGHT_FN } from "./ArticleGridLayout"
import { Screen } from "../Themed"
import ShareReminder from "./ShareReminder"
import { NewsFeedAlert, HEIGHT as ALERTS_HEIGHT } from "./AlertCard"
import { HEIGHT as SHARE_REMINDER_HEIGHT } from "./ShareReminder"
import AlertsLayout from "./AlertsLayout"
import { ArticleMatchPreview, Announcement } from "@/apiQueries/newsFeed"
import { MATCH_MOODS_HEIGHT } from "./MatchMoodsWidget"
import {
  HEIGHT as ANNOUNCEMENTS_HEIGHT,
  AnnouncementsLayout,
} from "./AnnouncementsLayout"
import { useFeatureFlag } from "@/utils/featureFlags"
import WelcomeDrawer from "./WelcomeDrawer"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { HAS_SEEN_WELCOME_POPUP_KEY as HAS_SEEN_WELCOME_KEY } from "@/utils/localStorage"
import {
  FullImageArticleLayout,
  HEIGHT as FULL_IMAGE_LAYOUT_HEIGHT,
} from "./FullImageArticleLayout"
import { NewsFeedFooter } from "./NewsFeedFooter"
import { Session } from "@/types/user"
import { useScrollToTop } from "@react-navigation/native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import MiniArticleList from "./MiniArticleList"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import StatsBar from "./statsBar/StatsBar"
import { HEIGHT as MINI_CARD_HEIGHT } from "./MiniArticleCard"
import { Article, NewsEventType } from "@/types/news"
import { useNewsContext } from "@/context/NewsContext"
import { LinearGradient } from "expo-linear-gradient"
import { Feature, useTestingContext } from "@/context/TestingContext"
import { trackEvent } from "@/utils/tracking"

const getSectionHeights = (layout: Layout, newReadRatedDesign: boolean) => {
  const sectionHeights: { [key: string]: number } = {
    alerts: ALERTS_HEIGHT,
    miniArticleList: MINI_CARD_HEIGHT,
    horizontal: HORIZONTAL_LAYOUT_HEIGHT_FN(newReadRatedDesign),
    list: LIST_LAYOUT_HEIGHT_FN(newReadRatedDesign),
    grid: GRID_LAYOUT_HEIGHT_FN(newReadRatedDesign),
    fullImage: FULL_IMAGE_LAYOUT_HEIGHT,
    spotlight: SPOTLIGHT_LAYOUT_HEIGHT_FN(newReadRatedDesign),
    announcements: ANNOUNCEMENTS_HEIGHT,
    share: SHARE_REMINDER_HEIGHT,
  }
  return sectionHeights[layout] || 0
}

const matchMoodSections = ["horizontal", "spotlight"]

const finishReadingEligible = (a: Article) =>
  a.gistRating === "like" && !a.isSurveyed

export interface NewsFeedProps {
  alerts: NewsFeedAlert[]
  announcements: Announcement[]
  matchPreviews: ArticleMatchPreview[]
  hideMatchMoods: boolean
  referralCode: string | null
  session: Session
  onCloseAlert: (alert: NewsFeedAlert) => void
}

const NewsFeed = ({
  alerts,
  announcements,
  matchPreviews,
  hideMatchMoods,
  referralCode,
  session,
  onCloseAlert,
}: NewsFeedProps) => {
  const { articles } = useNewsContext()
  const { featureIsOn } = useTestingContext()

  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")
  const moreNewsLayoutsFlag = useFeatureFlag("more_news_layouts")
  const gistsFlag = featureIsOn(Feature.Gists)

  const navBarRef = useRef<FlatList<any>>(null)
  const newsFeedRef = useRef<FlatList<any>>(null)
  const [showPopup, setShowPopup] = useState(false)
  const { points, isNewsOnly } = session.user
  const [visibleSectionIndex, setVisibleSectionIndex] = useState<any>(0)

  const { top } = useSafeAreaInsets()

  useScrollToTop(newsFeedRef)

  const sectionsWithContent = useMemo(() => {
    return newsfeedSections
      .filter(
        (section) =>
          (section.name === "alerts" && alerts.length) ||
          (section.name === "finishReading" &&
            articles.some(finishReadingEligible) &&
            gistsFlag) ||
          (section.name === "announcements" && announcements.length) ||
          articles.some((a) => a.frontpageSection === section.name) ||
          (section.name === "share" && referralCode),
      )
      .map((section) =>
        !moreNewsLayoutsFlag && section.layout === "fullImage"
          ? { ...section, layout: "grid" as Layout }
          : section,
      )
  }, [alerts, announcements, articles, referralCode, moreNewsLayoutsFlag])

  useEffect(() => {
    ;(async () => {
      const hasSeenWelcome = await AsyncStorage.getItem(HAS_SEEN_WELCOME_KEY)
      if (!hasSeenWelcome && points === 0) {
        setShowPopup(true)
        await AsyncStorage.setItem(HAS_SEEN_WELCOME_KEY, "true")
      }
    })()
  }, [])

  useEffect(() => {
    if (navBarRef.current) {
      navBarRef.current.scrollToIndex({
        index: visibleSectionIndex,
        animated: true,
      })
    }
  }, [visibleSectionIndex])

  useEffect(() => {
    if (articles.length === 0) trackEvent(NewsEventType.EmptyNewsFeedSeen)
  }, [articles.length])

  const autoScrolling = useRef(false)

  const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (!autoScrolling.current && viewableItems && viewableItems.length > 0) {
      const currentIndex = viewableItems[0].index
      setVisibleSectionIndex(currentIndex)
    }
  }).current

  const renderSection = ({
    section,
    articles,
    index,
  }: {
    section: Section
    articles: Article[]
    index: number
  }) => {
    const articlesMatchingSection = articles.filter(
      (a) =>
        a.frontpageSection === section.name ||
        (section.name === "finishReading" &&
          finishReadingEligible(a) &&
          gistsFlag),
    )

    let layoutComponent
    if (section.layout === "alerts" && alerts.length) {
      layoutComponent = (
        <AlertsLayout alerts={alerts} onCloseAlert={onCloseAlert} />
      )
    } else if (section.layout === "miniArticleList") {
      layoutComponent = <MiniArticleList articles={articlesMatchingSection} />
    } else if (section.name === "announcements" && announcements.length) {
      layoutComponent = <AnnouncementsLayout announcements={announcements} />
    } else if (section.layout === "horizontal") {
      layoutComponent = (
        <ArticleHorizontalLayout
          articles={articlesMatchingSection}
          matchPreviews={matchPreviews}
          hideMatchMoods={hideMatchMoods}
        />
      )
    } else if (section.layout === "list") {
      layoutComponent = <ArticleListLayout articles={articlesMatchingSection} />
    } else if (section.layout === "grid") {
      layoutComponent = <ArticleGridLayout articles={articlesMatchingSection} />
    } else if (section.layout === "fullImage") {
      layoutComponent = (
        <FullImageArticleLayout articles={articlesMatchingSection} />
      )
    } else if (section.layout === "spotlight") {
      layoutComponent = (
        <ArticleSpotlightLayout
          articles={articlesMatchingSection}
          matchPreviews={matchPreviews}
          hideMatchMoods={hideMatchMoods}
        />
      )
    } else if (section.layout === "share" && referralCode) {
      layoutComponent = <ShareReminder referralCode={referralCode} />
    }

    const marginTop = index === 0 ? FIRST_SECTION_MARGIN_TOP : 0
    return (
      <View style={styles.section}>
        <View style={styles.titleContainer}>
          <View style={[styles.titleContentContainer, { marginTop }]}>
            {section.name === "finishReading" && (
              <MaterialCommunityIcons
                name="lightning-bolt"
                size={hp(2.2)}
                color={DARK_GREY}
              />
            )}
            <Text style={styles.title}>{section.title}</Text>
          </View>
          {section.subtitle && (
            <Text style={styles.subtitle}> {section.subtitle} </Text>
          )}
        </View>
        {articlesMatchingSection.length > 0 || !section.isNews
          ? layoutComponent
          : null}

        <View
          style={index !== sectionsWithContent.length - 1 && styles.separator}
        />
      </View>
    )
  }

  const getItemLayout = (data: any, index: number) => {
    let offset = -5

    for (let i = 0; i < index; i++) {
      const section = sectionsWithContent[i]

      offset +=
        SECTION_TITLE_HEIGHT +
        (section.subtitle ? SECTION_SUBTITLE_HEIGHT : 0) +
        SEPARATOR_HEIGHT +
        getSectionHeights(section.layout, newReadRatedFlag) +
        SECTION_MARGIN_BOTTOM

      if (!hideMatchMoods && matchMoodSections.includes(section.layout)) {
        offset += MATCH_MOODS_HEIGHT
      }
    }

    const currentItem = sectionsWithContent[index]

    const length =
      getSectionHeights(currentItem.layout, newReadRatedFlag) +
      (!hideMatchMoods && matchMoodSections.includes(currentItem.layout)
        ? MATCH_MOODS_HEIGHT
        : 0)

    return {
      length,
      offset,
      index,
    }
  }

  return (
    <Screen style={styles.container}>
      <NewsNavBar
        sections={sectionsWithContent}
        selectedSectionIndex={visibleSectionIndex}
        points={points}
        flatlistRef={navBarRef}
        onSectionChange={(index) => {
          autoScrolling.current = true
          setVisibleSectionIndex(index)
          newsFeedRef.current?.scrollToIndex({
            index: index,
            animated: true,
          })
        }}
      />
      <View
        style={[
          styles.body,
          {
            top: gistsFlag ? NAVBAR_HEIGHT : top + NAVBAR_HEIGHT_WITH_PADDING,
          },
        ]}
      >
        {gistsFlag && <StatsBar />}
        <View style={{ position: "relative" }}>
          <LinearGradient
            colors={[BEIGE, `${BEIGE}00`]}
            locations={[0.2, 1]}
            style={styles.topGradient}
          />
          <FlatList
            ref={newsFeedRef}
            data={sectionsWithContent}
            keyExtractor={(item) => item.name}
            showsVerticalScrollIndicator={false}
            renderItem={({ item, index }) => {
              return (
                <>
                  {renderSection({ section: item, articles, index })}
                  {index === sectionsWithContent.length - 1 && (
                    <NewsFeedFooter isNewsOnly={isNewsOnly} />
                  )}
                </>
              )
            }}
            onScrollBeginDrag={() => {
              autoScrolling.current = false
            }}
            extraData={visibleSectionIndex}
            getItemLayout={getItemLayout}
            onViewableItemsChanged={onViewableItemsChanged}
          />
        </View>
      </View>
      {showPopup && (
        <WelcomeDrawer
          onClose={() => {
            setShowPopup(false)
          }}
        />
      )}
    </Screen>
  )
}

const FIRST_SECTION_MARGIN_TOP = 15
const SECTION_MARGIN_BOTTOM = 20
const SECTION_TITLE_HEIGHT = 35
const SECTION_SUBTITLE_HEIGHT = 40
const SEPARATOR_HEIGHT = 10

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
  },
  body: {
    position: "relative",
    backgroundColor: BEIGE,
  },
  welcome: {
    width: 250,
    marginLeft: 16,
  },
  topGradient: {
    height: 30,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  section: {
    marginBottom: SECTION_MARGIN_BOTTOM,
  },
  titleContainer: {
    marginLeft: 16,
  },
  titleContentContainer: {
    height: SECTION_TITLE_HEIGHT,
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  title: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 22,
    color: Colors.light.text,
  },
  separator: {
    borderBottomWidth: 1.2,
    borderColor: "rgba(0, 0, 0, 0.1)",
    height: SEPARATOR_HEIGHT,
  },
  subtitle: {
    fontFamily: "InterTight-Regular",
    fontSize: 12,
    fontWeight: "400",
    color: "gray",
    height: SECTION_SUBTITLE_HEIGHT,
  },
})

export { NewsFeed }
