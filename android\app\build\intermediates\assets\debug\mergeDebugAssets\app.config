{"name": "InPress", "slug": "inpress-expo-router", "version": "242.0.0", "orientation": "portrait", "icon": "./assets/images/inpress-icon-white-on-black.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "runtimeVersion": "1.0.0", "ios": {"supportsTablet": false, "buildNumber": "242", "bundleIdentifier": "com.scoopt.inpress", "infoPlist": {"NSLocationAlwaysAndWhenInUseUsageDescription": "InPress uses your location to deliver local news and match you with people in your area.", "NSLocationWhenInUseUsageDescription": "InPress uses your location to deliver local news and match you with people in your area.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": ["cstr6suwn9.skadnetwork"]}, {"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}]}}, "android": {"versionCode": 242, "adaptiveIcon": {"foregroundImage": "./assets/images/inpress-icon-white-on-black.png", "backgroundColor": "#ffffff"}, "package": "com.scoopt.inpress", "permissions": ["android.permission.ACCESS_FINE_LOCATION", "com.google.android.gms.permission.AD_ID", "android.permission.RECORD_AUDIO", "android.permission.CAMERA", "android.permission.INTERNET", "android.permission.ACCESS_COARSE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/inpress-icon-white-on-black.png"}, "plugins": ["expo-router", ["expo-font", {"fonts": ["assets/fonts/PPEditorialOld-Regular.otf", "assets/fonts/PPEditorialOld-Italic.otf", "assets/fonts/InterTight-Regular.ttf", "assets/fonts/InterTight-SemiBold.ttf"]}], ["@sentry/react-native/expo", {"organization": "inpress-1m", "project": "inpress-app"}], ["expo-image-picker", {"photosPermission": "InPress uses the photo library to allow you to select pictures for your profile."}], ["expo-camera", {"cameraPermission": "In<PERSON>ress uses your camera for taking pictures for your profile."}], "expo-localization", ["react-native-fbsdk-next", {"appID": "437278972477723", "clientToken": "37f42594766e72e5c2ee2aa86ec9f5c4", "displayName": "InPress", "autoLogAppEventsEnabled": true, "advertiserIDCollectionEnabled": true}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "InPress uses your location to deliver local news and match you with people in your area.", "locationWhenInUsePermission": "InPress uses your location to deliver local news and match you with people in your area."}], "react-native-appsflyer", "expo-tracking-transparency"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "6845f5f4-0431-47e1-9e84-d75f2cecde9b"}, "appsflyer": {"appId": "id6456752116"}}, "owner": "inpress", "sdkVersion": "52.0.0", "platforms": ["ios", "android", "web"]}