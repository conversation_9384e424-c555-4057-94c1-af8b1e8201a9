import { FlatList, StyleSheet, View } from "react-native"
import {
  VERTICAL_ARTICLE_CARD_HEIGHT_FN,
  VerticalArticleCard,
} from "./VerticalArticleCard"
import { gridArticleMargin, newsfeedMarginHorizontal } from "./constants"
import { useFeatureFlag } from "@/utils/featureFlags"
import { Article } from "@/types/news"

export const GRID_LAYOUT_HEIGHT_FN = (newReadRatedFlag: boolean) =>
  VERTICAL_ARTICLE_CARD_HEIGHT_FN(newReadRatedFlag) * 2 + 16

export const ArticleGridLayout = ({ articles }: { articles: Article[] }) => {
  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")
  return (
    <View
      style={[
        styles.container,
        { height: GRID_LAYOUT_HEIGHT_FN(newReadRatedFlag) },
      ]}
    >
      <FlatList
        data={articles}
        numColumns={2}
        renderItem={({ item }) => (
          <View style={styles.article}>
            <VerticalArticleCard article={item} />
          </View>
        )}
        ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
        keyExtractor={(item) => item.id.toString()}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: newsfeedMarginHorizontal - gridArticleMargin,
    alignItems: "center",
  },
  article: {
    marginHorizontal: gridArticleMargin,
  },
})
