import {
  getUser,
  updateProfile,
  uploadNewProfileImages,
} from "@/apiQueries/auth"
import { router } from "expo-router"
import _ from "lodash"
import { Alert } from "react-native"
import { ConnectionMode } from "./ConnectionModeStep"
import { DEFAULT_BIRTHDATE, FinishedNewProfile, SignUp_ } from "./SignUp"
import { useSession } from "@/ctx"
import { useEffect, useState } from "react"
import { EventType, trackEvent } from "@/utils/tracking"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { Loader } from "../widgets/Loader"
import { Account, isUserWithProfile, UnknownTypeUser } from "@/types/user"

interface ProfileUpdate
  extends Pick<
    FinishedNewProfile,
    | "biography"
    | "genderPrefs"
    | "minAgePref"
    | "maxAgePref"
    | "minSimilarityPref"
    | "maxSimilarityPref"
    | "images"
    | "scoopResponses"
  > {}

interface SocialActivation extends ProfileUpdate {
  datesModeIsActivated: boolean
  friendsModeIsActivated: boolean
}

type UnknownTypeUpdate = ProfileUpdate | SocialActivation

const ActivateConnectionMode = ({
  presetConnectionMode,
  isActivatingSocial,
}: {
  presetConnectionMode?: ActiveConnectionMode
  isActivatingSocial?: boolean
}) => {
  const { session, refreshSession } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [signUpError, setSignUpError] = useState<string>()
  const [prefilledData, setPrefilledData] = useState<UnknownTypeUser>()

  useEffect(() => {
    if (presetConnectionMode) {
      const prefilledMode =
        presetConnectionMode === ConnectionMode.Dates
          ? ConnectionMode.Friends
          : ConnectionMode.Dates

      getUser({
        token: session!.token,
        userId: session!.user.id,
        connectionMode: prefilledMode,
      })
        .then(setPrefilledData)
        .catch((e) => {
          console.error("Failed to load other mode's profile", e)
        })
    } else {
      setPrefilledData(session!.user)
    }
  }, [presetConnectionMode])

  const handleActivateMode = async ({
    connectionMode,
    userUpdate,
  }: {
    connectionMode: ActiveConnectionMode
    userUpdate: UnknownTypeUpdate
  }) => {
    try {
      setIsLoading(true)

      const imageIds = await uploadNewProfileImages({
        images: userUpdate.images,
        connectionMode,
      })

      await updateProfile({
        token: session!.token,
        userUpdate: {
          ...userUpdate,
          preferences: {
            genders: userUpdate.genderPrefs,
            minAge: userUpdate.minAgePref,
            maxAge: userUpdate.maxAgePref,
            minSimilarity: userUpdate.minSimilarityPref,
            maxSimilarity: userUpdate.maxSimilarityPref,
          },
          imageIds,
          datesModeIsActivated:
            connectionMode === ConnectionMode.Dates ? true : undefined,
          friendsModeIsActivated:
            connectionMode === ConnectionMode.Friends ? true : undefined,
        },
        connectionMode,
      })

      trackEvent(EventType.ModeActivated, { data: { connectionMode } })
      Alert.alert(`Your ${_.startCase(connectionMode)} profile is now active!`)
      await refreshSession(session!.token)
      router.push("/account")
    } catch (e: any) {
      trackEvent(EventType.ActivateModeFailed, {
        data: {
          error: e,
        },
      })

      try {
        const errorString = JSON.stringify(e)
        setSignUpError(errorString)
      } catch (e) {
        setSignUpError("An unknown error occurred")
      }
    }
    setIsLoading(false)
  }

  const handleActivateSocial = async (userUpdate: SocialActivation) =>
    handleActivateMode({
      connectionMode: userUpdate.datesModeIsActivated
        ? ConnectionMode.Dates
        : ConnectionMode.Friends,
      userUpdate,
    })

  const handleActivatePresetMode = async (userUpdate: ProfileUpdate) =>
    handleActivateMode({
      connectionMode: presetConnectionMode!,
      userUpdate,
    })

  const handleRegister = async (userUpdate: UnknownTypeUpdate) => {
    const isSocialActivation = (
      update: UnknownTypeUpdate,
      isActivatingSocial: boolean | undefined,
    ): update is SocialActivation => !!isActivatingSocial

    if (isSocialActivation(userUpdate, isActivatingSocial)) {
      await handleActivateSocial(userUpdate)
    } else {
      await handleActivatePresetMode(userUpdate)
    }
  }

  if (!prefilledData) return <Loader connectionMode={presetConnectionMode} />

  const defaultProfile: Partial<FinishedNewProfile> & {
    firstName: Account["firstName"]
  } = {
    firstName: session!.user.firstName,
    birthdate: DEFAULT_BIRTHDATE,
    genderPrefs: [],
    images: prefilledData.images,
    biography: isUserWithProfile(prefilledData) ? prefilledData.biography : "",
    scoopResponses: [],
  }

  return (
    <SignUp_<UnknownTypeUpdate>
      defaultProfile={defaultProfile}
      isLoading={isLoading}
      signUpError={signUpError}
      isActivatingSocial={isActivatingSocial}
      presetConnectionMode={presetConnectionMode}
      onRetry={() => setSignUpError(undefined)}
      handleRegister={handleRegister}
      handleTrackEvent={trackEvent}
    />
  )
}

export default ActivateConnectionMode
