import { RawArticle, Article } from "@/types/news"
import { decode } from "html-entities"
import _ from "lodash"

function capitalizeWordPreservingPunctuation(word: string): string {
  return word
    .split(/([-\/])/g) // keep separators like - and /
    .map(capitalizeSingleChunk)
    .join("")
}

function capitalizeSingleChunk(chunk: string): string {
  const match = chunk.match(/^(['"“‘(\[]?)(\w)(.*)/)
  if (!match) return chunk
  const [, lead, first, rest] = match
  return lead + first.toUpperCase() + rest
}

export function titleCase(str: string): string {
  return str
    .trim()
    .split(/\s+/)
    .map(capitalizeWordPreservingPunctuation)
    .join(" ")
}

export const convertRawArticle = (article: RawArticle): Article => {
  let cleanTitle = article.title
  try {
    cleanTitle = decode(article.title)
  } catch (error) {
    console.error("Failed to decode article title:", error)
  }

  cleanTitle = titleCase(cleanTitle)

  return {
    ...article,
    title: cleanTitle,
    publishedAt: article.published_at,
    imageUrl: article.image_url,
    frontpageSection: article.frontpage_section,
    faviconUrl: article.favicon_url,
    summaryPoints: article.summary_points,
    isOpened: article.is_opened,
    isSurveyed: article.is_surveyed,
    gistRating: article.gist_rating,
  }
}
