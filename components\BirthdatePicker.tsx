import moment from "moment"
import { DatePicker, DatePickerProps } from "./DatePicker"
import { Text } from "./Themed"
import { StyleSheet } from "react-native"

export const BirthdatePicker = (props: DatePickerProps) => {
  const { value } = props
  const today = new Date()
  const maxDate = new Date(
    today.getFullYear() - 18,
    today.getMonth(),
    today.getDate(),
  )
  return (
    <>
      <DatePicker {...props} maximumDate={maxDate} />
      <Text style={{ marginTop: 5, textAlign: "center" }}>
        {moment(moment()).diff(value, "years")} years old
      </Text>
      <Text style={styles.birthDayDisclaimer}>
        Your age will be visible on your profile, but your date of birth will
        not. Your DOB remains private and never visible to others.
      </Text>
    </>
  )
}

const styles = StyleSheet.create({
  birthDayDisclaimer: {
    textAlign: "center",
    marginTop: 10,
    color: "#000000b3",
  },
})
