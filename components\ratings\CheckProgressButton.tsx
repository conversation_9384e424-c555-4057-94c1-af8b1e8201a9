import { Button } from "react-native-paper"
import JourneyIconSmall from "../icons/levels/JourneyIconSmall"
import { completionStepStyles } from "./styles"
import { router } from "expo-router"

export const CheckProgressButton = () => {
  return (
    <Button
      mode="contained"
      style={completionStepStyles.button}
      labelStyle={completionStepStyles.buttonText}
      contentStyle={completionStepStyles.buttonContent}
      icon={() => <JourneyIconSmall />}
      onPress={() => router.push(`/account/journey`)}
    >
      InScore Progress
    </Button>
  )
}
