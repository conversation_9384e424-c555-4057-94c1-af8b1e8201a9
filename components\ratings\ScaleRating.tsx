import React, { useEffect, useState } from "react"
import { RatingRadioGroup } from "./RatingRadioGroup"

interface ScaleRatingProps {
  initialValue: number | undefined
  options: string[]
  onChange: (value: number) => void
}

export const ScaleRating = ({
  initialValue,
  options,
  onChange,
}: ScaleRatingProps) => {
  const [selectedRating, setSelectedRating] = useState<number | undefined>(
    initialValue,
  )

  useEffect(() => {
    if (selectedRating !== undefined) {
      onChange(selectedRating)
    }
  }, [selectedRating])

  const handlePress = (rating: number) => {
    setSelectedRating(rating)
  }

  return (
    <RatingRadioGroup
      ratingOptions={options}
      selectedRating={selectedRating}
      onRatingChange={handlePress}
    />
  )
}
