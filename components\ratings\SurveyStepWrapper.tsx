import { StyleSheet, TouchableOpacity, View } from "react-native"
import { Text } from "../Themed"
import BackArrowIcon from "../icons/BackArrowIcon"
import NextArrowIcon from "../icons/NextArrowIcon"
import { Button } from "../Button"

export interface SurveyStepProps {
  stepIndex: number
  totalSteps: number
  onBack?: () => void
  onNext: () => void
  onClose: () => void
}

export const SurveyStepWrapper = ({
  title,
  subtitle,
  stepIndex,
  totalSteps,
  children,
  onBack,
  nextIsDisabled,
  onNext,
  onClose,
}: {
  title?: string
  subtitle?: string
  stepIndex: number
  totalSteps: number
  children: React.ReactNode
  onBack?: () => void
  nextIsDisabled?: boolean
  onNext?: () => void
  onClose: () => void
}) => {
  return (
    <View style={styles.container}>
      {title && (
        <View style={styles.headerContainer}>
          <Text style={styles.title}>{title}</Text>
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </View>
      )}
      {children}
      {(onBack || onNext) && (
        <View style={styles.bottomContainer}>
          <View style={styles.dotsContainer}>
            {Array.from({ length: totalSteps }).map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === stepIndex ? styles.activeDot : styles.inactiveDot,
                ]}
              />
            ))}
          </View>
          <View
            style={{
              ...styles.navigationContainer,
              flexDirection: onBack && onNext ? "row" : "column",
            }}
          >
            {onBack && (
              <Button
                style={{
                  ...styles.buttonNext,
                  width: onNext ? "auto" : "100%",
                }}
                onPress={onBack}
                text="Back"
                iconComponent={
                  <BackArrowIcon width={7} height={13} fill={"#fff"} />
                }
              />
            )}
            <View style={{ flex: 1 }} />
            {onNext && (
              <Button
                style={{
                  ...styles.buttonNext,
                  width: onBack ? "auto" : "100%",
                  opacity: nextIsDisabled ? 0.15 : 1,
                }}
                onPress={onNext}
                disabled={nextIsDisabled}
                text={stepIndex === totalSteps - 1 ? "Complete" : "Next"}
                iconComponent={
                  <NextArrowIcon width={7} height={13} fill={"#fff"} />
                }
                isTextOnLeft
              />
            )}
          </View>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    height: "90%",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 16,
  },
  headerContainer: {
    alignItems: "center",
    paddingTop: 10,
    paddingBottom: 24,
  },
  title: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 19.5,
    paddingBottom: 4,
  },
  subtitle: {
    fontFamily: "InterTight-Regular",
    letterSpacing: 0.6,
    fontSize: 12,
  },
  bottomContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  navigationContainer: {
    paddingTop: 10,
    marginBottom: 30,
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
    paddingVertical: 12,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 5,
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: "black",
  },
  inactiveDot: {
    backgroundColor: "lightgray",
  },
  buttonNext: {
    flexDirection: "row",
    columnGap: 15,
    backgroundColor: "black",
    borderRadius: 50,
    paddingVertical: 15,
    paddingHorizontal: 30,
    elevation: 5,
    justifyContent: "center",
    alignItems: "center",
  },
  buttonNextText: {
    color: "white",
    fontFamily: "InterTight-SemiBold",
    fontSize: 16,
  },
})
