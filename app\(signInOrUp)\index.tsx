import { useEffect, useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from "react-native"
import { Link, router } from "expo-router"
import { <PERSON><PERSON> } from "@/components/Button"
import { Screen } from "@/components/Themed"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { IS_NEWS_ONLY_KEY, ONBOARDING_DATA_KEY } from "@/utils/localStorage"
import { openAppStore } from "@/utils/general"
import { signUpPath } from "@/utils/deepLinks"
import { BoldText, LargeText } from "@/components/StyledText"
import {
  checkVersionStatus,
  VersionStatus,
} from "@/apiQueries/checkVersionStatus"
import { UpdateRequired } from "@/screens/UpdateRequired"
import { PRIVACY_URL, TOS_URL } from "@/constants/Links"
import { initAppsFlyer } from "@/utils/tracking"
import { SecretlyTappableLogo } from "@/components/signInOrUp/SecretlyTappableLogo"

const InitialView = ({}) => {
  const [versionStatus, setVersionStatus] = useState<VersionStatus>()

  useEffect(() => {
    ;(async () => {
      try {
        const status = await checkVersionStatus()
        setVersionStatus(status)
      } catch (e) {
        console.error("Failed to check for new app version", e)
      }
    })()
  }, [])

  return <InitialView_ versionStatus={versionStatus} />
}

export const InitialView_ = ({
  versionStatus,
}: {
  versionStatus?: VersionStatus
}) => {
  const [adminTapCount, setAdminTapCount] = useState(0)

  const possiblyReroute = async () => {
    const onboardingData = await AsyncStorage.getItem(ONBOARDING_DATA_KEY)
    try {
      if (onboardingData) {
        router.navigate(signUpPath)
        return
      }
    } catch (e) {
      console.error(e)
      AsyncStorage.removeItem(ONBOARDING_DATA_KEY)
    }
  }

  useEffect(() => {
    initAppsFlyer()
  }, [])

  useEffect(() => {
    possiblyReroute()
  }, [])

  useEffect(() => {
    if (adminTapCount > 5) {
      router.push("/(signInOrUp)/admin")
    }
  }, [adminTapCount])

  const handlePressCreate = async ({
    isNewsOnlyStr,
  }: {
    isNewsOnlyStr: "true" | "false"
  }) => {
    await AsyncStorage.setItem(IS_NEWS_ONLY_KEY, isNewsOnlyStr)
    router.navigate(signUpPath)
  }

  const accountButtonProps = {
    style: { backgroundColor: "white" },
    textStyle: { color: "black" },
  }

  if (versionStatus === "update_required") {
    return <UpdateRequired />
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
      keyboardVerticalOffset={-75}
    >
      <Screen style={styles.container} statusBarStyle="light-content">
        <View
          style={styles.invisibleAdminBox}
          onTouchEnd={() => {
            setAdminTapCount(adminTapCount + 1)
          }}
        />
        <ImageBackground
          source={require("../assets/BackgroundIntro.png")}
          style={styles.backgroundImage}
        >
          <SecretlyTappableLogo />
          <View style={styles.bottomContainer}>
            <View style={styles.updateContainer}>
              {versionStatus === "update_available" && (
                <TouchableOpacity onPress={() => openAppStore()}>
                  <Text style={styles.updateAlert}>
                    A new version of the app is available! Please tap here to
                    open the app store and update.
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            <View style={{ rowGap: 12, marginBottom: 25 }}>
              <>
                <Text style={styles.createText}>Choose your experience:</Text>
                <Button
                  text="Just the News"
                  onPress={() => handlePressCreate({ isNewsOnlyStr: "true" })}
                  {...accountButtonProps}
                />
                <Button
                  text="News + Social"
                  onPress={() => handlePressCreate({ isNewsOnlyStr: "false" })}
                  {...accountButtonProps}
                />
              </>
            </View>
            <View style={styles.disclaimerContainer}>
              <LargeText style={styles.loginText}>
                Already have an account?{" "}
                <BoldText>
                  <Link style={styles.linkText} href="/(signInOrUp)/login">
                    Log in
                  </Link>
                </BoldText>
              </LargeText>
              <Text style={styles.disclaimer}>
                By tapping 'Create account' / 'Log in', you agree to our{" "}
                <Link style={styles.linkText} href={TOS_URL}>
                  Terms of service
                </Link>
                . Learn how we process your data in our{" "}
                <Link style={styles.linkText} href={PRIVACY_URL}>
                  Privacy policy
                </Link>{" "}
                and{" "}
                <Link style={styles.linkText} href={PRIVACY_URL}>
                  Cookies policy.
                </Link>
              </Text>
            </View>
          </View>
        </ImageBackground>
      </Screen>
    </KeyboardAvoidingView>
  )
}

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 0,
  },
  invisibleAdminBox: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 100,
    height: 100,
    zIndex: 5,
  },
  backgroundImage: {
    paddingTop: 125,
    alignItems: "center",
    flex: 1,
    resizeMode: "cover",
    width: "100%",
  },
  bottomContainer: {
    marginTop: "auto",
    marginBottom: 40,
  },
  updateContainer: {
    alignItems: "center",
  },
  updateAlert: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 15,
    color: "white",
    textAlign: "center",
    width: 300,
    marginBottom: 25,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    padding: 10,
  },
  createText: {
    fontFamily: "InterTight-Regular",
    fontSize: 20,
    color: "white",
    textAlign: "center",
  },
  loginText: {
    color: "white",
  },
  linkText: {
    textDecorationLine: "underline",
    fontSize: 13.5,
    color: "white",
    fontFamily: "InterTight-SemiBold",
  },
  disclaimerContainer: {
    alignItems: "center",
    marginBottom: 5,
    gap: 15,
    color: "white",
  },
  disclaimer: {
    fontFamily: "InterTight-Regular",
    fontSize: 13,
    color: "white",
    textAlign: "center",
    width: 319,
  },
})

export default InitialView
