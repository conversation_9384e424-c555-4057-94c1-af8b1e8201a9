import { StyleSheet, View } from "react-native"
import PersonCard from "../../components/leads/PersonCard"
import { UserWithPrivateData } from "@/types/user"
import { useSocialContext } from "@/context/SocialContext"
import { Lead } from "@/types/social"

export interface ViewProfileScreenProps {
  user: UserWithPrivateData
}

export const ViewProfileScreen = ({ user }: ViewProfileScreenProps) => {
  const { rootTopicNameToId } = useSocialContext()

  const lead: Lead = {
    id: 1,
    score: 0.7,
    topics: [
      {
        name: "News-based dating apps",
        rootId: rootTopicNameToId["technology"],
      },
      {
        name: "Being informed",
        rootId: rootTopicNameToId["worldNews"],
      },
      {
        name: "Finding unique connections",
        rootId: rootTopicNameToId["lifestyle"],
      },
    ],
    user,
  }

  return (
    <View key="profile" style={styles.profile}>
      <PersonCard
        lead={lead}
        recipientUser={lead.user}
        showHideAndReport={false}
      />
    </View>
  )
}

export const styles = StyleSheet.create({
  profile: {
    backgroundColor: "transparent",
    marginHorizontal: 20,
  },
})

export default ViewProfileScreen
