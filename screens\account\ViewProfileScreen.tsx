import { StyleSheet, View } from "react-native"
import { Lead } from "@/apiQueries/apiQueries"
import PersonCard from "../../components/leads/PersonCard"
import { UserWithPrivateData } from "@/types/user"

export interface ViewProfileScreenProps {
  user: UserWithPrivateData
}

export const ViewProfileScreen = ({ user }: ViewProfileScreenProps) => {
  const lead: Lead = {
    id: 1,
    score: 0.7,
    topics: [
      "News-based dating apps",
      "Being informed",
      "Finding unique connections",
    ],
    user,
  }

  return (
    <View key="profile" style={styles.profile}>
      <PersonCard
        lead={lead}
        recipientUser={lead.user}
        showHideAndReport={false}
      />
    </View>
  )
}

export const styles = StyleSheet.create({
  profile: {
    backgroundColor: "transparent",
    marginHorizontal: 20,
  },
})

export default ViewProfileScreen
