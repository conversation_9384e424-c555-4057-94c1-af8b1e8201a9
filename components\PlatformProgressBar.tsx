import React from "react"
import { Platform, View } from "react-native"
import { ProgressBar as PaperProgressBar } from "react-native-paper"

interface ProgressBarProps {
  style?: any
  color?: string
  animatedValue?: number
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  style,
  color = "white",
  animatedValue = 0,
}) => {
  if (Platform.OS === "web") {
    // Web implementation using a simple View
    return (
      <View
        style={[
          style,
          {
            backgroundColor: color,
            transform: [{ scaleX: animatedValue }],
            transformOrigin: "left",
          },
        ]}
      />
    )
  }

  // Native platforms use react-native-paper ProgressBar
  return (
    <PaperProgressBar
      style={style}
      color={color}
      animatedValue={animatedValue}
    />
  )
}

export default ProgressBar
