import React from "react"
import { StyleSheet, View } from "react-native"
import { Title } from "react-native-paper"
import { ArticleMatchPreview } from "@/apiQueries/newsFeed"
import { Image } from "expo-image"

export type MatchMoodsWidgetProps = {
  matchPreview?: ArticleMatchPreview
}

export const MatchMoodsWidget = ({ matchPreview }: MatchMoodsWidgetProps) => {
  return (
    <View style={styles.matchesContainer}>
      <View style={styles.imagesContainer}>
        {matchPreview
          ? matchPreview.matchImages.map(({ url }, index) => (
              <View key={index} style={styles.circle}>
                <Image source={{ uri: url }} style={styles.circleImage} />
              </View>
            ))
          : null}
      </View>
      {matchPreview ? (
        <Title style={styles.matchText}>
          {matchPreview.matchCount}{" "}
          {matchPreview.matchCount === 1 ? "Match" : "Matches"} rated this
        </Title>
      ) : null}
    </View>
  )
}

const PADDING_BOTTOM = 4
export const MATCH_MOODS_HEIGHT = 32 + PADDING_BOTTOM

const styles = StyleSheet.create({
  matchesContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    paddingBottom: PADDING_BOTTOM,
    height: MATCH_MOODS_HEIGHT,
  },
  imagesContainer: {
    flexDirection: "row-reverse",
    gap: -4,
  },
  circle: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: "#fff",
    borderRadius: 12,
    overflow: "hidden",
  },
  circleImage: {
    width: "100%",
    height: "100%",
    objectFit: "cover",
  },
  matchText: {
    fontSize: 12,
    color: "#7d7d7d",
    fontWeight: "500",
    marginVertical: 0,
  },
})
