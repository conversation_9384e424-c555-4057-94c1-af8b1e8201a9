import Swiper from "react-native-deck-swiper"
import Person<PERSON><PERSON> from "@/components/leads/PersonCard"
import { LEADS } from "./leads"
import { useSocialContext } from "@/context/SocialContext"
import _ from "lodash"

export default function Story() {
  const { rootTopicNameToId } = useSocialContext()
  const topicsWithEveryRoot = _.map(rootTopicNameToId, (id, name) => ({
    name: _.startCase(name),
    rootId: id,
  }))
  return (
    <Swiper
      cards={[
        {
          ...LEADS[0],
          user: { ...LEADS[0].user, biography: "" },
          topics: topicsWithEveryRoot,
        },
      ]}
      verticalSwipe={false}
      renderCard={(lead) => (
        <PersonCard
          lead={lead}
          recipientUser={LEADS[0].user}
          onHideAndReport={() => {}}
        />
      )}
    />
  )
}
