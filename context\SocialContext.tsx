import _ from "lodash"
import { createContext, useContext, useMemo, useState } from "react"

type SocialContextProps = {
  rootTopicNameToId: Record<string, number>
  rootTopicIdToName: Record<number, string>
}

const SocialContext = createContext<SocialContextProps | undefined>(undefined)

const defaultRootTopics = [
  { name: "lifestyle", id: 0 },
  { name: "politics", id: 561 },
  { name: "business", id: 768 },
  { name: "culture", id: 1128 },
  { name: "worldNews", id: 1523 },
  { name: "technology", id: 1923 },
  { name: "foodCooking", id: 2226 },
  { name: "sports", id: 2454 },
  { name: "science", id: 2876 },
  { name: "health", id: 3147 },
  { name: "travel", id: 3302 },
  { name: "environment", id: 3438 },
]

export type SocialProviderProps = {
  children: React.ReactNode
}

export const SocialProvider = ({ children }: SocialProviderProps) => {
  const [rootTopics] = useState(defaultRootTopics)

  const rootTopicNameToId = useMemo(() => {
    return _(rootTopics)
      .keyBy("name")
      .mapValues((topic) => topic.id)
      .value()
  }, [])

  const rootTopicIdToName = useMemo(() => {
    return _(rootTopics)
      .keyBy("id")
      .mapValues((topic) => topic.name)
      .value()
  }, [])

  return (
    <SocialContext.Provider value={{ rootTopicNameToId, rootTopicIdToName }}>
      {children}
    </SocialContext.Provider>
  )
}

export const useSocialContext = () => {
  const context = useContext(SocialContext)

  if (!context) throw new Error("Context called outside Provider")

  return context
}
