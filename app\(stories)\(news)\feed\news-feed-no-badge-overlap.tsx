import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_ARTICLES, NEWSFEED_PROPS } from "./news"
import _ from "lodash"
import { useNewsContext } from "@/context/NewsContext"
import { useEffect } from "react"

export default function Story() {
  const { setArticles } = useNewsContext()

  useEffect(() => {
    const articlesWithFewSections = _(NEWSFEED_ARTICLES)
      .groupBy("frontpageSection")
      .map((articles, section) => articles[0])
      .slice(0, 6)
      .value()
    setArticles(articlesWithFewSections)
  }, [setArticles])

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} />
    </Screen>
  )
}
