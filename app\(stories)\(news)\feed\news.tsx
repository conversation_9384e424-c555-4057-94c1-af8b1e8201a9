import { NewsFeed, NewsFeedProps } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { newsfeedSections } from "@/components/news/constants"
import { USER } from "../../(account)/account"
import { SESSION } from "../../notifications/notification-feed"
import { faker } from "@faker-js/faker"
import _ from "lodash"
import { GistSwipeType, RawArticle } from "@/types/news"
import { convertRawArticle } from "@/utils/rawConversion"

export const rawArticles: RawArticle[] = [
  {
    id: 19147,
    source: "The New York Times",
    title: "13 Small Living Room Ideas That Will Maximize Your Space",
    url: "https://forward.com/fast-forward/602674/john-fetterman-israel-iran-biden/",
    image_url:
      "https://forward.com/wp-content/uploads/2024/04/GettyImages-**********.jpg?_t=**********",
    frontpage_section: "topStories",
    published_at: "2024-04-14 14:00:00",
    favicon_url:
      "https://www.nytimes.com/vi-assets/static-assets/apple-touch-icon-dark-f74e69bf6dae735169854a4126cebf8c.png",
  },
  {
    id: 19155,
    source: "Women's Health Magazine",
    title:
      "Iran's attack draws Israel and US closer together after weeks of growing tension",
    url: "https://forward.com/fast-forward/602554/irans-attack-draws-israel-and-us-closer-together-after-weeks-of-growing-tension/",
    image_url:
      "https://forward.com/wp-content/uploads/2024/04/GettyImages-**********.jpg?_t=**********",
    frontpage_section: "topStories",
    published_at: "2024-04-14 14:00:00",
    favicon_url:
      "https://www.womenshealthmag.com/_assets/design-tokens/womenshealthmag/static/images/favicon.dea226e.ico",
  },
  {
    id: 19060,
    source: "The Jewish Daily Forward",
    title:
      "How to host a Seder when everyone is talking past each other and strongly disagrees about the war",
    url: "https://forward.com/culture/602335/how-to-host-a-passover-seder-wartime-israel-gaza-palestine/",
    image_url:
      "https://forward.com/wp-content/uploads/2024/04/GettyImages-**********-scaled.jpg?_t=**********",
    frontpage_section: "topStories",
    published_at: "2024-04-14 14:00:00",
    favicon_url:
      "https://forward.com/wp-content/uploads/2021/11/cropped-favicon-32x32.png",
  },
  {
    id: 19065,
    source: "Forward",
    title:
      "How to host a Seder when everyone is talking past each other and strongly disagrees about the war",
    url: "https://forward.com/culture/602335/how-to-host-a-passover-seder-wartime-israel-gaza-palestine/",
    image_url:
      "https://forward.com/wp-content/uploads/2024/04/GettyImages-**********-scaled.jpg?_t=**********",
    frontpage_section: "business",
    published_at: "2024-04-14 14:00:00",
    faviconUrl:
      "https://forward.com/wp-content/uploads/2021/11/cropped-favicon-32x32.png",
  },
].map((article) => ({
  ...article,
  summary_points: null,
  is_opened: Math.random() > 0.5,
  is_surveyed: Math.random() > 0.5,
  is_gist_rated: Math.random() > 0.5,
  gist_rating: null,
  position: 0,
}))

export const articles = rawArticles.map(convertRawArticle)

export const NEWSFEED_ARTICLES = newsfeedSections
  .filter((section) => section.isNews && section.name !== "finishReading")
  .map((section, sectionIndex) =>
    articles
      .map((article, articleIndex) => ({
        ...article,
        id: sectionIndex * 1000 + articleIndex,
        frontpageSection: section.name,
        isOpened: Math.random() > 0.5,
        summaryPoints: _.times(2).map((v) =>
          faker.lorem.sentence({ min: 6, max: 7 }),
        ),
      }))
      .map((article) => ({
        ...article,
        isSurveyed: article.isOpened && Math.random() > 0.5,
      })),
  )
  .flat()

export const MATCH_DATA_LIST = articles
  .map((a, index) => {
    const counts = [10, 0, 1, 6]
    const count = counts[index % counts.length]
    return {
      articleId: a.id,
      matchCount: count,
      matchImages: USER.images.slice(0, count),
    }
  })
  .filter((a) => a.matchCount > 0)

export const NEWSFEED_PROPS: NewsFeedProps = {
  alerts: [],
  announcements: [],
  matchPreviews: MATCH_DATA_LIST,
  hideMatchMoods: true,
  referralCode: "ABC123",
  session: SESSION,
  onCloseAlert: () => {},
}

export default function NewsFeedStory() {
  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} />
    </Screen>
  )
}
