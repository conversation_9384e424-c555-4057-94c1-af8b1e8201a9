import { View, Text, TouchableOpacity } from "react-native"
import { But<PERSON> } from "../Button"
import { registerForPushNotificationsAsync } from "@/utils/pushNotifications"

export const TITLE = "Enable notifications to stay up to date"
export const SUBTITLE =
  "Please enable in-app notifications so you won’t miss anything important. \n\nYou can always customize your notifications in settings."

const NotificationStep = ({
  onNext,
}: {
  onNext: (pushToken: string | undefined) => void
}) => {
  const handlePress = () => {
    registerForPushNotificationsAsync().then(onNext)
  }

  return (
    <View style={{ justifyContent: "center", alignItems: "center" }}>
      <Button text="Enable notifications" onPress={handlePress} />
      <TouchableOpacity
        style={{
          marginTop: 8,
          padding: 8,
          borderRadius: 4,
        }}
        onPress={() => onNext(undefined)}
      >
        <Text style={{ textDecorationLine: "underline" }}>
          Proceed without notifications
        </Text>
      </TouchableOpacity>
    </View>
  )
}

export default NotificationStep
