import { ReadRatedCover } from "@/components/news/ReadRatedCover"
import { BoldText } from "@/components/StyledText"
import { Article } from "@/types/news"
import { StyleSheet, Text, View } from "react-native"

export default function Story() {
  return (
    <View style={styles.container}>
      <BoldText>Read, not rated</BoldText>
      <View style={styles.item}>
        <Text>Some random text</Text>
        <ReadRatedCover
          article={{ isOpened: true, isSurveyed: false } as Article}
        />
      </View>
      <BoldText>Read and rated</BoldText>
      <View style={styles.item}>
        <Text>Some random text</Text>
        <ReadRatedCover
          article={{ isOpened: true, isSurveyed: true } as Article}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
    marginTop: 50,
    alignItems: "center",
    gap: 20,
  },
  item: {
    width: 150,
    height: 150,
  },
})
