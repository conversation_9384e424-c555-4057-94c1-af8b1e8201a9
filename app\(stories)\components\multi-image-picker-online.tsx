import { updateProfileImages } from "@/apiQueries/auth"
import { But<PERSON> } from "@/components/Button"
import MultiImagePicker, {
  LocalOrRemoteImage,
} from "@/components/MultiImagePicker"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { TextInput } from "@/components/TextInput"
import { Screen } from "@/components/Themed"
import { useState } from "react"

export default function Story() {
  const [pics, setPics] = useState<LocalOrRemoteImage[]>([])
  const [token, setToken] = useState<string>("")

  return (
    <Screen>
      <TextInput label="token" value={token} onChangeText={setToken} />
      <MultiImagePicker onImagesChange={setPics} />
      <Button
        text="Submit"
        onPress={async () => {
          const response = await updateProfileImages({
            token,
            pics: pics.map((pic, index) => ({
              position: index,
              ...pic,
            })),
            connectionMode: ConnectionMode.Dates,
          })

          console.log("response:")
          console.log(response)
        }}
      />
    </Screen>
  )
}
