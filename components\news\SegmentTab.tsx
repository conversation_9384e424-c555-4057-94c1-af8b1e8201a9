import { DARK_GREY } from "@/constants/Colors"
import { fontStyles } from "@/styles"
import { useEffect, useRef } from "react"
import { Animated, StyleSheet, TouchableOpacity, View } from "react-native"

type Props = {
  tabs: string[]
  activeIndex: number
  onTabChange: (index: number) => void
}

const SegmentTab = ({ tabs, activeIndex, onTabChange }: Props) => {
  const dimOpacity = 0.4
  const animatedValues = useRef(
    tabs.map(() => new Animated.Value(dimOpacity)) || [],
  ).current

  useEffect(() => {
    if (animatedValues.length > 0) {
      animatedValues[activeIndex].setValue(1)
      animatedValues
        .filter((_, i) => i !== activeIndex)
        .forEach((animValue) => {
          animValue.setValue(dimOpacity)
        })
    }
  }, [activeIndex])

  const handleTabPress = (index: number) => {
    onTabChange(index)
    const animations = animatedValues.map((animValue, i) => {
      return Animated.timing(animValue, {
        toValue: i === index ? 1 : dimOpacity,
        duration: 250,
        useNativeDriver: true,
      })
    })
    Animated.parallel(animations).start()
  }

  return (
    <View style={styles.container}>
      {tabs.map((item, index) => {
        return (
          <TouchableOpacity
            key={index}
            onPress={() => handleTabPress(index)}
            activeOpacity={0.8}
            style={styles.tab}
          >
            <Animated.Text
              style={[
                styles.tabLabel,
                {
                  color: DARK_GREY,
                  opacity: animatedValues[index],
                },
              ]}
            >
              {item}
            </Animated.Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )
}

export default SegmentTab

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  tab: {
    flex: 1,
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
    height: 45,
  },
  tabLabel: {
    ...fontStyles.editorial,
    fontSize: 20,
  },
})
