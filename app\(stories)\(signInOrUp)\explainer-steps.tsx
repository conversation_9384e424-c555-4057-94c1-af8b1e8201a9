import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { router } from "expo-router"
import { useHideHeader } from "../story_utils"

import { useState } from "react"
import { assembleExplainerSteps } from "@/components/signInOrUp/steps"

export default function Story() {
  useHideHeader()

  const [currentStepIndex, setCurrentStepIndex] = useState(0)

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex((prev) => prev + 1)
    } else {
      router.back()
    }
  }

  const steps = assembleExplainerSteps({
    onNext: handleNext,
  })

  return (
    <SignUpWrapper
      title={steps[currentStepIndex].title}
      subtitle="Gists help you get straight to the point while earning points to track how much you learn."
      progress={(currentStepIndex + 1) / steps.length}
      submitIsDisabled={steps[currentStepIndex].submitIsDisabled}
      onBack={() => setCurrentStepIndex((prev) => Math.max(prev - 1, 0))}
      onNext={() =>
        steps[currentStepIndex].onNext || currentStepIndex < steps.length - 1
          ? setCurrentStepIndex((prev) => prev + 1)
          : router.back()
      }
    >
      {steps[currentStepIndex].bodyComponent!()}
    </SignUpWrapper>
  )
}
