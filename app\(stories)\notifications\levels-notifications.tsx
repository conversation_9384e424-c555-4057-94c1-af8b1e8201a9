import {
  AlmostHitDensityBonus1Notification,
  AlmostHitDensityBonus2Notification,
  AlmostHitStreakNotification,
  AlmostLevelUpNotification,
  AlmostLosingStreakNotification,
  HitDayOrWeekStreakNotification,
  HitDensityBonus1Notification,
  HitDensityBonus2Notification,
  HitLongTermStreakNotification,
  HitMonthStreakNotification,
  LevelsFeatureLiveNotification,
  LevelsWeeklyRecapNotification,
  LevelUpNotification,
  NotificationType,
} from "@/apiQueries/notificationTypes"
import { LEVELS } from "../components/badge"
import { NotificationFeed_ } from "@/components/notifications/NotificationFeed"
import { SESSION } from "./notification-feed"

const DEFAULT_PROPS = {
  id: "1",
  createdAt: new Date().toISOString(),
  isRead: false,
}

const LEVEL_UP_NOTIFICATION: LevelUpNotification = {
  type: NotificationType.LevelUp,
  level: LEVELS[0],
  nextLevel: LEVELS[1],
  ...DEFAULT_PROPS,
}

const ALMOST_LEVEL_UP_NOTIFICATION: AlmostLevelUpNotification = {
  type: NotificationType.AlmostLevelUp,
  pointsNeeded: 10,
  nextLevel: LEVELS[1],
  ...DEFAULT_PROPS,
}

const LEVELS_FEATURE_LIVE_NOTIFICATION: LevelsFeatureLiveNotification = {
  type: NotificationType.LevelsFeatureLive,
  ...DEFAULT_PROPS,
}

const ALMOST_LOSING_STREAK_NOTIFICATION: AlmostLosingStreakNotification = {
  type: NotificationType.AlmostLosingStreak,
  ...DEFAULT_PROPS,
}

const ALMOST_HIT_STREAK_NOTIFICATION: AlmostHitStreakNotification = {
  type: NotificationType.AlmostHitStreak,
  streakTypeToHit: "2_day",
  rewardPoints: 30,
  ...DEFAULT_PROPS,
}

const ALMOST_HIT_DENSITY_BONUS_1_NOTIFICATION: AlmostHitDensityBonus1Notification =
  {
    type: NotificationType.AlmostHitDensityBonus1,
    ...DEFAULT_PROPS,
  }

const ALMOST_HIT_DENSITY_BONUS_2_NOTIFICATION: AlmostHitDensityBonus2Notification =
  {
    type: NotificationType.AlmostHitDensityBonus2,
    ...DEFAULT_PROPS,
  }

const HIT_DENSITY_BONUS_1_NOTIFICATION: HitDensityBonus1Notification = {
  type: NotificationType.HitDensityBonus1,
  ...DEFAULT_PROPS,
}

const HIT_DENSITY_BONUS_2_NOTIFICATION: HitDensityBonus2Notification = {
  type: NotificationType.HitDensityBonus2,
  ...DEFAULT_PROPS,
}

const HIT_DAY_OR_WEEK_STREAK_NOTIFICATION: HitDayOrWeekStreakNotification = {
  type: NotificationType.HitDayOrWeekStreak,
  streakType: "2_day",
  pointsEarned: 30,
  ...DEFAULT_PROPS,
}

const HIT_MONTH_STREAK_NOTIFICATION: HitMonthStreakNotification = {
  type: NotificationType.HitMonthStreak,
  streakType: "4_week",
  ...DEFAULT_PROPS,
}

const HIT_LONG_TERM_STREAK_NOTIFICATION: HitLongTermStreakNotification = {
  type: NotificationType.HitLongTermStreak,
  streakType: "180_day",
  pointsEarned: 500,
  ...DEFAULT_PROPS,
}

const LEVELS_WEEKLY_RECAP_NOTIFICATION: LevelsWeeklyRecapNotification = {
  type: NotificationType.LevelsWeeklyRecap,
  ratingsCount: 30,
  pointsEarned: 450,
  nextLevel: LEVELS[1],
  ...DEFAULT_PROPS,
}

const LEVELS_NOTIFICATIONS = [
  LEVEL_UP_NOTIFICATION,
  ALMOST_LEVEL_UP_NOTIFICATION,
  LEVELS_FEATURE_LIVE_NOTIFICATION,
  ALMOST_LOSING_STREAK_NOTIFICATION,
  ALMOST_HIT_STREAK_NOTIFICATION,
  ALMOST_HIT_DENSITY_BONUS_1_NOTIFICATION,
  ALMOST_HIT_DENSITY_BONUS_2_NOTIFICATION,
  HIT_DENSITY_BONUS_1_NOTIFICATION,
  HIT_DENSITY_BONUS_2_NOTIFICATION,
  HIT_DAY_OR_WEEK_STREAK_NOTIFICATION,
  HIT_MONTH_STREAK_NOTIFICATION,
  HIT_LONG_TERM_STREAK_NOTIFICATION,
  LEVELS_WEEKLY_RECAP_NOTIFICATION,
].map((notification, index) => ({
  ...notification,
  id: `${index}`,
}))

export default function Story() {
  return (
    <NotificationFeed_
      notifications={LEVELS_NOTIFICATIONS}
      session={SESSION}
      onRefresh={() => {}}
      onNavigate={() => {}}
    />
  )
}
