import React, { useCallback } from "react"
import { FlatList } from "react-native"

import { widthPercentageToDP as wp } from "react-native-responsive-screen"

import MiniArticleCard from "@/components/news/MiniArticleCard"
import { Article } from "@/types/news"

type Props = {
  articles: Article[]
}

const MiniArticleList: React.FC<Props> = ({ articles }) => {
  const renderItem = useCallback(({ item }: { item: Article }) => {
    return <MiniArticleCard article={item} />
  }, [])

  return (
    <FlatList
      data={articles}
      renderItem={renderItem}
      contentContainerStyle={{ gap: wp(4.3), paddingHorizontal: 10 }}
      horizontal
      showsHorizontalScrollIndicator={false}
    />
  )
}

export default MiniArticleList
