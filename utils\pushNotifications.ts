import * as Device from "expo-device"
import * as Notifications from "expo-notifications"
import Constants from "expo-constants"
import { <PERSON><PERSON>, Linking, Platform } from "react-native"
import { router } from "expo-router"
import { convertRawNotification } from "@/apiQueries/notifications"
import { ConnectionModeContextValue } from "@/context/ModeContext"
import { EventType, trackEvent } from "./tracking"
import {
  Notification,
  NotificationType,
  RawNotification,
  BroadcastNotification,
} from "@/apiQueries/notificationTypes"
import { newsFeedPath, leadsPath, matchesPath, levelsPath } from "./deepLinks"
import { openArticle } from "@/components/ArticlePage"
import { navigateToInAppDestination } from "@/components/news/constants"

const getPermissionsStatus = async () => {
  const { status } = await Notifications.getPermissionsAsync()
  return status
}

const checkAndroidChannel = async () => {
  const notificationChannel = await Notifications.getNotificationChannelAsync(
    "default",
  )
  return notificationChannel !== null
}

const checkIfNotificationsEnabled = async () => {
  try {
    const status = await getPermissionsStatus()
    const permissionGranted =
      Platform.OS === "android"
        ? status === "granted" && (await checkAndroidChannel())
        : status === "granted"

    return permissionGranted
  } catch (error) {
    console.error("Error loading settings:", error)
  }
}

function handleRegistrationError(errorMessage: string) {
  throw new Error(errorMessage)
}

async function setupNotifications() {
  if (Platform.OS === "android") {
    Notifications.setNotificationChannelAsync("default", {
      name: "default",
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: "#FF231F7C",
    })
  }

  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: false,
      shouldSetBadge: true,
    }),
  })
}

async function getPushToken() {
  const projectId =
    Constants?.expoConfig?.extra?.eas?.projectId ??
    Constants?.easConfig?.projectId
  if (!projectId) {
    handleRegistrationError("Project ID not found")
  }

  let pushToken

  try {
    pushToken = (
      await Notifications.getExpoPushTokenAsync({
        projectId,
      })
    ).data
  } catch (e: unknown) {
    throw new Error(`Failed to get push token: ${e}`)
  }

  return pushToken
}

export async function registerForPushNotificationsAsync() {
  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = existingStatus
    if (existingStatus === "denied") {
      Alert.alert(
        "Enable notifications",
        "You must manually enable push notifications in your settings",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Settings",
            onPress: () => Linking.openSettings(),
          },
        ],
      )
    } else if (existingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
        },
      })
      finalStatus = status
    }

    if (finalStatus === "granted") {
      await setupNotifications()
      return await getPushToken()
    } else {
      console.log(
        "Permission not granted to get push token for push notification!",
      )
    }
  } else {
    console.warn("Must use physical device for push notifications")
  }
}

const broadcastHandler = (notification: BroadcastNotification) => {
  const { articleId, articleUrl, inAppDestination, externalUrl } = notification
  if (articleId && articleUrl) {
    openArticle({ id: articleId, url: articleUrl })
  } else if (inAppDestination) {
    navigateToInAppDestination(inAppDestination)
  } else if (externalUrl) {
    Linking.openURL(externalUrl).catch((err) =>
      console.error("Failed to open external URL:", err),
    )
  } else {
    router.navigate(newsFeedPath)
  }
}

const notificationDestinations: Record<NotificationType, string | Function> = {
  [NotificationType.Announcement]: newsFeedPath,
  [NotificationType.MoreSurveysNeeded]: newsFeedPath,
  [NotificationType.Broadcast]: broadcastHandler,
  [NotificationType.NewLeads]: leadsPath,
  [NotificationType.NewMatch]: matchesPath,
  [NotificationType.NewLike]: leadsPath,
  [NotificationType.NewFriendRequest]: newsFeedPath,
  [NotificationType.FriendRequestAccepted]: matchesPath,
  [NotificationType.NewMessage]: matchesPath,
  [NotificationType.LevelUp]: levelsPath,
  [NotificationType.AlmostLevelUp]: newsFeedPath,
  [NotificationType.LevelsFeatureLive]: levelsPath,
  [NotificationType.AlmostLosingStreak]: newsFeedPath,
  [NotificationType.AlmostHitStreak]: newsFeedPath,
  [NotificationType.AlmostHitDensityBonus1]: newsFeedPath,
  [NotificationType.AlmostHitDensityBonus2]: newsFeedPath,
  [NotificationType.HitDensityBonus1]: levelsPath,
  [NotificationType.HitDensityBonus2]: levelsPath,
  [NotificationType.HitDayOrWeekStreak]: levelsPath,
  [NotificationType.HitMonthStreak]: levelsPath,
  [NotificationType.HitLongTermStreak]: levelsPath,
  [NotificationType.LevelsWeeklyRecap]: levelsPath,
}

const isKnownNotificationType = (type: string): type is NotificationType =>
  type in notificationDestinations

export const handleNotification = async ({
  notification,
  setActiveConnectionMode,
}: {
  notification: Notification
  setActiveConnectionMode: ConnectionModeContextValue["setActiveConnectionMode"]
}) => {
  if (notification.type === NotificationType.Announcement && notification.url) {
    router.navigate(notification.url)
    return
  }

  switch (notification.type) {
    case NotificationType.NewMessage:
    case NotificationType.NewMatch:
    case NotificationType.NewLike:
      if (notification.connectionMode) {
        setActiveConnectionMode(notification.connectionMode)
      }
  }

  const type = notification.type
  if (type === NotificationType.NewMessage && notification.chatChannel) {
    router.navigate(`/(app)/matches/${notification.chatChannel.streamId}`)
  } else if (isKnownNotificationType(type)) {
    const destination = notificationDestinations[type]

    if (typeof destination === "function") {
      destination(notification)
    } else if (typeof destination === "string") {
      router.navigate(destination as any)
    }
  } else {
    router.navigate(newsFeedPath)
  }
}

const handleResponse = ({
  response,
  setActiveConnectionMode,
}: {
  response: Notifications.NotificationResponse
  setActiveConnectionMode: ConnectionModeContextValue["setActiveConnectionMode"]
}) => {
  trackEvent(EventType.NotificationRespondedTo, {
    data: response.notification.request.content,
  })

  const notification = convertRawNotification(
    response.notification.request.content.data as RawNotification,
  )

  handleNotification({ notification, setActiveConnectionMode })
}

export {
  checkIfNotificationsEnabled,
  setupNotifications,
  getPushToken,
  handleResponse,
}
