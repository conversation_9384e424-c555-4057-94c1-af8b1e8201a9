1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.scoopt.inpress"
4    android:versionCode="242"
5    android:versionName="242.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:3-75
11-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:4:3-62
14-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:4:20-60
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:5:3-64
15-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:5:20-62
16    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
16-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:6:3-77
16-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:6:20-75
17    <uses-permission android:name="android.permission.READ_CONTACTS" />
17-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:7:3-69
17-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:7:20-67
18    <uses-permission
18-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:8:3-77
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:8:20-75
20        android:maxSdkVersion="32" />
20-->[:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-35
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:9:3-68
21-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:9:20-66
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:11:3-63
22-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:11:20-61
23    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
23-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:12:3-70
23-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:12:20-68
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:13:3-78
24-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:13:20-76
25    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
25-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:14:3-76
25-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:14:20-74
26
27    <queries>
27-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:15:3-21:13
28        <intent>
28-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:16:5-20:14
29            <action android:name="android.intent.action.VIEW" />
29-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
29-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
30
31            <category android:name="android.intent.category.BROWSABLE" />
31-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
31-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
32
33            <data android:scheme="https" />
33-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
33-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
34        </intent>
35        <intent>
35-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
36            <action android:name="android.intent.action.SEND" />
36-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
36-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
37
38            <category android:name="android.intent.category.DEFAULT" />
38-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
38-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
39
40            <data android:mimeType="application/octet-stream" />
40-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
41        </intent>
42        <intent>
42-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:18
43            <action android:name="android.intent.action.SEND" />
43-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
43-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
44
45            <category android:name="android.intent.category.DEFAULT" />
45-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
45-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
46
47            <data android:mimeType="text/x-vcard" />
47-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
48        </intent>
49        <intent>
49-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-28:18
50            <action android:name="android.intent.action.SEND" />
50-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
50-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
51
52            <category android:name="android.intent.category.DEFAULT" />
52-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
52-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
53
54            <data android:mimeType="text/vcard" />
54-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
55        </intent>
56        <intent>
56-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:9-31:18
57            <action android:name="android.intent.action.EDIT" />
57-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-65
57-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:21-62
58        </intent>
59        <intent>
59-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:18
60            <action android:name="android.intent.action.INSERT" />
60-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-67
60-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:21-64
61        </intent>
62
63        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
63-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
63-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
64        <intent>
64-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
65            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
65-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
65-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
66        </intent>
67        <intent>
67-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
68
69            <!-- Required for picking images from the camera roll if targeting API 30 -->
70            <action android:name="android.media.action.IMAGE_CAPTURE" />
70-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
70-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
71        </intent>
72        <intent>
72-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
73
74            <!-- Required for picking images from the camera if targeting API 30 -->
75            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
75-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
75-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
76        </intent>
77        <intent>
77-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
78
79            <!-- Required for file sharing if targeting API 30 -->
80            <action android:name="android.intent.action.SEND" />
80-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
80-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
81
82            <data android:mimeType="*/*" />
82-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
83        </intent> <!-- Fallback SENDTO -->
84        <intent>
84-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-23:18
85            <action android:name="android.intent.action.SENDTO" />
85-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-67
85-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:21-64
86
87            <category android:name="android.intent.category.DEFAULT" />
87-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
87-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
88            <category android:name="android.intent.category.BROWSABLE" />
88-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
88-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
89
90            <data android:scheme="sms" />
90-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
90-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
91        </intent>
92        <intent>
92-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-31:18
93            <action android:name="android.intent.action.SENDTO" />
93-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-67
93-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:21-64
94
95            <category android:name="android.intent.category.DEFAULT" />
95-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
95-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
96            <category android:name="android.intent.category.BROWSABLE" />
96-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
96-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
97
98            <data android:scheme="smsto" />
98-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
98-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
99        </intent>
100        <intent>
100-->[:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
101
102            <!-- Required for opening tabs if targeting API 30 -->
103            <action android:name="android.support.customtabs.action.CustomTabsService" />
103-->[:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
103-->[:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
104        </intent> <!-- Needs to be explicitly declared on Android R+ -->
105        <package android:name="com.google.android.apps.maps" />
105-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
105-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
106        <package android:name="com.facebook.katana" />
106-->[com.facebook.android:facebook-common:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c03408381ed6f07d82964863171975e\transformed\facebook-common-18.1.3\AndroidManifest.xml:16:9-55
106-->[com.facebook.android:facebook-common:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c03408381ed6f07d82964863171975e\transformed\facebook-common-18.1.3\AndroidManifest.xml:16:18-52
107
108        <intent>
108-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
109            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
109-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
109-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
110        </intent>
111        <intent>
111-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
112            <action android:name="android.intent.action.GET_CONTENT" />
112-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
112-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
113
114            <category android:name="android.intent.category.OPENABLE" />
114-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
114-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
115
116            <data android:mimeType="*/*" />
116-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
117        </intent>
118        <intent>
118-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:11:9-13:18
119            <action android:name="com.appsflyer.referrer.INSTALL_PROVIDER" />
119-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:12:13-78
119-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:12:21-75
120        </intent>
121
122        <package android:name="com.instagram.android" />
122-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:19:9-57
122-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:19:18-54
123        <package android:name="com.facebook.lite" />
123-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:22:9-53
123-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:22:18-50
124        <package android:name="com.samsung.android.mapsagent" />
124-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:34:9-65
124-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:34:18-62
125    </queries>
126
127    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
127-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
127-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
128    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
128-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
128-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
129    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
129-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-76
129-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-73
130    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
130-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-75
130-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-72
131    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
131-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-75
131-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-72
132    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
132-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-90
132-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-87
133    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
133-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
133-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
134    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
134-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
134-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
135
136    <uses-feature
136-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
137        android:glEsVersion="0x00020000"
137-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
138        android:required="true" />
138-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
139
140    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
140-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
140-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
141    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
141-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
141-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
142    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Support for Google Privacy Sandbox adservices API -->
142-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
142-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
143    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
143-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:16:5-88
143-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:16:22-85
144    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
144-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:17:5-82
144-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:17:22-79
145    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
145-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:18:5-92
145-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:18:22-89
146    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
146-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:19:5-83
146-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:19:22-80
147
148    <permission
148-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
149        android:name="com.scoopt.inpress.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
149-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
150        android:protectionLevel="signature" />
150-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
151
152    <uses-permission android:name="com.scoopt.inpress.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Samsung Preload referrer collection -->
152-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
152-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
153    <uses-permission android:name="com.samsung.android.mapsagent.permission.READ_APP_INFO" /> <!-- Needed to interact with Huawei AppGallery ContentProvider -->
153-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:31:5-94
153-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:31:22-91
154    <uses-permission android:name="com.huawei.appmarket.service.commondata.permission.GET_COMMON_DATA" />
154-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:38:5-106
154-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:38:22-103
155    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
155-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
155-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
156    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
156-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:5-98
156-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:22-95
157    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" /> <!-- for android -->
157-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:5-94
157-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:22-91
158    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
159    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
160    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
161    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
162    <!-- for Samsung -->
163    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
163-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
163-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
164    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
164-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
164-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
165    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
165-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
165-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
166    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
166-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
166-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
167    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
167-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
167-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
168    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
168-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
168-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
169    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
169-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
169-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
170    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
170-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
170-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
171    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
171-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
171-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
172    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
172-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
172-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
173    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
173-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
173-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
174    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
174-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
174-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
175    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
175-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
175-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
176    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
176-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
176-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
177    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
177-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
177-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
178    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
178-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
178-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
179
180    <application
180-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:3-58:17
181        android:name="com.scoopt.inpress.MainApplication"
181-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:16-47
182        android:allowBackup="true"
182-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:162-188
183        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
183-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
184        android:dataExtractionRules="@xml/appsflyer_data_extraction_rules"
184-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:42:9-75
185        android:debuggable="true"
186        android:extractNativeLibs="false"
187        android:fullBackupContent="@xml/appsflyer_backup_rules"
187-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:43:9-64
188        android:icon="@mipmap/ic_launcher"
188-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:81-115
189        android:label="@string/app_name"
189-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:48-80
190        android:requestLegacyExternalStorage="true"
190-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:248-291
191        android:roundIcon="@mipmap/ic_launcher_round"
191-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:116-161
192        android:supportsRtl="true"
192-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:221-247
193        android:theme="@style/AppTheme"
193-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:189-220
194        android:usesCleartextTraffic="true" >
194-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:18-53
195        <meta-data
195-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:5-100
196            android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled"
196-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:16-77
197            android:value="true" />
197-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:78-98
198        <meta-data
198-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:5-103
199            android:name="com.facebook.sdk.ApplicationId"
199-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:16-61
200            android:value="@string/facebook_app_id" />
200-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:62-101
201        <meta-data
201-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:5-89
202            android:name="com.facebook.sdk.ApplicationName"
202-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:16-63
203            android:value="InPress" />
203-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:64-87
204        <meta-data
204-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:5-87
205            android:name="com.facebook.sdk.AutoInitEnabled"
205-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:16-63
206            android:value="false" />
206-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:64-85
207        <meta-data
207-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:5-94
208            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
208-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:16-71
209            android:value="true" />
209-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:72-92
210        <meta-data
210-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:5-107
211            android:name="com.facebook.sdk.ClientToken"
211-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:16-59
212            android:value="@string/facebook_client_token" />
212-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:60-105
213        <meta-data
213-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:5-119
214            android:name="com.google.android.geo.API_KEY"
214-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:16-61
215            android:value="AIzaSyB08un4GCREXiuy0FSKQ43vUM1ccU9bgOI" />
215-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:62-117
216        <meta-data
216-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:5-83
217            android:name="expo.modules.updates.ENABLED"
217-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:16-59
218            android:value="false" />
218-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:60-81
219        <meta-data
219-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:5-119
220            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
220-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:16-72
221            android:value="@string/expo_runtime_version" />
221-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:73-117
222        <meta-data
222-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:5-105
223            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
223-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:16-80
224            android:value="ALWAYS" />
224-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:81-103
225        <meta-data
225-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:5-99
226            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
226-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:16-79
227            android:value="0" />
227-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:80-97
228
229        <activity
229-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:5-47:16
230            android:name="com.scoopt.inpress.MainActivity"
230-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:15-43
231            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
231-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:44-157
232            android:exported="true"
232-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:279-302
233            android:launchMode="singleTask"
233-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:158-189
234            android:screenOrientation="portrait"
234-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:303-339
235            android:theme="@style/Theme.App.SplashScreen"
235-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:233-278
236            android:windowSoftInputMode="adjustResize" >
236-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:190-232
237            <intent-filter>
237-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:35:7-38:23
238                <action android:name="android.intent.action.MAIN" />
238-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:36:9-60
238-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:36:17-58
239
240                <category android:name="android.intent.category.LAUNCHER" />
240-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:37:9-68
240-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:37:19-66
241            </intent-filter>
242            <intent-filter>
242-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:39:7-46:23
243                <action android:name="android.intent.action.VIEW" />
243-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
243-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
244
245                <category android:name="android.intent.category.DEFAULT" />
245-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
245-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
246                <category android:name="android.intent.category.BROWSABLE" />
246-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
246-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
247
248                <data android:scheme="myapp" />
248-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
248-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
249                <data android:scheme="com.scoopt.inpress" />
249-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
249-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
250                <data android:scheme="exp+inpress-expo-router" />
250-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
250-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
251            </intent-filter>
252        </activity>
253        <activity
253-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:5-178
254            android:name="com.facebook.FacebookActivity"
254-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:15-59
255            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
255-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:60-143
256            android:label="@string/app_name"
256-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:144-176
257            android:theme="@style/com_facebook_activity_theme" />
257-->[com.facebook.android:facebook-common:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c03408381ed6f07d82964863171975e\transformed\facebook-common-18.1.3\AndroidManifest.xml:23:13-63
258        <activity
258-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:5-56:16
259            android:name="com.facebook.CustomTabActivity"
259-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:15-60
260            android:exported="true" >
260-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:61-84
261            <intent-filter>
261-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:50:7-55:23
262                <action android:name="android.intent.action.VIEW" />
262-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
262-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
263
264                <category android:name="android.intent.category.DEFAULT" />
264-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
264-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
265                <category android:name="android.intent.category.BROWSABLE" />
265-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
265-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
266
267                <data android:scheme="@string/fb_login_protocol_scheme" />
267-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
267-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
268            </intent-filter>
269            <intent-filter>
269-->[com.facebook.android:facebook-common:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c03408381ed6f07d82964863171975e\transformed\facebook-common-18.1.3\AndroidManifest.xml:29:13-38:29
270                <action android:name="android.intent.action.VIEW" />
270-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
270-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
271
272                <category android:name="android.intent.category.DEFAULT" />
272-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
272-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
273                <category android:name="android.intent.category.BROWSABLE" />
273-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
273-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
274
275                <data
275-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
276                    android:host="cct.com.scoopt.inpress"
277                    android:scheme="fbconnect" />
277-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
278            </intent-filter>
279        </activity>
280
281        <uses-library
281-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:5-83
282            android:name="org.apache.http.legacy"
282-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:19-56
283            android:required="false" />
283-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:57-81
284
285        <provider
285-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
286            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
286-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
287            android:authorities="com.scoopt.inpress.fileprovider"
287-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
288            android:exported="false"
288-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
289            android:grantUriPermissions="true" >
289-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
290            <meta-data
290-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
291                android:name="android.support.FILE_PROVIDER_PATHS"
291-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
292                android:resource="@xml/file_provider_paths" />
292-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
293        </provider>
294
295        <meta-data
295-->[:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:37
296            android:name="io.sentry.auto-init"
296-->[:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
297            android:value="false" />
297-->[:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-34
298
299        <property
299-->[:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-47:48
300            android:name="android.adservices.AD_SERVICES_CONFIG"
300-->[:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-65
301            android:resource="@xml/ad_services_config" />
301-->[:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-55
302
303        <activity
303-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
304            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
304-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
305            android:exported="true"
305-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
306            android:launchMode="singleTask"
306-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
307            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
307-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
308            <intent-filter>
308-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
309                <action android:name="android.intent.action.VIEW" />
309-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
309-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
310
311                <category android:name="android.intent.category.DEFAULT" />
311-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
311-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
312                <category android:name="android.intent.category.BROWSABLE" />
312-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
312-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
313
314                <data android:scheme="expo-dev-launcher" />
314-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
314-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
315            </intent-filter>
316        </activity>
317        <activity
317-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
318            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
318-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
319            android:screenOrientation="portrait"
319-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
320            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
320-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
321        <activity
321-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
322            android:name="expo.modules.devmenu.DevMenuActivity"
322-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
323            android:exported="true"
323-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
324            android:launchMode="singleTask"
324-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
325            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
325-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
326            <intent-filter>
326-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
327                <action android:name="android.intent.action.VIEW" />
327-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
327-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
328
329                <category android:name="android.intent.category.DEFAULT" />
329-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
329-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
330                <category android:name="android.intent.category.BROWSABLE" />
330-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
330-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
331
332                <data android:scheme="expo-dev-menu" />
332-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
332-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
333            </intent-filter>
334        </activity>
335
336        <provider
336-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
337            android:name="expo.modules.filesystem.FileSystemFileProvider"
337-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
338            android:authorities="com.scoopt.inpress.FileSystemFileProvider"
338-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
339            android:exported="false"
339-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
340            android:grantUriPermissions="true" >
340-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
341            <meta-data
341-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
342                android:name="android.support.FILE_PROVIDER_PATHS"
342-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
343                android:resource="@xml/file_system_provider_paths" />
343-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
344        </provider>
345
346        <service
346-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
347            android:name="com.google.android.gms.metadata.ModuleDependencies"
347-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
348            android:enabled="false"
348-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
349            android:exported="false" >
349-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
350            <intent-filter>
350-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
351                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
351-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
351-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
352            </intent-filter>
353
354            <meta-data
354-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
355                android:name="photopicker_activity:0:required"
355-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
356                android:value="" />
356-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
357        </service>
358
359        <activity
359-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
360            android:name="com.canhub.cropper.CropImageActivity"
360-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
361            android:exported="true"
361-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
362            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
362-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
363        <provider
363-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
364            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
364-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
365            android:authorities="com.scoopt.inpress.ImagePickerFileProvider"
365-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
366            android:exported="false"
366-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
367            android:grantUriPermissions="true" >
367-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
368            <meta-data
368-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
369                android:name="android.support.FILE_PROVIDER_PATHS"
369-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
370                android:resource="@xml/image_picker_provider_paths" />
370-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
371        </provider>
372
373        <service
373-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
374            android:name="expo.modules.location.services.LocationTaskService"
374-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
375            android:exported="false"
375-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
376            android:foregroundServiceType="location" />
376-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
377        <service
377-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
378            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
378-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
379            android:exported="false" >
379-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
380            <intent-filter android:priority="-1" >
380-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
380-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
381                <action android:name="com.google.firebase.MESSAGING_EVENT" />
381-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
381-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
382            </intent-filter>
383        </service>
384
385        <receiver
385-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
386            android:name="expo.modules.notifications.service.NotificationsService"
386-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
387            android:enabled="true"
387-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
388            android:exported="false" >
388-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
389            <intent-filter android:priority="-1" >
389-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
389-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
390                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
390-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
390-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
391                <action android:name="android.intent.action.BOOT_COMPLETED" />
391-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
391-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
392                <action android:name="android.intent.action.REBOOT" />
392-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
392-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
393                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
393-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
393-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
394                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
394-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
394-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
395                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
395-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
395-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
396            </intent-filter>
397        </receiver>
398
399        <activity
399-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
400            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
400-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
401            android:excludeFromRecents="true"
401-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
402            android:exported="false"
402-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
403            android:launchMode="standard"
403-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
404            android:noHistory="true"
404-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
405            android:taskAffinity=""
405-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
406            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
406-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
407
408        <provider
408-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-26:20
409            android:name="expo.modules.sharing.SharingFileProvider"
409-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
410            android:authorities="com.scoopt.inpress.SharingFileProvider"
410-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-71
411            android:exported="false"
411-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
412            android:grantUriPermissions="true" >
412-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-47
413            <meta-data
413-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
414                android:name="android.support.FILE_PROVIDER_PATHS"
414-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
415                android:resource="@xml/sharing_provider_paths" />
415-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
416        </provider>
417
418        <meta-data
418-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
419            android:name="org.unimodules.core.AppLoader#react-native-headless"
419-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
420            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
420-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
421        <meta-data
421-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
422            android:name="com.facebook.soloader.enabled"
422-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
423            android:value="true" />
423-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
424
425        <activity
425-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
426            android:name="com.facebook.react.devsupport.DevSettingsActivity"
426-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
427            android:exported="false" />
427-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
428
429        <meta-data
429-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
430            android:name="com.google.android.gms.version"
430-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
431            android:value="@integer/google_play_services_version" />
431-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
432
433        <receiver
433-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
434            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
434-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
435            android:exported="true"
435-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
436            android:permission="com.google.android.c2dm.permission.SEND" >
436-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
437            <intent-filter>
437-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
438                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
438-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
438-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
439            </intent-filter>
440
441            <meta-data
441-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
442                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
442-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
443                android:value="true" />
443-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
444        </receiver>
445        <!--
446             FirebaseMessagingService performs security checks at runtime,
447             but set to not exported to explicitly avoid allowing another app to call it.
448        -->
449        <service
449-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
450            android:name="com.google.firebase.messaging.FirebaseMessagingService"
450-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
451            android:directBootAware="true"
451-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
452            android:exported="false" >
452-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
453            <intent-filter android:priority="-500" >
453-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
453-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
454                <action android:name="com.google.firebase.MESSAGING_EVENT" />
454-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
454-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
455            </intent-filter>
456        </service>
457        <service
457-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
458            android:name="com.google.firebase.components.ComponentDiscoveryService"
458-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
459            android:directBootAware="true"
459-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
460            android:exported="false" >
460-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
461            <meta-data
461-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
462                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
462-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
463                android:value="com.google.firebase.components.ComponentRegistrar" />
463-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
464            <meta-data
464-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
465                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
465-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
466                android:value="com.google.firebase.components.ComponentRegistrar" />
466-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
467            <meta-data
467-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
468                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
468-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
469                android:value="com.google.firebase.components.ComponentRegistrar" />
469-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
470            <meta-data
470-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
471                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
471-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
472                android:value="com.google.firebase.components.ComponentRegistrar" />
472-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
473            <meta-data
473-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
474                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
474-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
475                android:value="com.google.firebase.components.ComponentRegistrar" />
475-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
476            <meta-data
476-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
477                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
477-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
478                android:value="com.google.firebase.components.ComponentRegistrar" />
478-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
479            <meta-data
479-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
480                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
480-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
481                android:value="com.google.firebase.components.ComponentRegistrar" />
481-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
482        </service>
483        <service
483-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
484            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
484-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
485            android:directBootAware="true"
485-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
486            android:exported="false" >
486-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
487            <meta-data
487-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
488                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
488-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
489                android:value="com.google.firebase.components.ComponentRegistrar" />
489-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
490            <meta-data
490-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
491                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
491-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
492                android:value="com.google.firebase.components.ComponentRegistrar" />
492-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
493            <meta-data
493-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
494                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
494-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
495                android:value="com.google.firebase.components.ComponentRegistrar" />
495-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
496        </service>
497
498        <provider
498-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
499            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
499-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
500            android:authorities="com.scoopt.inpress.mlkitinitprovider"
500-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
501            android:exported="false"
501-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
502            android:initOrder="99" />
502-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
503
504        <activity
504-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f8d6988ae828e36541145bf02d3e3b\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
505            android:name="com.google.android.gms.common.api.GoogleApiActivity"
505-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f8d6988ae828e36541145bf02d3e3b\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
506            android:exported="false"
506-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f8d6988ae828e36541145bf02d3e3b\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
507            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
507-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1f8d6988ae828e36541145bf02d3e3b\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
508        <activity android:name="com.facebook.CustomTabMainActivity" />
508-->[com.facebook.android:facebook-common:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c03408381ed6f07d82964863171975e\transformed\facebook-common-18.1.3\AndroidManifest.xml:24:9-71
508-->[com.facebook.android:facebook-common:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c03408381ed6f07d82964863171975e\transformed\facebook-common-18.1.3\AndroidManifest.xml:24:19-68
509
510        <meta-data
510-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
511            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
511-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
512            android:value="GlideModule" />
512-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
513
514        <uses-library
514-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
515            android:name="androidx.camera.extensions.impl"
515-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
516            android:required="false" />
516-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
517
518        <service
518-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
519            android:name="androidx.camera.core.impl.MetadataHolderService"
519-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
520            android:enabled="false"
520-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
521            android:exported="false" >
521-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
522            <meta-data
522-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
523                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
523-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
524                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
524-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
525        </service>
526
527        <provider
527-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
528            android:name="com.canhub.cropper.CropFileProvider"
528-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
529            android:authorities="com.scoopt.inpress.cropper.fileprovider"
529-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
530            android:exported="false"
530-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
531            android:grantUriPermissions="true" >
531-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
532            <meta-data
532-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
533                android:name="android.support.FILE_PROVIDER_PATHS"
533-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
534                android:resource="@xml/library_file_paths" />
534-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
535        </provider>
536        <provider
536-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
537            android:name="com.google.firebase.provider.FirebaseInitProvider"
537-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
538            android:authorities="com.scoopt.inpress.firebaseinitprovider"
538-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
539            android:directBootAware="true"
539-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
540            android:exported="false"
540-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
541            android:initOrder="100" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
541-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
542        <provider
542-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:12:9-15:40
543            android:name="io.sentry.android.core.SentryInitProvider"
543-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:13:13-69
544            android:authorities="com.scoopt.inpress.SentryInitProvider"
544-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:14:13-70
545            android:exported="false" />
545-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:15:13-37
546        <provider
546-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:16:9-20:39
547            android:name="io.sentry.android.core.SentryPerformanceProvider"
547-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:17:13-76
548            android:authorities="com.scoopt.inpress.SentryPerformanceProvider"
548-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:18:13-77
549            android:exported="false"
549-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:19:13-37
550            android:initOrder="200" />
550-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:20:13-36
551        <provider
551-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
552            android:name="androidx.startup.InitializationProvider"
552-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
553            android:authorities="com.scoopt.inpress.androidx-startup"
553-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
554            android:exported="false" >
554-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
555            <meta-data
555-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
556                android:name="androidx.work.WorkManagerInitializer"
556-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
557                android:value="androidx.startup" />
557-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
558            <meta-data
558-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
559                android:name="androidx.emoji2.text.EmojiCompatInitializer"
559-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
560                android:value="androidx.startup" />
560-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
561            <meta-data
561-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
562                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
562-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
563                android:value="androidx.startup" />
563-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
564            <meta-data
564-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
565                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
565-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
566                android:value="androidx.startup" />
566-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
567        </provider>
568
569        <service
569-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
570            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
570-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
571            android:directBootAware="false"
571-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
572            android:enabled="@bool/enable_system_alarm_service_default"
572-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
573            android:exported="false" />
573-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
574        <service
574-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
575            android:name="androidx.work.impl.background.systemjob.SystemJobService"
575-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
576            android:directBootAware="false"
576-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
577            android:enabled="@bool/enable_system_job_service_default"
577-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
578            android:exported="true"
578-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
579            android:permission="android.permission.BIND_JOB_SERVICE" />
579-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
580        <service
580-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
581            android:name="androidx.work.impl.foreground.SystemForegroundService"
581-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
582            android:directBootAware="false"
582-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
583            android:enabled="@bool/enable_system_foreground_service_default"
583-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
584            android:exported="false" />
584-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
585
586        <receiver
586-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
587            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
587-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
588            android:directBootAware="false"
588-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
589            android:enabled="true"
589-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
590            android:exported="false" />
590-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
591        <receiver
591-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
592            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
592-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
593            android:directBootAware="false"
593-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
594            android:enabled="false"
594-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
595            android:exported="false" >
595-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
596            <intent-filter>
596-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
597                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
597-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
597-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
598                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
598-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
598-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
599            </intent-filter>
600        </receiver>
601        <receiver
601-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
602            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
602-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
603            android:directBootAware="false"
603-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
604            android:enabled="false"
604-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
605            android:exported="false" >
605-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
606            <intent-filter>
606-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
607                <action android:name="android.intent.action.BATTERY_OKAY" />
607-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
607-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
608                <action android:name="android.intent.action.BATTERY_LOW" />
608-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
608-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
609            </intent-filter>
610        </receiver>
611        <receiver
611-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
612            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
612-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
613            android:directBootAware="false"
613-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
614            android:enabled="false"
614-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
615            android:exported="false" >
615-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
616            <intent-filter>
616-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
617                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
617-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
617-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
618                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
618-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
618-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
619            </intent-filter>
620        </receiver>
621        <receiver
621-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
622            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
622-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
623            android:directBootAware="false"
623-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
624            android:enabled="false"
624-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
625            android:exported="false" >
625-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
626            <intent-filter>
626-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
627                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
627-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
627-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
628            </intent-filter>
629        </receiver>
630        <receiver
630-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
631            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
631-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
632            android:directBootAware="false"
632-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
633            android:enabled="false"
633-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
634            android:exported="false" >
634-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
635            <intent-filter>
635-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
636                <action android:name="android.intent.action.BOOT_COMPLETED" />
636-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
636-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
637                <action android:name="android.intent.action.TIME_SET" />
637-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
637-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
638                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
638-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
638-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
639            </intent-filter>
640        </receiver>
641        <receiver
641-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
642            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
642-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
643            android:directBootAware="false"
643-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
644            android:enabled="@bool/enable_system_alarm_service_default"
644-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
645            android:exported="false" >
645-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
646            <intent-filter>
646-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
647                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
647-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
647-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
648            </intent-filter>
649        </receiver>
650        <receiver
650-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
651            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
651-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
652            android:directBootAware="false"
652-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
653            android:enabled="true"
653-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
654            android:exported="true"
654-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
655            android:permission="android.permission.DUMP" >
655-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
656            <intent-filter>
656-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
657                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
657-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
657-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
658            </intent-filter>
659        </receiver>
660        <!--
661         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
662         with the application context. This config is merged in with the host app's manifest,
663         but there can only be one provider with the same authority activated at any given
664         point; so if the end user has two or more different apps that use Facebook SDK, only the
665         first one will be able to use the provider. To work around this problem, we use the
666         following placeholder in the authority to identify each host application as if it was
667         a completely different provider.
668        -->
669        <provider
669-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:32:9-35:40
670            android:name="com.facebook.internal.FacebookInitProvider"
670-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:33:13-70
671            android:authorities="com.scoopt.inpress.FacebookInitProvider"
671-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:34:13-72
672            android:exported="false" />
672-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:35:13-37
673
674        <receiver
674-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:37:9-43:20
675            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
675-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:38:13-86
676            android:exported="false" >
676-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:39:13-37
677            <intent-filter>
677-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:40:13-42:29
678                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
678-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:41:17-95
678-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:41:25-92
679            </intent-filter>
680        </receiver>
681        <receiver
681-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:44:9-50:20
682            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
682-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:45:13-118
683            android:exported="false" >
683-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:46:13-37
684            <intent-filter>
684-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:47:13-49:29
685                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
685-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:48:17-103
685-->[com.facebook.android:facebook-core:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0cd5341ec8b2181562cbaa94dcc6c77\transformed\facebook-core-18.1.3\AndroidManifest.xml:48:25-100
686            </intent-filter>
687        </receiver>
688        <receiver
688-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
689            android:name="androidx.profileinstaller.ProfileInstallReceiver"
689-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
690            android:directBootAware="false"
690-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
691            android:enabled="true"
691-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
692            android:exported="true"
692-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
693            android:permission="android.permission.DUMP" >
693-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
694            <intent-filter>
694-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
695                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
695-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
695-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
696            </intent-filter>
697            <intent-filter>
697-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
698                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
698-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
698-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
699            </intent-filter>
700            <intent-filter>
700-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
701                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
701-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
701-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
702            </intent-filter>
703            <intent-filter>
703-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
704                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
704-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
704-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
705            </intent-filter>
706        </receiver>
707
708        <service
708-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
709            android:name="androidx.room.MultiInstanceInvalidationService"
709-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
710            android:directBootAware="true"
710-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
711            android:exported="false" />
711-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
712        <service
712-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
713            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
713-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
714            android:exported="false" >
714-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
715            <meta-data
715-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
716                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
716-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
717                android:value="cct" />
717-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
718        </service>
719        <service
719-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
720            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
720-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
721            android:exported="false"
721-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
722            android:permission="android.permission.BIND_JOB_SERVICE" >
722-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
723        </service>
724
725        <receiver
725-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
726            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
726-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
727            android:exported="false" />
727-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
728
729        <service
729-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:19:9-21:40
730            android:name="io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService"
730-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:20:13-132
731            android:exported="false" />
731-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:21:13-37
732        <service
732-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:22:9-24:40
733            android:name="io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService"
733-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:23:13-127
734            android:exported="false" />
734-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:24:13-37
735        <service
735-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:25:9-27:40
736            android:name="io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService"
736-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:26:13-120
737            android:exported="false" />
737-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:27:13-37
738    </application>
739
740</manifest>
