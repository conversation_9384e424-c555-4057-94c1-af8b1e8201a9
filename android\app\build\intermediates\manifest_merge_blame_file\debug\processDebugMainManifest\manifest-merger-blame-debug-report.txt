1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.scoopt.inpress"
4    android:versionCode="235"
5    android:versionName="235.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:3-75
11-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:4:3-62
14-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:4:20-60
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:5:3-64
15-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:5:20-62
16    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
16-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:6:3-77
16-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:6:20-75
17    <uses-permission android:name="android.permission.READ_CONTACTS" />
17-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:7:3-69
17-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:7:20-67
18    <uses-permission
18-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:8:3-77
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:8:20-75
20        android:maxSdkVersion="32" />
20-->[:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-35
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:9:3-68
21-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:9:20-66
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:11:3-63
22-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:11:20-61
23    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
23-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:12:3-70
23-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:12:20-68
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:13:3-78
24-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:13:20-76
25    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
25-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:14:3-76
25-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:14:20-74
26
27    <queries>
27-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:15:3-21:13
28        <intent>
28-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:16:5-20:14
29            <action android:name="android.intent.action.VIEW" />
29-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
29-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
30
31            <category android:name="android.intent.category.BROWSABLE" />
31-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
31-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
32
33            <data android:scheme="https" />
33-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
33-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
34        </intent>
35        <intent>
35-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
36            <action android:name="android.intent.action.SEND" />
36-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
36-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
37
38            <category android:name="android.intent.category.DEFAULT" />
38-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
38-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
39
40            <data android:mimeType="application/octet-stream" />
40-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
41        </intent>
42        <intent>
42-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:18
43            <action android:name="android.intent.action.SEND" />
43-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
43-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
44
45            <category android:name="android.intent.category.DEFAULT" />
45-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
45-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
46
47            <data android:mimeType="text/x-vcard" />
47-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
48        </intent>
49        <intent>
49-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-28:18
50            <action android:name="android.intent.action.SEND" />
50-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
50-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
51
52            <category android:name="android.intent.category.DEFAULT" />
52-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
52-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
53
54            <data android:mimeType="text/vcard" />
54-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
55        </intent>
56        <intent>
56-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:9-31:18
57            <action android:name="android.intent.action.EDIT" />
57-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-65
57-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:21-62
58        </intent>
59        <intent>
59-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:18
60            <action android:name="android.intent.action.INSERT" />
60-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-67
60-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:21-64
61        </intent>
62
63        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
63-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
63-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
64        <intent>
64-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
65            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
65-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
65-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
66        </intent>
67        <intent>
67-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
68
69            <!-- Required for picking images from the camera roll if targeting API 30 -->
70            <action android:name="android.media.action.IMAGE_CAPTURE" />
70-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
70-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
71        </intent>
72        <intent>
72-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
73
74            <!-- Required for picking images from the camera if targeting API 30 -->
75            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
75-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
75-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
76        </intent>
77        <intent>
77-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
78
79            <!-- Required for file sharing if targeting API 30 -->
80            <action android:name="android.intent.action.SEND" />
80-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
80-->[:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
81
82            <data android:mimeType="*/*" />
82-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
83        </intent> <!-- Fallback SENDTO -->
84        <intent>
84-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-23:18
85            <action android:name="android.intent.action.SENDTO" />
85-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-67
85-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:21-64
86
87            <category android:name="android.intent.category.DEFAULT" />
87-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
87-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
88            <category android:name="android.intent.category.BROWSABLE" />
88-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
88-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
89
90            <data android:scheme="sms" />
90-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
90-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
91        </intent>
92        <intent>
92-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-31:18
93            <action android:name="android.intent.action.SENDTO" />
93-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-67
93-->[:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:21-64
94
95            <category android:name="android.intent.category.DEFAULT" />
95-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
95-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
96            <category android:name="android.intent.category.BROWSABLE" />
96-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
96-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
97
98            <data android:scheme="smsto" />
98-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
98-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
99        </intent>
100        <intent>
100-->[:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
101
102            <!-- Required for opening tabs if targeting API 30 -->
103            <action android:name="android.support.customtabs.action.CustomTabsService" />
103-->[:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
103-->[:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
104        </intent> <!-- Needs to be explicitly declared on Android R+ -->
105        <package android:name="com.google.android.apps.maps" />
105-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
105-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
106        <package android:name="com.facebook.katana" />
106-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:9-55
106-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:18-52
107
108        <intent>
108-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
109            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
109-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
109-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
110        </intent>
111        <intent>
111-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
112            <action android:name="android.intent.action.GET_CONTENT" />
112-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
112-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
113
114            <category android:name="android.intent.category.OPENABLE" />
114-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
114-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
115
116            <data android:mimeType="*/*" />
116-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
117        </intent>
118        <intent>
118-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:13:9-15:18
119            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
119-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:14:13-91
119-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:14:21-88
120        </intent>
121        <intent>
121-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:11:9-13:18
122            <action android:name="com.appsflyer.referrer.INSTALL_PROVIDER" />
122-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:12:13-78
122-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:12:21-75
123        </intent>
124
125        <package android:name="com.instagram.android" />
125-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:19:9-57
125-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:19:18-54
126        <package android:name="com.facebook.lite" />
126-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:22:9-53
126-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:22:18-50
127        <package android:name="com.samsung.android.mapsagent" />
127-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:34:9-65
127-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:34:18-62
128    </queries>
129
130    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
130-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
130-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
131    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
131-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
131-->[:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
132    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
132-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-76
132-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-73
133    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
133-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-75
133-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-72
134    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
134-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-75
134-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-72
135    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
135-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-90
135-->[:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-87
136    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
136-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
136-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
137    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
137-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
137-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
138
139    <uses-feature
139-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
140        android:glEsVersion="0x00020000"
140-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
141        android:required="true" />
141-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
142
143    <uses-permission android:name="com.android.vending.BILLING" />
143-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:10:5-67
143-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:10:22-64
144    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
144-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
144-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
145    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
145-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
145-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
146    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Support for Google Privacy Sandbox adservices API -->
146-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
146-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
147    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
147-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:5-88
147-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:22-85
148    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
148-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:5-82
148-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:22-79
149    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
149-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:5-92
149-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:22-89
150    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
150-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:5-83
150-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:22-80
151
152    <permission
152-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
153        android:name="com.scoopt.inpress.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
153-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
154        android:protectionLevel="signature" />
154-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
155
156    <uses-permission android:name="com.scoopt.inpress.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Samsung Preload referrer collection -->
156-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
156-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
157    <uses-permission android:name="com.samsung.android.mapsagent.permission.READ_APP_INFO" /> <!-- Needed to interact with Huawei AppGallery ContentProvider -->
157-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:31:5-94
157-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:31:22-91
158    <uses-permission android:name="com.huawei.appmarket.service.commondata.permission.GET_COMMON_DATA" />
158-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:38:5-106
158-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:38:22-103
159    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
159-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
159-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
160    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
160-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:5-98
160-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:22-95
161    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" /> <!-- for android -->
161-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:5-94
161-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:22-91
162    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
163    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
164    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
165    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
166    <!-- for Samsung -->
167    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
167-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
167-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
168    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
168-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
168-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
169    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
169-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
169-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
170    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
170-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
170-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
171    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
171-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
171-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
172    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
172-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
172-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
173    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
173-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
173-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
174    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
174-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
174-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
175    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
175-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
175-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
176    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
176-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
176-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
177    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
177-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
177-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
178    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
178-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
178-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
179    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
179-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
179-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
180    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
180-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
180-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
181    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
181-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
181-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
182    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
182-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
182-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
183
184    <application
184-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:3-58:17
185        android:name="com.scoopt.inpress.MainApplication"
185-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:16-47
186        android:allowBackup="true"
186-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:162-188
187        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
187-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
188        android:dataExtractionRules="@xml/appsflyer_data_extraction_rules"
188-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:42:9-75
189        android:debuggable="true"
190        android:extractNativeLibs="false"
191        android:fullBackupContent="@xml/appsflyer_backup_rules"
191-->[com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:43:9-64
192        android:icon="@mipmap/ic_launcher"
192-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:81-115
193        android:label="@string/app_name"
193-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:48-80
194        android:requestLegacyExternalStorage="true"
194-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:248-291
195        android:roundIcon="@mipmap/ic_launcher_round"
195-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:116-161
196        android:supportsRtl="true"
196-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:221-247
197        android:theme="@style/AppTheme"
197-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:189-220
198        android:usesCleartextTraffic="true" >
198-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:18-53
199        <meta-data
199-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:5-100
200            android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled"
200-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:16-77
201            android:value="true" />
201-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:78-98
202        <meta-data
202-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:5-103
203            android:name="com.facebook.sdk.ApplicationId"
203-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:16-61
204            android:value="@string/facebook_app_id" />
204-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:62-101
205        <meta-data
205-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:5-89
206            android:name="com.facebook.sdk.ApplicationName"
206-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:16-63
207            android:value="InPress" />
207-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:64-87
208        <meta-data
208-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:5-87
209            android:name="com.facebook.sdk.AutoInitEnabled"
209-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:16-63
210            android:value="false" />
210-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:64-85
211        <meta-data
211-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:5-94
212            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
212-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:16-71
213            android:value="true" />
213-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:72-92
214        <meta-data
214-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:5-107
215            android:name="com.facebook.sdk.ClientToken"
215-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:16-59
216            android:value="@string/facebook_client_token" />
216-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:60-105
217        <meta-data
217-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:5-119
218            android:name="com.google.android.geo.API_KEY"
218-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:16-61
219            android:value="AIzaSyB08un4GCREXiuy0FSKQ43vUM1ccU9bgOI" />
219-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:62-117
220        <meta-data
220-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:5-83
221            android:name="expo.modules.updates.ENABLED"
221-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:16-59
222            android:value="false" />
222-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:60-81
223        <meta-data
223-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:5-119
224            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
224-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:16-72
225            android:value="@string/expo_runtime_version" />
225-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:73-117
226        <meta-data
226-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:5-105
227            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
227-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:16-80
228            android:value="ALWAYS" />
228-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:81-103
229        <meta-data
229-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:5-99
230            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
230-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:16-79
231            android:value="0" />
231-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:80-97
232
233        <activity
233-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:5-47:16
234            android:name="com.scoopt.inpress.MainActivity"
234-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:15-43
235            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
235-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:44-157
236            android:exported="true"
236-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:279-302
237            android:launchMode="singleTask"
237-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:158-189
238            android:screenOrientation="portrait"
238-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:303-339
239            android:theme="@style/Theme.App.SplashScreen"
239-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:233-278
240            android:windowSoftInputMode="adjustResize" >
240-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:190-232
241            <intent-filter>
241-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:35:7-38:23
242                <action android:name="android.intent.action.MAIN" />
242-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:36:9-60
242-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:36:17-58
243
244                <category android:name="android.intent.category.LAUNCHER" />
244-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:37:9-68
244-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:37:19-66
245            </intent-filter>
246            <intent-filter>
246-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:39:7-46:23
247                <action android:name="android.intent.action.VIEW" />
247-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
247-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
248
249                <category android:name="android.intent.category.DEFAULT" />
249-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
249-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
250                <category android:name="android.intent.category.BROWSABLE" />
250-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
250-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
251
252                <data android:scheme="myapp" />
252-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
252-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
253                <data android:scheme="com.scoopt.inpress" />
253-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
253-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
254                <data android:scheme="exp+inpress-expo-router" />
254-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
254-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
255            </intent-filter>
256        </activity>
257        <activity
257-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:5-178
258            android:name="com.facebook.FacebookActivity"
258-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:15-59
259            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
259-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:60-143
260            android:label="@string/app_name"
260-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:144-176
261            android:theme="@style/com_facebook_activity_theme" />
261-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:23:13-63
262        <activity
262-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:5-56:16
263            android:name="com.facebook.CustomTabActivity"
263-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:15-60
264            android:exported="true" >
264-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:61-84
265            <intent-filter>
265-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:50:7-55:23
266                <action android:name="android.intent.action.VIEW" />
266-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
266-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
267
268                <category android:name="android.intent.category.DEFAULT" />
268-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
268-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
269                <category android:name="android.intent.category.BROWSABLE" />
269-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
269-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
270
271                <data android:scheme="@string/fb_login_protocol_scheme" />
271-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
271-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
272            </intent-filter>
273            <intent-filter>
273-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
274                <action android:name="android.intent.action.VIEW" />
274-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
274-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
275
276                <category android:name="android.intent.category.DEFAULT" />
276-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
276-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
277                <category android:name="android.intent.category.BROWSABLE" />
277-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
277-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
278
279                <data
279-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
280                    android:host="cct.com.scoopt.inpress"
281                    android:scheme="fbconnect" />
281-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
282            </intent-filter>
283        </activity>
284
285        <uses-library
285-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:5-83
286            android:name="org.apache.http.legacy"
286-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:19-56
287            android:required="false" />
287-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:57-81
288
289        <provider
289-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
290            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
290-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
291            android:authorities="com.scoopt.inpress.fileprovider"
291-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
292            android:exported="false"
292-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
293            android:grantUriPermissions="true" >
293-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
294            <meta-data
294-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
295                android:name="android.support.FILE_PROVIDER_PATHS"
295-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
296                android:resource="@xml/file_provider_paths" />
296-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
297        </provider>
298
299        <meta-data
299-->[:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:37
300            android:name="io.sentry.auto-init"
300-->[:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
301            android:value="false" />
301-->[:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-34
302
303        <property
303-->[:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-47:48
304            android:name="android.adservices.AD_SERVICES_CONFIG"
304-->[:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-65
305            android:resource="@xml/ad_services_config" />
305-->[:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-55
306
307        <activity
307-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
308            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
308-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
309            android:exported="true"
309-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
310            android:launchMode="singleTask"
310-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
311            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
311-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
312            <intent-filter>
312-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
313                <action android:name="android.intent.action.VIEW" />
313-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
313-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
314
315                <category android:name="android.intent.category.DEFAULT" />
315-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
315-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
316                <category android:name="android.intent.category.BROWSABLE" />
316-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
316-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
317
318                <data android:scheme="expo-dev-launcher" />
318-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
318-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
319            </intent-filter>
320        </activity>
321        <activity
321-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
322            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
322-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
323            android:screenOrientation="portrait"
323-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
324            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
324-->[:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
325        <activity
325-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
326            android:name="expo.modules.devmenu.DevMenuActivity"
326-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
327            android:exported="true"
327-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
328            android:launchMode="singleTask"
328-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
329            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
329-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
330            <intent-filter>
330-->[:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
331                <action android:name="android.intent.action.VIEW" />
331-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
331-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
332
333                <category android:name="android.intent.category.DEFAULT" />
333-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
333-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
334                <category android:name="android.intent.category.BROWSABLE" />
334-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
334-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
335
336                <data android:scheme="expo-dev-menu" />
336-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
336-->C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
337            </intent-filter>
338        </activity>
339
340        <provider
340-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
341            android:name="expo.modules.filesystem.FileSystemFileProvider"
341-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
342            android:authorities="com.scoopt.inpress.FileSystemFileProvider"
342-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
343            android:exported="false"
343-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
344            android:grantUriPermissions="true" >
344-->[:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
345            <meta-data
345-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
346                android:name="android.support.FILE_PROVIDER_PATHS"
346-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
347                android:resource="@xml/file_system_provider_paths" />
347-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
348        </provider>
349
350        <service
350-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
351            android:name="com.google.android.gms.metadata.ModuleDependencies"
351-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
352            android:enabled="false"
352-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
353            android:exported="false" >
353-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
354            <intent-filter>
354-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
355                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
355-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
355-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
356            </intent-filter>
357
358            <meta-data
358-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
359                android:name="photopicker_activity:0:required"
359-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
360                android:value="" />
360-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
361        </service>
362
363        <activity
363-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
364            android:name="com.canhub.cropper.CropImageActivity"
364-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
365            android:exported="true"
365-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
366            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
366-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
367        <provider
367-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
368            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
368-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
369            android:authorities="com.scoopt.inpress.ImagePickerFileProvider"
369-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
370            android:exported="false"
370-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
371            android:grantUriPermissions="true" >
371-->[:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
372            <meta-data
372-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
373                android:name="android.support.FILE_PROVIDER_PATHS"
373-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
374                android:resource="@xml/image_picker_provider_paths" />
374-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
375        </provider>
376
377        <service
377-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
378            android:name="expo.modules.location.services.LocationTaskService"
378-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
379            android:exported="false"
379-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
380            android:foregroundServiceType="location" />
380-->[:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
381        <service
381-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
382            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
382-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
383            android:exported="false" >
383-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
384            <intent-filter android:priority="-1" >
384-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
384-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
385                <action android:name="com.google.firebase.MESSAGING_EVENT" />
385-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
385-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
386            </intent-filter>
387        </service>
388
389        <receiver
389-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
390            android:name="expo.modules.notifications.service.NotificationsService"
390-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
391            android:enabled="true"
391-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
392            android:exported="false" >
392-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
393            <intent-filter android:priority="-1" >
393-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
393-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
394                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
394-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
394-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
395                <action android:name="android.intent.action.BOOT_COMPLETED" />
395-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
395-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
396                <action android:name="android.intent.action.REBOOT" />
396-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
396-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
397                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
397-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
397-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
398                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
398-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
398-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
399                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
399-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
399-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
400            </intent-filter>
401        </receiver>
402
403        <activity
403-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
404            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
404-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
405            android:excludeFromRecents="true"
405-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
406            android:exported="false"
406-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
407            android:launchMode="standard"
407-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
408            android:noHistory="true"
408-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
409            android:taskAffinity=""
409-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
410            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
410-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
411
412        <provider
412-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-26:20
413            android:name="expo.modules.sharing.SharingFileProvider"
413-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
414            android:authorities="com.scoopt.inpress.SharingFileProvider"
414-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-71
415            android:exported="false"
415-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
416            android:grantUriPermissions="true" >
416-->[:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-47
417            <meta-data
417-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
418                android:name="android.support.FILE_PROVIDER_PATHS"
418-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
419                android:resource="@xml/sharing_provider_paths" />
419-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
420        </provider>
421
422        <meta-data
422-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
423            android:name="org.unimodules.core.AppLoader#react-native-headless"
423-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
424            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
424-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
425        <meta-data
425-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
426            android:name="com.facebook.soloader.enabled"
426-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
427            android:value="true" />
427-->[:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
428
429        <activity
429-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
430            android:name="com.facebook.react.devsupport.DevSettingsActivity"
430-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
431            android:exported="false" />
431-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
432
433        <meta-data
433-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
434            android:name="com.google.android.gms.version"
434-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
435            android:value="@integer/google_play_services_version" />
435-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
436
437        <activity android:name="com.facebook.CustomTabMainActivity" />
437-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:9-71
437-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:19-68
438
439        <meta-data
439-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
440            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
440-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
441            android:value="GlideModule" />
441-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
442
443        <uses-library
443-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
444            android:name="androidx.camera.extensions.impl"
444-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
445            android:required="false" />
445-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
446
447        <service
447-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
448            android:name="androidx.camera.core.impl.MetadataHolderService"
448-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
449            android:enabled="false"
449-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
450            android:exported="false" >
450-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
451            <meta-data
451-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
452                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
452-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
453                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
453-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
454        </service>
455
456        <provider
456-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
457            android:name="com.canhub.cropper.CropFileProvider"
457-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
458            android:authorities="com.scoopt.inpress.cropper.fileprovider"
458-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
459            android:exported="false"
459-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
460            android:grantUriPermissions="true" >
460-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
461            <meta-data
461-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
462                android:name="android.support.FILE_PROVIDER_PATHS"
462-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
463                android:resource="@xml/library_file_paths" />
463-->[:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
464        </provider>
465
466        <receiver
466-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:8:9-15:20
467            android:name="com.amazon.device.iap.ResponseReceiver"
467-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:9:13-66
468            android:exported="true"
468-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:10:13-36
469            android:permission="com.amazon.inapp.purchasing.Permission.NOTIFY" >
469-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:11:13-79
470            <intent-filter>
470-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:12:13-14:29
471                <action android:name="com.amazon.inapp.purchasing.NOTIFY" />
471-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:13:17-77
471-->[com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:13:25-74
472            </intent-filter>
473        </receiver>
474
475        <activity
475-->[com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:10:9-13:75
476            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
476-->[com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:11:13-97
477            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
477-->[com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:12:13-96
478            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
478-->[com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:13:13-72
479
480        <meta-data
480-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:19:9-21:37
481            android:name="com.google.android.play.billingclient.version"
481-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:20:13-73
482            android:value="6.2.1" />
482-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:21:13-34
483
484        <activity
484-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:23:9-27:75
485            android:name="com.android.billingclient.api.ProxyBillingActivity"
485-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:24:13-78
486            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
486-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:25:13-96
487            android:exported="false"
487-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:26:13-37
488            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
488-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:27:13-72
489        <activity
489-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:28:9-32:75
490            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
490-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:29:13-80
491            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
491-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:30:13-96
492            android:exported="false"
492-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:31:13-37
493            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
493-->[com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:32:13-72
494
495        <receiver
495-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
496            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
496-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
497            android:exported="true"
497-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
498            android:permission="com.google.android.c2dm.permission.SEND" >
498-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
499            <intent-filter>
499-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
500                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
500-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
500-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
501            </intent-filter>
502
503            <meta-data
503-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
504                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
504-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
505                android:value="true" />
505-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
506        </receiver>
507        <!--
508             FirebaseMessagingService performs security checks at runtime,
509             but set to not exported to explicitly avoid allowing another app to call it.
510        -->
511        <service
511-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
512            android:name="com.google.firebase.messaging.FirebaseMessagingService"
512-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
513            android:directBootAware="true"
513-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
514            android:exported="false" >
514-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
515            <intent-filter android:priority="-500" >
515-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
515-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
516                <action android:name="com.google.firebase.MESSAGING_EVENT" />
516-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
516-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
517            </intent-filter>
518        </service>
519        <service
519-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
520            android:name="com.google.firebase.components.ComponentDiscoveryService"
520-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
521            android:directBootAware="true"
521-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
522            android:exported="false" >
522-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
523            <meta-data
523-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
524                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
524-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
525                android:value="com.google.firebase.components.ComponentRegistrar" />
525-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
526            <meta-data
526-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
527                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
527-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
528                android:value="com.google.firebase.components.ComponentRegistrar" />
528-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
529            <meta-data
529-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
530                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
530-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
531                android:value="com.google.firebase.components.ComponentRegistrar" />
531-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
532            <meta-data
532-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
533                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
533-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
534                android:value="com.google.firebase.components.ComponentRegistrar" />
534-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
535            <meta-data
535-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
536                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
536-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
537                android:value="com.google.firebase.components.ComponentRegistrar" />
537-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
538            <meta-data
538-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
539                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
539-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
540                android:value="com.google.firebase.components.ComponentRegistrar" />
540-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
541            <meta-data
541-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
542                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
542-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
543                android:value="com.google.firebase.components.ComponentRegistrar" />
543-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
544        </service>
545        <service
545-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
546            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
546-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
547            android:directBootAware="true"
547-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
548            android:exported="false" >
548-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
549            <meta-data
549-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
550                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
550-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
551                android:value="com.google.firebase.components.ComponentRegistrar" />
551-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
552            <meta-data
552-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
553                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
553-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
554                android:value="com.google.firebase.components.ComponentRegistrar" />
554-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
555            <meta-data
555-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
556                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
556-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
557                android:value="com.google.firebase.components.ComponentRegistrar" />
557-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
558        </service>
559
560        <provider
560-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
561            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
561-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
562            android:authorities="com.scoopt.inpress.mlkitinitprovider"
562-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
563            android:exported="false"
563-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
564            android:initOrder="99" />
564-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
565        <provider
565-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
566            android:name="com.google.firebase.provider.FirebaseInitProvider"
566-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
567            android:authorities="com.scoopt.inpress.firebaseinitprovider"
567-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
568            android:directBootAware="true"
568-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
569            android:exported="false"
569-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
570            android:initOrder="100" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
570-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
571        <provider
571-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:12:9-15:40
572            android:name="io.sentry.android.core.SentryInitProvider"
572-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:13:13-69
573            android:authorities="com.scoopt.inpress.SentryInitProvider"
573-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:14:13-70
574            android:exported="false" />
574-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:15:13-37
575        <provider
575-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:16:9-20:39
576            android:name="io.sentry.android.core.SentryPerformanceProvider"
576-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:17:13-76
577            android:authorities="com.scoopt.inpress.SentryPerformanceProvider"
577-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:18:13-77
578            android:exported="false"
578-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:19:13-37
579            android:initOrder="200" />
579-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:20:13-36
580        <provider
580-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
581            android:name="androidx.startup.InitializationProvider"
581-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
582            android:authorities="com.scoopt.inpress.androidx-startup"
582-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
583            android:exported="false" >
583-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
584            <meta-data
584-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
585                android:name="androidx.work.WorkManagerInitializer"
585-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
586                android:value="androidx.startup" />
586-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
587            <meta-data
587-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
588                android:name="androidx.emoji2.text.EmojiCompatInitializer"
588-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
589                android:value="androidx.startup" />
589-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
590            <meta-data
590-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
591                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
591-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
592                android:value="androidx.startup" />
592-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
593            <meta-data
593-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
594                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
594-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
595                android:value="androidx.startup" />
595-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
596        </provider>
597
598        <service
598-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
599            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
599-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
600            android:directBootAware="false"
600-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
601            android:enabled="@bool/enable_system_alarm_service_default"
601-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
602            android:exported="false" />
602-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
603        <service
603-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
604            android:name="androidx.work.impl.background.systemjob.SystemJobService"
604-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
605            android:directBootAware="false"
605-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
606            android:enabled="@bool/enable_system_job_service_default"
606-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
607            android:exported="true"
607-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
608            android:permission="android.permission.BIND_JOB_SERVICE" />
608-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
609        <service
609-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
610            android:name="androidx.work.impl.foreground.SystemForegroundService"
610-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
611            android:directBootAware="false"
611-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
612            android:enabled="@bool/enable_system_foreground_service_default"
612-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
613            android:exported="false" />
613-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
614
615        <receiver
615-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
616            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
616-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
617            android:directBootAware="false"
617-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
618            android:enabled="true"
618-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
619            android:exported="false" />
619-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
620        <receiver
620-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
621            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
621-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
622            android:directBootAware="false"
622-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
623            android:enabled="false"
623-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
624            android:exported="false" >
624-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
625            <intent-filter>
625-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
626                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
626-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
626-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
627                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
627-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
627-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
628            </intent-filter>
629        </receiver>
630        <receiver
630-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
631            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
631-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
632            android:directBootAware="false"
632-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
633            android:enabled="false"
633-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
634            android:exported="false" >
634-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
635            <intent-filter>
635-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
636                <action android:name="android.intent.action.BATTERY_OKAY" />
636-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
636-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
637                <action android:name="android.intent.action.BATTERY_LOW" />
637-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
637-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
638            </intent-filter>
639        </receiver>
640        <receiver
640-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
641            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
641-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
642            android:directBootAware="false"
642-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
643            android:enabled="false"
643-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
644            android:exported="false" >
644-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
645            <intent-filter>
645-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
646                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
646-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
646-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
647                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
647-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
647-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
648            </intent-filter>
649        </receiver>
650        <receiver
650-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
651            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
651-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
652            android:directBootAware="false"
652-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
653            android:enabled="false"
653-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
654            android:exported="false" >
654-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
655            <intent-filter>
655-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
656                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
656-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
656-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
657            </intent-filter>
658        </receiver>
659        <receiver
659-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
660            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
660-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
661            android:directBootAware="false"
661-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
662            android:enabled="false"
662-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
663            android:exported="false" >
663-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
664            <intent-filter>
664-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
665                <action android:name="android.intent.action.BOOT_COMPLETED" />
665-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
665-->[:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
666                <action android:name="android.intent.action.TIME_SET" />
666-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
666-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
667                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
667-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
667-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
668            </intent-filter>
669        </receiver>
670        <receiver
670-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
671            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
671-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
672            android:directBootAware="false"
672-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
673            android:enabled="@bool/enable_system_alarm_service_default"
673-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
674            android:exported="false" >
674-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
675            <intent-filter>
675-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
676                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
676-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
676-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
677            </intent-filter>
678        </receiver>
679        <receiver
679-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
680            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
680-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
681            android:directBootAware="false"
681-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
682            android:enabled="true"
682-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
683            android:exported="true"
683-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
684            android:permission="android.permission.DUMP" >
684-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
685            <intent-filter>
685-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
686                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
686-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
686-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
687            </intent-filter>
688        </receiver>
689        <!--
690         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
691         with the application context. This config is merged in with the host app's manifest,
692         but there can only be one provider with the same authority activated at any given
693         point; so if the end user has two or more different apps that use Facebook SDK, only the
694         first one will be able to use the provider. To work around this problem, we use the
695         following placeholder in the authority to identify each host application as if it was
696         a completely different provider.
697        -->
698        <provider
698-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
699            android:name="com.facebook.internal.FacebookInitProvider"
699-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:33:13-70
700            android:authorities="com.scoopt.inpress.FacebookInitProvider"
700-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:34:13-72
701            android:exported="false" />
701-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:35:13-37
702
703        <receiver
703-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
704            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
704-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:38:13-86
705            android:exported="false" >
705-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:39:13-37
706            <intent-filter>
706-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
707                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
707-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:17-95
707-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:25-92
708            </intent-filter>
709        </receiver>
710        <receiver
710-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
711            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
711-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:45:13-118
712            android:exported="false" >
712-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:46:13-37
713            <intent-filter>
713-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
714                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
714-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:17-103
714-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:25-100
715            </intent-filter>
716        </receiver>
717
718        <activity
718-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
719            android:name="com.google.android.gms.common.api.GoogleApiActivity"
719-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
720            android:exported="false"
720-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
721            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
721-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
722
723        <receiver
723-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
724            android:name="androidx.profileinstaller.ProfileInstallReceiver"
724-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
725            android:directBootAware="false"
725-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
726            android:enabled="true"
726-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
727            android:exported="true"
727-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
728            android:permission="android.permission.DUMP" >
728-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
729            <intent-filter>
729-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
730                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
730-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
730-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
731            </intent-filter>
732            <intent-filter>
732-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
733                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
733-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
733-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
734            </intent-filter>
735            <intent-filter>
735-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
736                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
736-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
736-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
737            </intent-filter>
738            <intent-filter>
738-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
739                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
739-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
739-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
740            </intent-filter>
741        </receiver>
742
743        <service
743-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
744            android:name="androidx.room.MultiInstanceInvalidationService"
744-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
745            android:directBootAware="true"
745-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
746            android:exported="false" />
746-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
747        <service
747-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
748            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
748-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
749            android:exported="false" >
749-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
750            <meta-data
750-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
751                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
751-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
752                android:value="cct" />
752-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
753        </service>
754        <service
754-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
755            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
755-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
756            android:exported="false"
756-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
757            android:permission="android.permission.BIND_JOB_SERVICE" >
757-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
758        </service>
759
760        <receiver
760-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
761            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
761-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
762            android:exported="false" />
762-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
763
764        <service
764-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:19:9-21:40
765            android:name="io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService"
765-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:20:13-132
766            android:exported="false" />
766-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:21:13-37
767        <service
767-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:22:9-24:40
768            android:name="io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService"
768-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:23:13-127
769            android:exported="false" />
769-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:24:13-37
770        <service
770-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:25:9-27:40
771            android:name="io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService"
771-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:26:13-120
772            android:exported="false" />
772-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:27:13-37
773    </application>
774
775</manifest>
