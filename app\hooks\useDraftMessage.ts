import { useCallback, useEffect, useState } from "react"
import type { Channel } from "stream-chat"
import AsyncStorage from "@react-native-async-storage/async-storage"

const STORAGE_KEY = "@chat/drafts"

const getDrafts = async () =>
  JSON.parse((await AsyncStorage.getItem(STORAGE_KEY)) || "{}")

const updateDraft = async (key: string, value: string) => {
  const drafts = await getDrafts()

  if (!value) {
    delete drafts[key]
  } else {
    drafts[key] = value
  }

  await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(drafts))
}

const useDraftMessage = (channel: Channel) => {
  const [draftMessage, setDraftMessage] = useState<string>()

  const handleInputChange = useCallback(
    (value: string) => {
      updateDraft(channel.cid, value)
      setDraftMessage(value)
    },
    [channel.cid],
  )

  const getMainInputDraft = useCallback(async () => {
    const drafts = await getDrafts()
    return (drafts[channel.cid] || "") as string
  }, [channel.cid])

  useEffect(() => {
    getMainInputDraft().then(setDraftMessage)
  }, [channel.cid])

  return {
    draftMessage,
    handleInputChange,
  }
}

export default useDraftMessage
