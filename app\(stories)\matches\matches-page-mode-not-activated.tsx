import { USER } from "../(account)/account"
import { MatchesPage_ } from "@/components/MatchesPage"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"

export default function Story() {
  return (
    <MatchesPage_
      matches={[]}
      activeConnectionMode={ConnectionMode.Dates}
      user={{
        ...USER,
        datesModeIsActivated: false,
        friendsModeIsActivated: true,
      }}
      testMode={true}
      onSelectChannel={async () => {}}
    />
  )
}
