import { Level } from "@/types/levels"
import { calculateProgress } from "@/utils/levels"

describe("Levels", () => {
  it("calculates levels progress for first level", () => {
    const points = 40
    const currentLevel: Partial<Level> = { pointsRequired: 30 }
    const nextLevel: Partial<Level> = { pointsRequired: 100 }
    const progress = calculateProgress({
      points,
      currentLevel: currentLevel as Level,
      nextLevel: nextLevel as Level,
    })
    expect(progress).toBeCloseTo(0.14, 2)
  })

  it("calculates levels progress for before first level", () => {
    const points = 20
    const nextLevel: Partial<Level> = { pointsRequired: 30 }
    const progress = calculateProgress({
      points,
      currentLevel: undefined,
      nextLevel: nextLevel as Level,
    })
    expect(progress).toBeCloseTo(0.67, 2)
  })

  it("calculates levels progress for zero points", () => {
    const points = 0
    const nextLevel: Partial<Level> = { pointsRequired: 30 }
    const progress = calculateProgress({
      points,
      currentLevel: undefined,
      nextLevel: nextLevel as Level,
    })
    expect(progress).toEqual(0)
  })

  it("calculates levels progress for final level", () => {
    const points = 150
    const currentLevel: Partial<Level> = { pointsRequired: 100 }
    const progress = calculateProgress({
      points,
      currentLevel: currentLevel as Level,
      nextLevel: undefined,
    })
    expect(progress).toEqual(1)
  })
})
