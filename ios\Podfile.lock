PODS:
  - AppsFlyerFramework (6.16.2):
    - AppsFlyerFramework/Main (= 6.16.2)
  - AppsFlyerFramework/Main (6.16.2)
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXApplication (6.0.2):
    - ExpoModulesCore
  - EXAV (15.0.2):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (17.0.8):
    - ExpoModulesCore
  - EXImageLoader (5.0.0):
    - ExpoModulesCore
    - React-Core
  - EXJSONUtils (0.14.0)
  - EXManifests (0.15.8):
    - ExpoModulesCore
  - EXNotifications (0.29.14):
    - ExpoModulesCore
  - Expo (52.0.46):
    - ExpoModulesCore
  - expo-dev-client (5.0.20):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (5.0.35):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Main (= 5.0.35)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Main (5.0.35):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Unsafe (5.0.35):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu (6.0.25):
    - DoubleConversion
    - expo-dev-menu/Main (= 6.0.25)
    - expo-dev-menu/ReactNativeCompatibles (= 6.0.25)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu-interface (1.9.3)
  - expo-dev-menu/Main (6.0.25):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/ReactNativeCompatibles (6.0.25):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/SafeAreaView (6.0.25):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/Vendored (6.0.25):
    - DoubleConversion
    - expo-dev-menu/SafeAreaView
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAdapterFBSDKNext (13.4.1):
    - ExpoModulesCore
    - FBSDKCoreKit
    - React-Core
  - ExpoAsset (11.0.5):
    - ExpoModulesCore
  - ExpoBlur (14.0.3):
    - ExpoModulesCore
  - ExpoCamera (16.0.18):
    - ExpoModulesCore
    - ZXingObjC/OneD
    - ZXingObjC/PDF417
  - ExpoContacts (14.0.5):
    - ExpoModulesCore
  - ExpoCrypto (14.0.2):
    - ExpoModulesCore
  - ExpoDevice (7.0.3):
    - ExpoModulesCore
  - ExpoFileSystem (18.0.12):
    - ExpoModulesCore
  - ExpoFont (13.0.4):
    - ExpoModulesCore
  - ExpoHead (4.0.20):
    - ExpoModulesCore
  - ExpoImage (2.0.7):
    - ExpoModulesCore
    - libavif/libdav1d
    - SDWebImage (~> 5.19.1)
    - SDWebImageAVIFCoder (~> 0.11.0)
    - SDWebImageSVGCoder (~> 1.7.0)
  - ExpoImageManipulator (13.0.6):
    - EXImageLoader
    - ExpoModulesCore
    - SDWebImageWebPCoder
  - ExpoImagePicker (16.0.6):
    - ExpoModulesCore
  - ExpoKeepAwake (14.0.3):
    - ExpoModulesCore
  - ExpoLinearGradient (14.0.2):
    - ExpoModulesCore
  - ExpoLinking (7.0.5):
    - ExpoModulesCore
  - ExpoLocalization (16.0.1):
    - ExpoModulesCore
  - ExpoLocation (18.0.10):
    - ExpoModulesCore
  - ExpoMediaLibrary (17.0.6):
    - ExpoModulesCore
    - React-Core
  - ExpoModulesCore (2.2.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoSharing (13.0.1):
    - ExpoModulesCore
  - ExpoSMS (13.0.1):
    - ExpoModulesCore
  - ExpoSplashScreen (0.29.24):
    - ExpoModulesCore
  - ExpoSystemUI (4.0.9):
    - ExpoModulesCore
  - ExpoTrackingTransparency (5.1.1):
    - ExpoModulesCore
  - ExpoWebBrowser (14.0.2):
    - ExpoModulesCore
  - EXUpdatesInterface (1.0.0):
    - ExpoModulesCore
  - fast_float (6.1.4)
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBLazyVector (0.76.9)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - FBSDKGamingServicesKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
    - FBSDKShareKit (= 18.0.0)
  - FBSDKLoginKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
  - FBSDKShareKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - hermes-engine (0.76.9):
    - hermes-engine/Pre-built (= 0.76.9)
  - hermes-engine/Pre-built (0.76.9)
  - libavif/core (0.11.1)
  - libavif/libdav1d (0.11.1):
    - libavif/core
    - libdav1d (>= 0.6.0)
  - libdav1d (1.2.0)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.2.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - lottie-ios (= 4.5.0)
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - Mixpanel-swift (4.4.0):
    - Mixpanel-swift/Complete (= 4.4.0)
  - Mixpanel-swift/Complete (4.4.0)
  - MixpanelReactNative (3.0.9):
    - Mixpanel-swift (= 4.4.0)
    - React-Core
  - PurchasesHybridCommon (11.1.1):
    - RevenueCat (= 4.43.2)
  - RCT-Folly (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Default (= 2024.10.14.00)
  - RCT-Folly/Default (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCT-Folly/Fabric (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCTDeprecation (0.76.9)
  - RCTRequired (0.76.9)
  - RCTTypeSafety (0.76.9):
    - FBLazyVector (= 0.76.9)
    - RCTRequired (= 0.76.9)
    - React-Core (= 0.76.9)
  - React (0.76.9):
    - React-Core (= 0.76.9)
    - React-Core/DevSupport (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-RCTActionSheet (= 0.76.9)
    - React-RCTAnimation (= 0.76.9)
    - React-RCTBlob (= 0.76.9)
    - React-RCTImage (= 0.76.9)
    - React-RCTLinking (= 0.76.9)
    - React-RCTNetwork (= 0.76.9)
    - React-RCTSettings (= 0.76.9)
    - React-RCTText (= 0.76.9)
    - React-RCTVibration (= 0.76.9)
  - React-callinvoker (0.76.9)
  - React-Core (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - RCTTypeSafety
    - React-Core/CoreModulesHeaders
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage
    - ReactCodegen
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-jsinspector
    - React-logger
    - React-perflogger
    - React-runtimeexecutor
    - React-timing
  - React-debug (0.76.9)
  - React-defaultsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.9)
    - React-Fabric/attributedstring (= 0.76.9)
    - React-Fabric/componentregistry (= 0.76.9)
    - React-Fabric/componentregistrynative (= 0.76.9)
    - React-Fabric/components (= 0.76.9)
    - React-Fabric/core (= 0.76.9)
    - React-Fabric/dom (= 0.76.9)
    - React-Fabric/imagemanager (= 0.76.9)
    - React-Fabric/leakchecker (= 0.76.9)
    - React-Fabric/mounting (= 0.76.9)
    - React-Fabric/observers (= 0.76.9)
    - React-Fabric/scheduler (= 0.76.9)
    - React-Fabric/telemetry (= 0.76.9)
    - React-Fabric/templateprocessor (= 0.76.9)
    - React-Fabric/uimanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.9)
    - React-Fabric/components/root (= 0.76.9)
    - React-Fabric/components/view (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.9)
    - React-FabricComponents/textlayoutmanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.9)
    - React-FabricComponents/components/iostextinput (= 0.76.9)
    - React-FabricComponents/components/modal (= 0.76.9)
    - React-FabricComponents/components/rncore (= 0.76.9)
    - React-FabricComponents/components/safeareaview (= 0.76.9)
    - React-FabricComponents/components/scrollview (= 0.76.9)
    - React-FabricComponents/components/text (= 0.76.9)
    - React-FabricComponents/components/textinput (= 0.76.9)
    - React-FabricComponents/components/unimplementedview (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.9)
  - React-featureflagsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.9):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
  - React-jsiexecutor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-perflogger
  - React-jsinspector (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-perflogger
    - React-runtimeexecutor
  - React-jsitracing (0.76.9):
    - React-jsi
  - React-logger (0.76.9):
    - glog
  - React-Mapbuffer (0.76.9):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-appsflyer (6.16.2):
    - AppsFlyerFramework (= 6.16.2)
    - React
  - react-native-fbsdk-next (13.4.1):
    - React-Core
    - react-native-fbsdk-next/Core (= 13.4.1)
    - react-native-fbsdk-next/Login (= 13.4.1)
    - react-native-fbsdk-next/Share (= 13.4.1)
  - react-native-fbsdk-next/Core (13.4.1):
    - FBSDKCoreKit (~> 18.0)
    - React-Core
  - react-native-fbsdk-next/Login (13.4.1):
    - FBSDKLoginKit (~> 18.0)
    - React-Core
  - react-native-fbsdk-next/Share (13.4.1):
    - FBSDKGamingServicesKit (~> 18.0)
    - FBSDKShareKit (~> 18.0)
    - React-Core
  - react-native-google-maps (1.18.0):
    - Google-Maps-iOS-Utils (= 4.2.2)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-maps (1.18.0):
    - React-Core
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-safe-area-context (4.12.0):
    - React-Core
  - react-native-view-shot (4.0.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-webview (13.12.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.76.9)
  - React-NativeModulesApple (0.76.9):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.9):
    - DoubleConversion
    - RCT-Folly (= 2024.10.14.00)
  - React-performancetimeline (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.9):
    - React-Core/RCTActionSheetHeaders (= 0.76.9)
  - React-RCTAnimation (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.9):
    - React-Core/RCTLinkingHeaders (= 0.76.9)
    - React-jsi (= 0.76.9)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.9)
  - React-RCTNetwork (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.9):
    - React-Core/RCTTextHeaders (= 0.76.9)
    - Yoga
  - React-RCTVibration (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.9)
  - React-rendererdebug (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - React-debug
  - React-rncore (0.76.9)
  - React-RuntimeApple (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.9):
    - React-jsi (= 0.76.9)
  - React-RuntimeHermes (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.9)
  - React-utils (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-debug
    - React-jsi (= 0.76.9)
  - ReactCodegen (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.9):
    - ReactCommon/turbomodule (= 0.76.9)
  - ReactCommon/turbomodule (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi
    - React-logger
    - React-perflogger
    - ReactCommon/turbomodule/bridging (= 0.76.9)
    - ReactCommon/turbomodule/core (= 0.76.9)
  - ReactCommon/turbomodule/bridging (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi (= 0.76.9)
    - React-logger
    - React-perflogger
  - ReactCommon/turbomodule/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-debug (= 0.76.9)
    - React-featureflags (= 0.76.9)
    - React-jsi
    - React-logger
    - React-perflogger
    - React-utils (= 0.76.9)
  - RevenueCat (4.43.2)
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNCPicker (2.9.0):
    - React-Core
  - RNDateTimePicker (8.2.0):
    - React-Core
  - RNGestureHandler (2.20.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNPurchases (7.28.1):
    - PurchasesHybridCommon (= 11.1.1)
    - React-Core
  - RNReanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated/apple (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSentry (6.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Sentry/HybridSDK (= 8.48.0)
    - Yoga
  - RNSVG (15.8.0):
    - React-Core
  - SDWebImage (5.19.7):
    - SDWebImage/Core (= 5.19.7)
  - SDWebImage/Core (5.19.7)
  - SDWebImageAVIFCoder (0.11.0):
    - libavif/core (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageSVGCoder (1.7.0):
    - SDWebImage/Core (~> 5.6)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - Sentry/HybridSDK (8.48.0)
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)
  - ZXingObjC/Core (3.6.9)
  - ZXingObjC/OneD (3.6.9):
    - ZXingObjC/Core
  - ZXingObjC/PDF417 (3.6.9):
    - ZXingObjC/Core

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - EXNotifications (from `../node_modules/expo-notifications/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoAdapterFBSDKNext (from `../node_modules/react-native-fbsdk-next/ios`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoBlur (from `../node_modules/expo-blur/ios`)
  - ExpoCamera (from `../node_modules/expo-camera/ios`)
  - ExpoContacts (from `../node_modules/expo-contacts/ios`)
  - ExpoCrypto (from `../node_modules/expo-crypto/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoHead (from `../node_modules/expo-router/ios`)
  - ExpoImage (from `../node_modules/expo-image/ios`)
  - ExpoImageManipulator (from `../node_modules/expo-image-manipulator/ios`)
  - ExpoImagePicker (from `../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoLinking (from `../node_modules/expo-linking/ios`)
  - ExpoLocalization (from `../node_modules/expo-localization/ios`)
  - ExpoLocation (from `../node_modules/expo-location/ios`)
  - ExpoMediaLibrary (from `../node_modules/expo-media-library/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoSharing (from `../node_modules/expo-sharing/ios`)
  - ExpoSMS (from `../node_modules/expo-sms/ios`)
  - ExpoSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - ExpoSystemUI (from `../node_modules/expo-system-ui/ios`)
  - ExpoTrackingTransparency (from `../node_modules/expo-tracking-transparency/ios`)
  - ExpoWebBrowser (from `../node_modules/expo-web-browser/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils (= 4.2.2)
  - GoogleMaps (= 7.4.0)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - MixpanelReactNative (from `../node_modules/mixpanel-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-fbsdk-next (from `../node_modules/react-native-fbsdk-next`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNPurchases (from `../node_modules/react-native-purchases`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppsFlyerFramework
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKGamingServicesKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - libavif
    - libdav1d
    - libwebp
    - lottie-ios
    - Mixpanel-swift
    - PurchasesHybridCommon
    - RevenueCat
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageSVGCoder
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - ZXingObjC

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  EXNotifications:
    :path: "../node_modules/expo-notifications/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoAdapterFBSDKNext:
    :path: "../node_modules/react-native-fbsdk-next/ios"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../node_modules/expo-blur/ios"
  ExpoCamera:
    :path: "../node_modules/expo-camera/ios"
  ExpoContacts:
    :path: "../node_modules/expo-contacts/ios"
  ExpoCrypto:
    :path: "../node_modules/expo-crypto/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoHead:
    :path: "../node_modules/expo-router/ios"
  ExpoImage:
    :path: "../node_modules/expo-image/ios"
  ExpoImageManipulator:
    :path: "../node_modules/expo-image-manipulator/ios"
  ExpoImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoLinking:
    :path: "../node_modules/expo-linking/ios"
  ExpoLocalization:
    :path: "../node_modules/expo-localization/ios"
  ExpoLocation:
    :path: "../node_modules/expo-location/ios"
  ExpoMediaLibrary:
    :path: "../node_modules/expo-media-library/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoSharing:
    :path: "../node_modules/expo-sharing/ios"
  ExpoSMS:
    :path: "../node_modules/expo-sms/ios"
  ExpoSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  ExpoSystemUI:
    :path: "../node_modules/expo-system-ui/ios"
  ExpoTrackingTransparency:
    :path: "../node_modules/expo-tracking-transparency/ios"
  ExpoWebBrowser:
    :path: "../node_modules/expo-web-browser/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  MixpanelReactNative:
    :path: "../node_modules/mixpanel-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-fbsdk-next:
    :path: "../node_modules/react-native-fbsdk-next"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNPurchases:
    :path: "../node_modules/react-native-purchases"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppsFlyerFramework: fe5303bffcdfd941d5f570c2d21eaaea982e7bdc
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  EXApplication: 27a524f5c3e671c6218220fba04752629466a1a9
  EXAV: 57ea461614a714b8b8fcc72c6fbc298b8f1ec78b
  EXConstants: a1f35b9aabbb3c6791f8e67722579b1ffcdd3f18
  EXImageLoader: 759063a65ab016b836f73972d3bb25404888713d
  EXJSONUtils: 01fc7492b66c234e395dcffdd5f53439c5c29c93
  EXManifests: 77e77c283c4c53a50b9bddebed25abe93f0b48f1
  EXNotifications: c024b19d69d31fbc22ed63682559cef38523da9e
  Expo: 3e53243e3281214a7d613f8a875c0b732d7512c2
  expo-dev-client: ca3ebc270cb0d3f26e6d9c3e1da19957ef4fcac1
  expo-dev-launcher: 11ea4216669a6e466dcb5ef1c23f4ad441605803
  expo-dev-menu: 2c332a184dc36a9a9f23f399cc8d834666495ece
  expo-dev-menu-interface: 00dc42302a72722fdecec3fa048de84a9133bcc4
  ExpoAdapterFBSDKNext: ea93990577019c9cd85df68a0c10683bc08f4f23
  ExpoAsset: 0687fe05f5d051c4a34dd1f9440bd00858413cfe
  ExpoBlur: 567af66164e3043a9a30069594aed1ddf0a88d97
  ExpoCamera: 173e000631122854b87c20310513981e89030bc6
  ExpoContacts: 95526dcc8b50a65e61d569655f4cb05edb713cbb
  ExpoCrypto: 1eaf79360c8135af1f2ebb133394fd3513ca9a3d
  ExpoDevice: cf7419343b6e060209aaadd596defb1b6320597f
  ExpoFileSystem: c8c19bf80d914c83dda3beb8569d7fb603be0970
  ExpoFont: 773955186469acc5108ff569712a2d243857475f
  ExpoHead: 15cd0b1168451650dafe3983b99beea3befb3590
  ExpoImage: c37f79e5e97e0a662ed1c4b17363464007da3148
  ExpoImageManipulator: 43c7bb3ecccbe993054d2e9131c8dcbe54f1385b
  ExpoImagePicker: 482b2a6198b365dd18b5a0cb6d4caeec880cb8e1
  ExpoKeepAwake: 2a5f15dd4964cba8002c9a36676319a3394c85c7
  ExpoLinearGradient: ee9efc5acb988b911320e964fab9b4cbdeb198c4
  ExpoLinking: 0381341519ca7180a3a057d20edb1cf6a908aaf4
  ExpoLocalization: e36b911e04d371c6c6624ef818e56229bf51c498
  ExpoLocation: ad29273f84077363296657176ea39c3545521720
  ExpoMediaLibrary: e76bf16d148184fc32f6302445d5d11e72ab47f5
  ExpoModulesCore: c2eeb11b2fc321dfc21b892be14c124dcac0a1e8
  ExpoSharing: c4540ef2e6615a92e05d2bddf019e1f58f27119e
  ExpoSMS: 2f90c7c780ef65c9f52b800183aab554360b34a2
  ExpoSplashScreen: 1832984021b0795fda9302cf84ac62f0490eeadd
  ExpoSystemUI: fb8213e39d19e0861320fa69eb60cad7a839c080
  ExpoTrackingTransparency: c7cc2db827cee8e6e7230aada8319c31a54fd2df
  ExpoWebBrowser: 6890a769e6c9d83da938dceb9a03e764afc3ec9c
  EXUpdatesInterface: 1dcebac98ac5dad4289e6ff2bd5616822e894397
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBLazyVector: 7605ea4810e0e10ae4815292433c09bf4324ba45
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  FBSDKGamingServicesKit: cdb625419879a651d07906d8f874fc76291be660
  FBSDKLoginKit: 5875762d1fe09ddcb05d03365d4f5dc34413843d
  FBSDKShareKit: 082d1b087d6481af36f8d8433542f25f2fc2c8dd
  fmt: 01b82d4ca6470831d1cc0852a1af644be019e8f6
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  hermes-engine: 9e868dc7be781364296d6ee2f56d0c1a9ef0bb11
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libdav1d: 23581a4d8ec811ff171ed5e2e05cd27bad64c39f
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: 828c890289f983e36cdd756b1e59e7bd46c94637
  Mixpanel-swift: 478ff46d19de4a251244a9c9a582070d4bb94cf9
  MixpanelReactNative: 155c9357e91e64d6b41205529ac8efb92b172947
  PurchasesHybridCommon: 73f4f2c6e4ec3487b4219bf60e1661100fb85da3
  RCT-Folly: 7b4f73a92ad9571b9dbdb05bb30fad927fa971e1
  RCTDeprecation: ebe712bb05077934b16c6bf25228bdec34b64f83
  RCTRequired: ca91e5dd26b64f577b528044c962baf171c6b716
  RCTTypeSafety: e7678bd60850ca5a41df9b8dc7154638cb66871f
  React: 4641770499c39f45d4e7cde1eba30e081f9d8a3d
  React-callinvoker: 4bef67b5c7f3f68db5929ab6a4d44b8a002998ea
  React-Core: 0a06707a0b34982efc4a556aff5dae4b22863455
  React-CoreModules: 907334e94314189c2e5eed4877f3efe7b26d85b0
  React-cxxreact: 3a1d5e8f4faa5e09be26614e9c8bbcae8d11b73d
  React-debug: 817160c07dc8d24d020fbd1eac7b3558ffc08964
  React-defaultsnativemodule: a965cb39fb0a79276ab611793d39f52e59a9a851
  React-domnativemodule: d647f94e503c62c44f54291334b1aa22a30fa08b
  React-Fabric: 64586dc191fc1c170372a638b8e722e4f1d0a09b
  React-FabricComponents: b0ebd032387468ea700574c581b139f57a7497fb
  React-FabricImage: 81f0e0794caf25ad1224fa406d288fbc1986607f
  React-featureflags: f2792b067a351d86fdc7bec23db3b9a2f2c8d26c
  React-featureflagsnativemodule: 95a02d895475de8ace78fedd76143866838bb720
  React-graphics: cbebe910e4a15b65b0bff94a4d3ed278894d6386
  React-hermes: ec18c10f5a69d49fb9b5e17ae95494e9ea13d4d3
  React-idlecallbacksnativemodule: 0c1ae840cc5587197cd926a3cb76828ad059d116
  React-ImageManager: f2a4c01c2ccb2193e60a20c135da74c7ca4d36f2
  React-jserrorhandler: 61d205b5a7cbc57fed3371dd7eed48c97f49fc64
  React-jsi: 95f7676103137861b79b0f319467627bcfa629ee
  React-jsiexecutor: 41e0fe87cda9ea3970ffb872ef10f1ff8dbd1932
  React-jsinspector: 15578208796723e5c6f39069b6e8bf36863ef6e2
  React-jsitracing: 3758cdb155ea7711f0e77952572ea62d90c69f0b
  React-logger: dbca7bdfd4aa5ef69431362bde6b36d49403cb20
  React-Mapbuffer: 6efad4a606c1fae7e4a93385ee096681ef0300dc
  React-microtasksnativemodule: 8732b71aa66045da4bb341ddee1bb539f71e5f38
  react-native-appsflyer: d52a08a5e687e4c2aca78519b0e258e1a82fad0a
  react-native-fbsdk-next: 1baeeacf0ecdeb839cca16ca75cb16a9bbbf8e03
  react-native-google-maps: ee48410bb3f1390abfa20592a44db5d6ada83e4d
  react-native-maps: 2e6e9896195781327ee15b33e3e84bf73c08207a
  react-native-netinfo: f0a9899081c185db1de5bb2fdc1c88c202a059ac
  react-native-safe-area-context: 142fade490cbebbe428640b8cbdb09daf17e8191
  react-native-view-shot: 0f696f0fd65f7294ac5ec336261f228e6afb8442
  react-native-webview: 0a19ebf8e1b6f1a5689bdd771ca158dcc88c5ef4
  React-nativeconfig: 8efdb1ef1e9158c77098a93085438f7e7b463678
  React-NativeModulesApple: 958d4f6c5c2ace4c0f427cf7ef82e28ae6538a22
  React-perflogger: 9b4f13c0afe56bc7b4a0e93ec74b1150421ee22d
  React-performancetimeline: 359db1cb889aa0282fafc5838331b0987c4915a9
  React-RCTActionSheet: aacf2375084dea6e7c221f4a727e579f732ff342
  React-RCTAnimation: d8c82deebebe3aaf7a843affac1b57cb2dc073d4
  React-RCTAppDelegate: 6c0377d9c4058773ea7073bb34bb9ebd6ddf5a84
  React-RCTBlob: 70a58c11a6a3500d1a12f2e51ca4f6c99babcff8
  React-RCTFabric: 7eb6dd2c8fda98cb860a572e3f4e4eb60d62c89e
  React-RCTImage: 5e9d655ba6a790c31e3176016f9b47fd0978fbf0
  React-RCTLinking: 2a48338252805091f7521eaf92687206401bdf2a
  React-RCTNetwork: 0c1282b377257f6b1c81934f72d8a1d0c010e4c3
  React-RCTSettings: f757b679a74e5962be64ea08d7865a7debd67b40
  React-RCTText: e7d20c490b407d3b4a2daa48db4bcd8ec1032af2
  React-RCTVibration: 8228e37144ca3122a91f1de16ba8e0707159cfec
  React-rendererconsistency: b4917053ecbaa91469c67a4319701c9dc0d40be6
  React-rendererdebug: 81becbc8852b38d9b1b68672aa504556481330d5
  React-rncore: 120d21715c9b4ba8f798bffe986cb769b988dd74
  React-RuntimeApple: 52ed0e9e84a7c2607a901149fb13599a3c057655
  React-RuntimeCore: ca6189d2e53d86db826e2673fe8af6571b8be157
  React-runtimeexecutor: 877596f82f5632d073e121cba2d2084b76a76899
  React-RuntimeHermes: 3b752dc5d8a1661c9d1687391d6d96acfa385549
  React-runtimescheduler: 8321bb09175ace2a4f0b3e3834637eb85bf42ebe
  React-timing: 331cbf9f2668c67faddfd2e46bb7f41cbd9320b9
  React-utils: 54df9ada708578c8ad40d92895d6fed03e0e8a9e
  ReactCodegen: 21a52ccddc6479448fc91903a437dd23ddc7366c
  ReactCommon: bfd3600989d79bc3acbe7704161b171a1480b9fd
  RevenueCat: 3d934653b7e8b09af88fd47e9e84cfaf5d0a89ba
  RNCAsyncStorage: 826b603ae9c0f88b5ac4e956801f755109fa4d5c
  RNCPicker: f963e01f78e546a93b98aa201501713dbda14e94
  RNDateTimePicker: 40ffda97d071a98a10fdca4fa97e3977102ccd14
  RNGestureHandler: 783a0ed6c92de677e2346d4250c5de028a1db698
  RNPurchases: 4305de02b902d3c1e56a50295dc0a6e758b73095
  RNReanimated: 3cdb86c3a93fd28e76b497f0ead4019fc502c1c1
  RNScreens: 02c4adf5b4820807807b1d7d4f8bc27eeaed8e11
  RNSentry: 04c096f6931bb1eb46eb5bfb132e9dc67687fad1
  RNSVG: 8b1a777d54096b8c2a0fd38fc9d5a454332bbb4d
  SDWebImage: 8a6b7b160b4d710e2a22b6900e25301075c34cb3
  SDWebImageAVIFCoder: 00310d246aab3232ce77f1d8f0076f8c4b021d90
  SDWebImageSVGCoder: 15a300a97ec1c8ac958f009c02220ac0402e936c
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  Sentry: 1ca8405451040482877dcd344dfa3ef80b646631
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: feb4910aba9742cfedc059e2b2902e22ffe9954a
  ZXingObjC: 8898711ab495761b2dbbdec76d90164a6d7e14c5

PODFILE CHECKSUM: 9bc637988ed16e7a64df5168a2763d5b32406e40

COCOAPODS: 1.15.2
