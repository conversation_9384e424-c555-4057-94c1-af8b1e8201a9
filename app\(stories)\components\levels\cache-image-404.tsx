import { S3_URL } from "@/constants/Links"
import { cacheComponent } from "@/utils/cacheComponent"
import { useEffect, useState } from "react"
import { Text, View } from "react-native"

export default function Story() {
  const [result, setResult] = useState<string | null>(null)

  useEffect(() => {
    ;(async () => {
      const url = `${S3_URL}/non-existing-image.jpg`

      try {
        const fileUri = await cacheComponent(url)
        setResult(fileUri)
      } catch (error: any) {
        setResult(error.message)
      }
    })()
  }, [])

  return (
    <View>
      <Text>
        This is the result of calling the function that downloads an image from
        a URL that returns a 404 error and an HTML page:
      </Text>
      <Text></Text>
      <Text style={{ fontSize: 18 }}>{result}</Text>
      <Text></Text>
      <Text>
        We'd expect it to return the original URL, since we don't want to cache
        it if the response code is not 200. We also expect a console error to be
        logged.
      </Text>
    </View>
  )
}
