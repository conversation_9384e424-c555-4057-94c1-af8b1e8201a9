import React from "react"
import { View, Text, StyleSheet, TouchableOpacity } from "react-native"

const ButtonStory: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Button Components</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Primary Buttons</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={[styles.button, styles.primaryButton]}>
            <Text style={[styles.buttonText, styles.primaryButtonText]}>
              Primary Button
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.primaryButton, styles.disabledButton]}>
            <Text style={[styles.buttonText, styles.primaryButtonText, styles.disabledText]}>
              Disabled
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Secondary Buttons</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={[styles.button, styles.secondaryButton]}>
            <Text style={[styles.buttonText, styles.secondaryButtonText]}>
              Secondary Button
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.outlineButton]}>
            <Text style={[styles.buttonText, styles.outlineButtonText]}>
              Outline Button
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Button Sizes</Text>
        <View style={styles.buttonColumn}>
          <TouchableOpacity style={[styles.button, styles.primaryButton, styles.largeButton]}>
            <Text style={[styles.buttonText, styles.primaryButtonText, styles.largeButtonText]}>
              Large Button
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.primaryButton]}>
            <Text style={[styles.buttonText, styles.primaryButtonText]}>
              Medium Button
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.primaryButton, styles.smallButton]}>
            <Text style={[styles.buttonText, styles.primaryButtonText, styles.smallButtonText]}>
              Small Button
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#fff",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 30,
    textAlign: "center",
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 15,
  },
  buttonRow: {
    flexDirection: "row",
    gap: 15,
    flexWrap: "wrap",
  },
  buttonColumn: {
    gap: 15,
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 120,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  // Primary Button Styles
  primaryButton: {
    backgroundColor: "#007AFF",
  },
  primaryButtonText: {
    color: "#fff",
  },
  // Secondary Button Styles
  secondaryButton: {
    backgroundColor: "#f0f0f0",
  },
  secondaryButtonText: {
    color: "#333",
  },
  // Outline Button Styles
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 2,
    borderColor: "#007AFF",
  },
  outlineButtonText: {
    color: "#007AFF",
  },
  // Disabled Button Styles
  disabledButton: {
    backgroundColor: "#ccc",
  },
  disabledText: {
    color: "#999",
  },
  // Size Variants
  largeButton: {
    paddingHorizontal: 30,
    paddingVertical: 16,
    minWidth: 200,
  },
  largeButtonText: {
    fontSize: 18,
  },
  smallButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    minWidth: 80,
  },
  smallButtonText: {
    fontSize: 14,
  },
})

export default ButtonStory
