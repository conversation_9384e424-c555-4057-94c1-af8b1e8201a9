import {
  ActiveConnectionMode,
  useActiveConnectionMode,
} from "@/context/ModeContext"
import React from "react"
import { View, TouchableOpacity, Text, StyleSheet } from "react-native"
import { ConnectionMode } from "./signInOrUp/ConnectionModeStep"
import { VERY_LIGHT_GREY } from "@/constants/Colors"

const modeOptions: { value: ActiveConnectionMode; label: string }[] = [
  {
    value: ConnectionMode.Dates,
    label: "Dating",
  },
  {
    value: ConnectionMode.Friends,
    label: "Friends",
  },
]

interface ToggleProps {
  // NOTE: DO NOT ADD ANY PROPS to this component without checking all usages of it.
  // It is redeclared dynamically using navigation.setOptions in chat screens.
}

const Toggle = ({}: ToggleProps) => {
  const { activeConnectionMode, setActiveConnectionMode } =
    useActiveConnectionMode()

  return (
    <View style={styles.container}>
      {modeOptions.map((mode) => (
        <TouchableOpacity
          key={mode.value}
          style={[
            styles.optionContainer,
            activeConnectionMode === mode.value ? styles.selectedOption : null,
          ]}
          onPress={() => setActiveConnectionMode(mode.value)}
          activeOpacity={0.8}
        >
          <Text
            style={[
              styles.text,
              activeConnectionMode === mode.value ? styles.selectedText : null,
            ]}
          >
            {mode.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 160,
    height: 31,
    marginRight: 24,
    backgroundColor: VERY_LIGHT_GREY,
    borderRadius: 10,
    paddingVertical: 5,
    paddingHorizontal: 2.5,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  optionContainer: {
    flex: 1,
    height: 21,
    borderRadius: 6,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedOption: {
    backgroundColor: "black",
  },
  text: {
    fontFamily: "InterTight-Regular",
    fontWeight: "500",
    fontSize: 12,
    textAlign: "center",
    textAlignVertical: "center",
  },
  selectedText: {
    color: "white",
  },
})

export default Toggle
