import { Step } from "@/components/ratings/ArticleSurvey"
import { RatingDrawer } from "@/components/ratings/RatingDrawer"
import _ from "lodash"
import { POINT_EVENTS } from "./level-3"
import { PointsCompletionStep } from "@/components/ratings/PointsCompletionStep"
import { LEADERBOARD_USERS } from "./leaderboard"
import { useSession } from "@/ctx"
import { Session } from "@/types/user"
import { USER } from "../../(account)/account"
import { useEffect } from "react"

export default function Story() {
  const testSession: Session = {
    user: { ...USER, id: LEADERBOARD_USERS[1].id },
    token: "test-token",
    version: process.env.EXPO_PUBLIC_SESSION_VERSION!,
  }

  const { overwriteSession } = useSession()
  useEffect(() => {
    overwriteSession(testSession)
  }, [])

  const step: Step = {
    children: (
      <PointsCompletionStep
        initialPoints={110}
        pointEvents={[POINT_EVENTS[0]]}
        soundAssets={[]}
        soundAndHapticsOn={false}
        leaderboardUsers={LEADERBOARD_USERS}
      />
    ),
  }

  return (
    <RatingDrawer step={step} stepIndex={3} totalSteps={4} onClose={_.noop} />
  )
}
