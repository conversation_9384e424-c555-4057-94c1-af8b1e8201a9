import {
  getSectionByName,
  Section,
  SectionGroup,
} from "@/components/news/constants"
import { Article } from "@/types/news"
import {
  rebuildArticles,
  sortArticlesStartingWithSection,
} from "@/utils/processArticles"
import _ from "lodash"

let sectionGroups: Partial<SectionGroup>[]
let articles: Partial<Article>[]
let startSection: Partial<Section>

describe("Processing articles", () => {
  beforeEach(() => {
    sectionGroups = [
      { sections: [getSectionByName("health")] },
      { sections: [getSectionByName("culture")] },
      { sections: [getSectionByName("technology")] },
      { sections: [getSectionByName("worldNews")] },
    ]

    articles = [
      { id: 1, frontpageSection: "health", position: 1 },
      { id: 2, frontpageSection: "culture", position: 1 },
      { id: 3, frontpageSection: "technology", position: 1 },
      { id: 4, frontpageSection: "worldNews", position: 1 },
    ]

    startSection = {
      name: "technology",
    }
  })

  it("re-orders articles based on active section", () => {
    const processedArticles = sortArticlesStartingWithSection({
      articles: articles as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
    })

    expect(processedArticles.map((a) => a.frontpageSection)).toEqual([
      "technology",
      "worldNews",
      "health",
      "culture",
    ])
  })

  it("re-orders articles based on position", () => {
    const articles = [
      { id: 1, frontpageSection: "health", position: 2 },
      { id: 2, frontpageSection: "health", position: 1 },
      { id: 3, frontpageSection: "technology", position: 1 },
      { id: 4, frontpageSection: "worldNews", position: 0 },
    ]

    const sortedArticles = sortArticlesStartingWithSection({
      articles: articles as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
    })

    expect(sortedArticles.map((a) => a.id)).toEqual([3, 4, 2, 1])
  })

  it("re-orders articles with null position", () => {
    const articles = [
      { id: 1, frontpageSection: "health", position: 1 },
      { id: 3, frontpageSection: "culture", position: null },
      { id: 2, frontpageSection: "culture", position: null },
      { id: 4, frontpageSection: "technology", position: 1 },
      { id: 5, frontpageSection: "worldNews", position: 0 },
    ]

    const sortedArticles = sortArticlesStartingWithSection({
      articles: articles as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
    })
    expect(sortedArticles.map((a) => a.id)).toEqual([4, 5, 1, 2, 3])
  })

  it("sorts by section before position", () => {
    const articles = [
      { id: 1, frontpageSection: "health", position: 2 },
      { id: 2, frontpageSection: "culture", position: 1 },
      { id: 3, frontpageSection: "technology", position: 1 },
      { id: 4, frontpageSection: "worldNews", position: 0 },
    ]

    const sortedArticles = sortArticlesStartingWithSection({
      articles: articles as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
    })
    expect(sortedArticles.map((a) => a.id)).toEqual([3, 4, 1, 2])
  })

  it("processes articles", () => {
    const processedArticles = rebuildArticles({
      articles: articles as Article[],
      startSection: startSection as Section,
      shuffleModeOn: false,
      sectionGroups: sectionGroups as SectionGroup[],
    })

    expect(processedArticles.length).toBe(4)
    expect(processedArticles.map((a) => a.id)).toEqual([3, 4, 1, 2])
  })

  it("processes articles with one swiped article", () => {
    const processedArticles = rebuildArticles({
      articles: _.set(articles, "[0].gistRating", "like") as Article[],
      startSection: startSection as Section,
      shuffleModeOn: false,
      sectionGroups: sectionGroups as SectionGroup[],
    })

    expect(processedArticles.length).toBe(4)
    expect(processedArticles.map((a) => a.id)).toEqual([3, 4, 1, 2])
  })

  it("shuffles articles when shuffleModeOn is true", () => {
    const moreArticles = _.times(100, (i) => ({
      ...articles[i % articles.length],
      id: i + 1,
    })) as Article[]

    console.log("moreArticles", moreArticles)

    const processedArticles = rebuildArticles({
      articles: moreArticles,
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
      shuffleModeOn: true,
    })

    expect(processedArticles.length).toBe(100)
    expect(processedArticles[0].id).toBe(articles[0].id)
    expect(processedArticles.map((a) => a.id)).not.toEqual(
      moreArticles.map((a) => a.id),
    )
  })

  it("does not crash when shuffling with no articles", () => {
    const processedArticles = rebuildArticles({
      articles: [],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
      shuffleModeOn: true,
    })

    expect(processedArticles.length).toBe(0)
  })

  it("shuffles when first article is gist rated", () => {
    const processedArticles = rebuildArticles({
      articles: _.set(articles, "[0].gistRating", "like") as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
      shuffleModeOn: true,
    })

    expect(processedArticles.length).toBe(4)
    expect(processedArticles[0].id).toBe(articles[1].id)
  })

  it("shuffles when first article is surveyed", () => {
    const processedArticles = rebuildArticles({
      articles: _.set(articles, "[0].isSurveyed", true) as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
      shuffleModeOn: true,
    })

    expect(processedArticles.length).toBe(4)
    expect(processedArticles[0].id).toBe(articles[1].id)
  })

  it("returns all articles when shuffling if all rated", () => {
    const processedArticles = rebuildArticles({
      articles: articles.map((a) => ({
        ...a,
        gistRating: "like",
      })) as Article[],
      startSection: startSection as Section,
      sectionGroups: sectionGroups as SectionGroup[],
      shuffleModeOn: true,
    })

    expect(processedArticles.length).toBe(articles.length)
  })
})
