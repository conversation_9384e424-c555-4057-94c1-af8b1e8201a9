import StatsBar from "@/components/news/statsBar/StatsBar"
import { Screen } from "@/components/Themed"
import { useLevels } from "@/context/LevelContext"
import { useEffect } from "react"

export default function Story() {
  const { setStats } = useLevels()
  useEffect(() => {
    setStats({
      points: 0,
      streakDays: 0,
      articlesRead: 0,
      articlesRated: 0,
    })
  }, [setStats])

  return (
    <Screen style={{ justifyContent: "center" }}>
      <StatsBar />
    </Screen>
  )
}
