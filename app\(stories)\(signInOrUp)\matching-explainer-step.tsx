import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { useHideHeader } from "../story_utils"
import { router } from "expo-router"
import {
  MatchingExplainerStep,
  TITLE,
} from "@/components/signInOrUp/MatchingExplainerStep"

export default function Story() {
  useHideHeader()

  return (
    <SignUpWrapper
      title={TITLE}
      progress={0.95}
      onBack={() => router.back()}
      onNext={() => {}}
    >
      <MatchingExplainerStep />
    </SignUpWrapper>
  )
}
