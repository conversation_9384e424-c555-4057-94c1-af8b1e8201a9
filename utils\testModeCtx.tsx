import { createContext, useContext, useState } from "react"

export type TestModeProps = {
  toggleTestMode: () => void
  deactivateTestMode: () => void
  testMode: boolean
}

const TestModeContext = createContext<TestModeProps>({
  toggleTestMode: () => {},
  deactivateTestMode: () => {},
  testMode: false,
})

export const useTestMode = () => {
  const contextValue = useContext(TestModeContext)
  return contextValue
}

export const TestModeProvider = (props: React.PropsWithChildren) => {
  const [testMode, setTestMode] = useState(false)

  const toggleTestMode = () => setTestMode((prev) => !prev)
  const deactivateTestMode = () => setTestMode(false)

  return (
    <TestModeContext.Provider
      value={{ testMode, toggleTestMode, deactivateTestMode }}
    >
      {props.children}
    </TestModeContext.Provider>
  )
}
