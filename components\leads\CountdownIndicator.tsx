import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>NSTONE, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_GREEN } from "@/constants/Colors"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { StyleSheet, View, Text } from "react-native"
import { AnimatedCircularProgress } from "react-native-circular-progress"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"
import { MAX_LEADS } from "./constants"

type CountdownIndicatorProps = {
  countRemaining: number
  connectionMode: ActiveConnectionMode
}

export const CountdownIndicator = ({
  countRemaining,
  connectionMode,
}: CountdownIndicatorProps) => {
  const countdownText = () => {
    return (
      <View style={styles.textContainer}>
        <Text style={styles.text}>{countRemaining}</Text>
      </View>
    )
  }

  const color =
    connectionMode === ConnectionMode.Dates ? BROWNSTONE : LIME_GREEN

  return (
    <View style={styles.progressBarStyle}>
      <AnimatedCircularProgress
        size={43}
        fill={(countRemaining / MAX_LEADS) * 100}
        width={3.5}
        tintColor={color}
        backgroundColor={GREY}
        rotation={0}
        lineCap="round"
        prefill={4}
        children={countdownText}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  progressBarStyle: {
    position: "absolute",
    zIndex: 300,
    right: 35,
    top: 30,
  },
  text: {
    color: "black",
    fontWeight: "500",
    fontSize: 20,
  },
  textContainer: {
    backgroundColor: BEIGE,
    opacity: 0.56,
    width: "90%",
    height: "90%",
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
  },
})
