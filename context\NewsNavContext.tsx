import React, { createContext, useContext, useState } from "react"

export type NewsTabName = "gists" | "newsfeed"

export interface NewsNavContextProps {
  activeTab: NewsTabName
  setActiveTab: React.Dispatch<React.SetStateAction<NewsTabName>>
}

const NewsNavContext = createContext<NewsNavContextProps | undefined>(undefined)

export type NewsNavProviderProps = {
  children: React.ReactNode
}

export const NewsNavProvider: React.FC<NewsNavProviderProps> = ({
  children,
}) => {
  const [activeTab, setActiveTab] = useState<NewsTabName>("gists")
  return (
    <NewsNavContext.Provider value={{ activeTab, setActiveTab }}>
      {children}
    </NewsNavContext.Provider>
  )
}

export const useNewsNavContext = () => {
  const context = useContext(NewsNavContext)
  if (!context) {
    throw new Error("Context must be used within a provider")
  }
  return context
}
