import { GistCard } from "@/components/gists/GistCard"
import { Screen } from "@/components/Themed"
import { articles } from "./(news)/feed/news"

export default function GistCardVideo() {
  return (
    <Screen>
      <GistCard
        article={articles[0]}
        isVisible={true}
        demoMode={true}
        onImageLoaded={() => console.log("Image loaded")}
        onTimerCompleted={() => console.log("Timer completed")}
      />
    </Screen>
  )
}
