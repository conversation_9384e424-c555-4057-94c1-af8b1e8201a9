import {GistCard} from "../../components/gists/GistCard"
import { Article } from "@/types/news"
import {rawArticles} from "./(news)/feed/news"



export default function GistCardVideo() {

    const articles: Article = rawArticles.map(convertRawArticle)

    return (
        <GistCard
            article={articles}
            isVisible={true}
            onTimerCompleted={() => {}}
        />
    )

}