import { GistCard } from "@/components/gists/GistCard"
import { Screen } from "@/components/Themed"
import { articles } from "./(news)/feed/news"
import { Article } from "@/types/news"
import { StoryNewsProvider } from "./story-components/StoryNewsProvider"
import { StoryLevelsProvider } from "./story-components/StoryLevelsProvider"

export default function GistCardVideo() {
  const testArticle: Article = {
    ...articles[0],
    localImage: require("@/assets/images/onboarding/salad-article.jpg"),
    summaryPoints: [
      "This is a sample bullet point that summarizes the gist of the article.",
      "Another bullet point for the gist, which is a summary of the article.",
      "Yet another point to summarize the gist of the article, providing insights.",
    ],
    videoURL: "https://www.filmsupply.com/clips/two-boxers-fighting-in-a-ring-while-using-protective-head-gear/712424",
  }

  return (
    <StoryLevelsProvider>
      <StoryNewsProvider>
        <Screen>
          <GistCard
            article={testArticle}
            isVisible={true}
            demoMode={true}
            onImageLoaded={() => console.log("Image loaded")}
            onTimerCompleted={() => console.log("Timer completed")}
          />
        </Screen>
      </StoryNewsProvider>
    </StoryLevelsProvider>
  )
}
