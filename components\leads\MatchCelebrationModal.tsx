import React, { useEffect } from "react"
import { View, Text, StyleSheet, Platform } from "react-native"
import { Button } from "../Button"
import { Modal as CustomModal } from "../Modal"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"
import { datesModePalette, friendsModePalette } from "@/constants/Colors"
import Ionicons from "@expo/vector-icons/Ionicons"
import Animated, {
  FadeInDown,
  FadeInLeft,
  useAnimatedStyle,
  useSharedValue,
  LinearTransition,
  FadeIn,
  withTiming,
  Easing,
  withSequence,
  ReduceMotion,
} from "react-native-reanimated"
import { Image } from "expo-image"
import { Window } from "@/utils/location"
import { BlurView } from "expo-blur"
import { User } from "@/types/user"
import { fontStyles } from "@/styles"

interface MatchCelebrationModalProps {
  visible: boolean
  matchProfileImageUrl: string
  currentUser: User
  connectionMode: ActiveConnectionMode
  onGoToMatches: () => void
  onClose: () => void
}

const PROFILE_IMAGE_SIZE = 140
const BORDER_WIDTH = 10
const MODAL_START_POINT = Window.height * 0.4
const BIG_TEXT_START_SCALE = 0.5
const BIG_TEXT_FINAL_SCALE = 0.9

const MODAL_INCOMING_TRANSITION_DURATION = 400
const MODAL_VISIBLE_DURATION = 400
const MODAL_ANIMATION_DELAY = MODAL_VISIBLE_DURATION - 50
const BUTTON_CONTAINER_DURATION = 700
const BUTTON_CONTAINER_DELAY = MODAL_ANIMATION_DELAY + 50
const SUB_TEXT_DELAY = BUTTON_CONTAINER_DELAY + 50
const SUB_TEXT_DURATION = 450
const BIG_TEXT_DELAY = SUB_TEXT_DELAY + 50
const BIG_TEXT_DURATION = BUTTON_CONTAINER_DURATION + 200
const PROFILE_DELAY = BIG_TEXT_DELAY + 50
const PROFILE_PHOTO_DURATION = 1500
const PROFILE_PHOTO_OPACITY_DURATION = 500

const MatchCelebrationModal = ({
  visible,
  matchProfileImageUrl,
  currentUser,
  connectionMode,
  onGoToMatches,
  onClose,
}: MatchCelebrationModalProps) => {
  const isDatesMode = connectionMode === ConnectionMode.Dates

  const backgroundColor = isDatesMode
    ? datesModePalette.backgroundColor
    : friendsModePalette.backgroundColor

  const scale = useSharedValue(BIG_TEXT_START_SCALE)
  const translateY = useSharedValue(MODAL_START_POINT)

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    }
  })

  const containerPosition = useAnimatedStyle(() => ({
    opacity: withTiming(translateY.value ? 0 : 1, {
      duration: MODAL_VISIBLE_DURATION,
    }),
    transform: [
      {
        translateY: withTiming(translateY.value, {
          duration: MODAL_INCOMING_TRANSITION_DURATION,
        }),
      },
    ],
  }))

  const convertHexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16)
    const g = parseInt(hex.slice(3, 5), 16)
    const b = parseInt(hex.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }

  useEffect(() => {
    if (visible) {
      translateY.value = 0
      setTimeout(() => {
        scale.value = withSequence(
          withTiming(BIG_TEXT_FINAL_SCALE, {
            duration: BIG_TEXT_DURATION,
            easing: Easing.out(Easing.back(4)),
            reduceMotion: ReduceMotion.Never,
          }),
        )
      }, BIG_TEXT_DELAY)
    } else {
      translateY.value = MODAL_START_POINT
      scale.value = BIG_TEXT_START_SCALE
    }
  }, [visible])

  const profileAnimation = (position: "left" | "right") => {
    return FadeInLeft.delay(MODAL_ANIMATION_DELAY)
      .withInitialValues({
        transform: [
          {
            translateX:
              PROFILE_IMAGE_SIZE *
              (position?.toLowerCase() === "left" ? -1 : 1),
          },
        ],
      })
      .easing(Easing.out(Easing.exp))
      .duration(PROFILE_PHOTO_DURATION)
  }

  const profileOpacityAnimation = FadeIn.delay(PROFILE_DELAY).duration(
    PROFILE_PHOTO_OPACITY_DURATION,
  )

  const renderProfileComponent = (type: "left" | "right") => {
    const commonTranslate = -PROFILE_IMAGE_SIZE / 2
    return (
      <Animated.View
        style={{
          top: "50%",
          transform: [
            { translateX: commonTranslate },
            { translateY: commonTranslate },
            {
              translateX:
                type === "left"
                  ? -PROFILE_IMAGE_SIZE / 2.5
                  : PROFILE_IMAGE_SIZE / 2.5,
            },
          ],
          position: "absolute",
        }}
        entering={profileOpacityAnimation}
      >
        <Animated.View
          entering={profileAnimation(type)}
          style={styles.profileImageContainer}
        >
          <Image
            source={
              type === "left" ? matchProfileImageUrl : currentUser.images[0].url
            }
            style={styles.profileImage}
          />
        </Animated.View>
      </Animated.View>
    )
  }

  const transparency = Platform.OS === "ios" ? 0.8 : 0.95

  return (
    <CustomModal
      visible={visible}
      containerStyle={[
        styles.fullScale,
        styles.transparent,
        styles.modalContainerStyle,
      ]}
      modalProps={{
        animationType: "none",
        transparent: true,
      }}
      overlayStyle={styles.transparent}
    >
      <Animated.View
        style={[
          styles.fullScale,
          styles.innerContainer,
          containerPosition,
          {
            backgroundColor: convertHexToRgba(backgroundColor, transparency),
            flexDirection: "row",
          },
        ]}
      >
        <BlurView
          intensity={50}
          style={[StyleSheet.absoluteFill, styles.blurViewStyle]}
        />
        <View style={{ flex: 1, justifyContent: "center" }}>
          <Animated.View style={styles.profileContainer}>
            {renderProfileComponent("left")}
            {renderProfileComponent("right")}
          </Animated.View>
          <Animated.View
            layout={LinearTransition.springify().delay(BIG_TEXT_DELAY)}
            style={[{ marginBottom: 12 }, animatedStyle]}
            entering={FadeIn.delay(BIG_TEXT_DELAY)}
          >
            <Text style={styles.header}>
              {isDatesMode ? `Big News:` : `This just in...`}
            </Text>
            <Text style={styles.header}>You're Matched!</Text>
          </Animated.View>
          <Animated.View
            entering={FadeInDown.delay(SUB_TEXT_DELAY).duration(
              SUB_TEXT_DURATION,
            )}
            style={styles.bodyContainer}
          >
            <Text style={styles.body}>You both swiped right!</Text>
          </Animated.View>
          <Animated.View
            entering={FadeInDown.delay(BUTTON_CONTAINER_DELAY).duration(
              BUTTON_CONTAINER_DURATION,
            )}
            style={styles.bodyContainer}
          >
            <Button
              text="Send message"
              isTextOnLeft={true}
              style={{ marginTop: 32 }}
              iconComponent={
                <Ionicons name="chatbubble" size={24} color="white" />
              }
              onPress={onGoToMatches}
            />
            <Button
              text="Back to Leads"
              style={styles.outlinedButton}
              textStyle={{ color: "black" }}
              onPress={onClose}
            />
          </Animated.View>
        </View>
      </Animated.View>
    </CustomModal>
  )
}

const styles = StyleSheet.create({
  modalContainerStyle: {
    padding: 0,
  },
  innerContainer: {
    flex: 1,
    padding: 20,
  },
  blurViewStyle: {
    zIndex: -1,
  },
  fullScale: {
    height: "100%",
    width: "100%",
  },
  transparent: {
    backgroundColor: "transparent",
  },
  profileContainer: {
    position: "relative",
    height: PROFILE_IMAGE_SIZE,
    marginBottom: 28,
    alignSelf: "center",
  },
  profileImageContainer: {
    width: PROFILE_IMAGE_SIZE,
    height: PROFILE_IMAGE_SIZE,
    borderRadius: PROFILE_IMAGE_SIZE,
    backgroundColor: "white",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  profileImage: {
    width: PROFILE_IMAGE_SIZE - BORDER_WIDTH,
    height: PROFILE_IMAGE_SIZE - BORDER_WIDTH,
    borderRadius: PROFILE_IMAGE_SIZE,
  },
  header: {
    fontSize: 50,
    lineHeight: 66,
    textAlign: "center",
    ...fontStyles.editorial,
  },
  bodyContainer: {
    alignItems: "center",
  },
  body: {
    fontSize: 18,
  },
  outlinedButton: {
    marginTop: 8,
    backgroundColor: "transparent",
    borderWidth: 1.5,
    color: "black",
  },
})

export default MatchCelebrationModal
