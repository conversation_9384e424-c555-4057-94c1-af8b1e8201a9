import Svg, { Path, SvgProps } from "react-native-svg"
export const ThinkerIcon = (props: SvgProps) => (
  <Svg width={20} height={20} viewBox="0 0 24 31" fill="none">
    <Path
      d="M5.72265 23.2306C5.2914 21.3822 3.34572 19.8707 2.54711 18.8746C1.1796 17.166 0.322566 15.1059 0.0747323 12.9315C-0.173101 10.7571 0.19835 8.55697 1.1463 6.58447C2.09424 4.61197 3.58012 2.94738 5.43277 1.78245C7.28541 0.617526 9.42945 -0.000350285 11.6179 1.48981e-07C13.8064 0.000350583 15.9502 0.618914 17.8025 1.78444C19.6548 2.94996 21.1401 4.61502 22.0874 6.58782C23.0347 8.56062 23.4055 10.7609 23.1569 12.9352C22.9084 15.1095 22.0507 17.1694 20.6827 18.8775C19.8841 19.8721 17.9413 21.3837 17.51 23.2306H5.72265ZM17.4244 26.1346V27.5866C17.4244 28.3568 17.1184 29.0955 16.5738 29.6401C16.0292 30.1847 15.2905 30.4907 14.5204 30.4907H8.71233C7.94214 30.4907 7.20349 30.1847 6.65888 29.6401C6.11428 29.0955 5.80832 28.3568 5.80832 27.5866V26.1346H17.4244ZM13.0683 11.6218V5.80657L6.53432 14.5259H10.1643V20.3339L16.6984 11.6218H13.0683Z"
      fill={props.fill || "black"}
    />
  </Svg>
)
