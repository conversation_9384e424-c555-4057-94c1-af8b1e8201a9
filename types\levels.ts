export interface Level {
  id: number
  name: string
  place: number
  color: string
  pointsRequired: number
  description: string
  levelUpTitle: string
  levelUpSubtitle: string
  badgeUrl: string
  grayscaleBadgeUrl: string
  iconUrl: string
  shareableUrl: string
}

export interface RawLevel extends Omit<Level, "pointsRequired"> {
  points_required: number
  level_up_title: string
  level_up_subtitle: string
  icon_url: string
  badge_url: string
  grayscale_badge_url: string
  shareable_url: string
}

export const convertRawLevel = (rawLevel: RawLevel): Level => {
  return {
    ...rawLevel,
    pointsRequired: rawLevel.points_required,
    levelUpTitle: rawLevel.level_up_title,
    levelUpSubtitle: rawLevel.level_up_subtitle,
    iconUrl: rawLevel.icon_url,
    badgeUrl: rawLevel.badge_url,
    grayscaleBadgeUrl: rawLevel.grayscale_badge_url,
    shareableUrl: rawLevel.shareable_url,
  }
}

export type StreakType =
  | "2_day"
  | "3_day"
  | "7_day"
  | "14_day"
  | "21_day"
  | "4_week"
  | "90_day"
  | "180_day"
  | "365_day"

export type PointEvent = {
  label: string
  points: number
  type: "article_rated" | "rating_bonus" | "streak_bonus"
  subtype: StreakType | null
}

export type RawStreak = {
  type: StreakType
  shareable_url: string
}

export type Streak = {
  type: StreakType
  title: string
  subtitle: string
  shareableUrl: string
}

export type LevelStats = {
  points: number
  streakDays: number
  articlesRead: number
  articlesRated: number
}

export type RawLevelStats = {
  points: LevelStats["points"]
  streak_days: LevelStats["streakDays"]
  articles_read: LevelStats["articlesRead"]
  articles_rated: LevelStats["articlesRated"]
}

export const convertRawLevelStats = (raw: RawLevelStats): LevelStats => {
  return {
    points: raw.points,
    streakDays: raw.streak_days,
    articlesRead: raw.articles_read,
    articlesRated: raw.articles_rated,
  }
}
