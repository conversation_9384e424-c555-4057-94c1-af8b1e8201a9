import Svg, { Path, SvgProps } from "react-native-svg"
export const NewbieIcon = (props: SvgProps) => (
  <Svg width={20} height={20} viewBox="0 0 29 24" fill="none">
    <Path
      d="M23.8263 14.3716C22.6688 15.0752 21.3393 15.4452 19.9847 15.4409C18.8499 15.4319 17.7279 15.1998 16.6827 14.7578C15.8771 15.895 15.4459 17.2551 15.4492 18.6487V22.2078C15.4495 22.3402 15.4226 22.4712 15.3701 22.5927C15.3176 22.7142 15.2407 22.8236 15.1442 22.9142C15.0477 23.0047 14.9335 23.0745 14.8089 23.119C14.6843 23.1636 14.5518 23.1821 14.4197 23.1733C14.1716 23.1517 13.9407 23.0371 13.7735 22.8524C13.6063 22.6678 13.5151 22.4267 13.5182 22.1777V20.6763L8.85717 16.0154C8.16429 16.2738 7.43151 16.409 6.692 16.4148C5.67394 16.4175 4.67492 16.1389 3.80512 15.6098C1.1753 14.0119 -0.240391 10.3346 0.0335739 5.76899C0.047368 5.53278 0.147416 5.30987 0.314724 5.14256C0.482032 4.97526 0.704951 4.87521 0.941158 4.86141C5.50684 4.59228 9.18424 6.00312 10.7773 8.6329C11.4033 9.66367 11.6765 10.8702 11.5558 12.0701C11.5483 12.163 11.514 12.2518 11.4571 12.3257C11.4002 12.3996 11.3231 12.4555 11.2352 12.4865C11.1472 12.5175 11.0521 12.5224 10.9615 12.5005C10.8708 12.4787 10.7884 12.431 10.7242 12.3633L8.407 9.93753C8.22446 9.76411 7.98139 9.66885 7.72962 9.67208C7.47785 9.6753 7.2373 9.77675 7.05926 9.95479C6.88122 10.1328 6.77977 10.3734 6.77655 10.6251C6.77332 10.8769 6.86858 11.12 7.042 11.3025L13.5447 17.9705C13.552 17.8764 13.5604 17.7822 13.5701 17.6893C13.7812 15.8992 14.5711 14.227 15.8197 12.927L21.9254 6.47501C22.1066 6.294 22.2084 6.04844 22.2085 5.79234C22.2087 5.53625 22.107 5.29059 21.926 5.10943C21.745 4.92826 21.4994 4.82642 21.2433 4.82631C20.9872 4.82619 20.7416 4.92782 20.5604 5.10882L14.6466 11.3628C14.5874 11.4255 14.5125 11.4712 14.4297 11.4952C14.3469 11.5191 14.2591 11.5205 14.1756 11.499C14.0921 11.4776 14.0158 11.4342 13.9548 11.3733C13.8937 11.3124 13.8501 11.2363 13.8284 11.1529C13.2563 9.04323 13.5085 6.94327 14.6008 5.1402C16.7563 1.58234 21.7721 -0.322111 28.019 0.0447789C28.2552 0.0585728 28.4781 0.15862 28.6455 0.325925C28.8128 0.49323 28.9128 0.716146 28.9266 0.952349C29.2887 7.20034 27.3842 12.2161 23.8263 14.3716Z"
      fill={props.fill || "black"}
    />
  </Svg>
)
