import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { Leaderboard, LeaderboardProps } from "./Leaderboard"
import { CelebrateStep } from "./CelebrateStep"
import { useSession } from "@/ctx"
import { View } from "react-native"

interface LeaderboardStepProps extends Pick<LeaderboardProps, "users"> {}

export const LeaderboardStep = (props: LeaderboardStepProps) => {
  const { session } = useSession()
  return (
    <CelebrateStep
      title="Go you!"
      subtitle="Here's how things are stacking up with your fellow knowledge seekers."
    >
      <View style={{ width: wp(80), marginBottom: 30 }}>
        <Leaderboard {...props} currentUserId={session!.user.id} />
      </View>
    </CelebrateStep>
  )
}
