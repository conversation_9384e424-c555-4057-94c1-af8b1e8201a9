import { StyleSheet, View } from "react-native"
import { use<PERSON><PERSON>back, useMemo, useState } from "react"
import { useSession } from "@/ctx"
import { getReferralCode } from "@/apiQueries/apiQueries"
import { useFocusEffect } from "expo-router"
import { useAppState } from "@/app/hooks/useAppState"
import { NewsFeedAlert } from "../../components/news/AlertCard"
import {
  getClosedAlerts,
  newUpdateAlert,
  setAlertAsClosed,
} from "../../components/news/alerts"
import {
  ArticleMatchPreview,
  getAnnouncements,
  getMatchMoods,
  Announcement,
} from "@/apiQueries/newsFeed"
import { getUserSettings } from "@/apiQueries/userSettings"
import { useFeatureFlag } from "@/utils/featureFlags"
import { checkVersionStatus } from "@/apiQueries/checkVersionStatus"
import { Session } from "@/types/user"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import SegmentTab from "../../components/news/SegmentTab"
import { NewsFeed } from "@/components/news/NewsFeed"
import { GistsPage } from "./GistsPage"
import { trackEvent } from "@/utils/tracking"
import { NewsEventType } from "@/types/news"
import { Loader } from "@/components/widgets/Loader"
import { NewsTabName, useNewsContext } from "@/context/NewsContext"
import { Feature, useTestingContext } from "@/context/TestingContext"

export const NewsPage = () => {
  const matchMoodsFlagEnabled = useFeatureFlag("match_moods")

  const { loadArticles, isLoading } = useNewsContext()

  const [alerts, setAlerts] = useState<NewsFeedAlert[]>([])
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [matchPreviews, setMatchPreviews] = useState<ArticleMatchPreview[]>([])
  const [optedOutOfMatchMoods, setOptedOutOfMatchMoods] = useState(true)

  const [referralCode, setReferralCode] = useState<string | null>(null)
  const { session, refreshSession } = useSession()

  const hideMatchMoods = useMemo(
    () => optedOutOfMatchMoods || !matchMoodsFlagEnabled,
    [optedOutOfMatchMoods, matchMoodsFlagEnabled],
  )

  const assembleAlerts = async () => {
    let newAlerts: NewsFeedAlert[] = []

    try {
      const status = await checkVersionStatus()
      if (status === "update_available") {
        newAlerts.push(newUpdateAlert)
      }
    } catch (e) {
      console.error("Failed to check for new app version", e)
    }

    const closedAlertTypes = await getClosedAlerts()
    newAlerts = newAlerts.filter(
      (alert) => !closedAlertTypes.includes(alert.type),
    )

    setAlerts(newAlerts)
  }

  const handleCloseAlert = async (alert: NewsFeedAlert) => {
    await setAlertAsClosed(alert)
    assembleAlerts()
  }

  const fetchAnnouncements = async () => {
    const announcements = await getAnnouncements({ token: session!.token })
    setAnnouncements(announcements)
  }

  const fetchReferralCode = async () =>
    getReferralCode(session!.token).then(setReferralCode)

  const fetchMatchPreviews = async () => {
    const { matchMoodsOptOut } = await getUserSettings({
      token: session!.token,
    })

    setOptedOutOfMatchMoods(matchMoodsOptOut)

    if (matchMoodsOptOut || !matchMoodsFlagEnabled) {
      setMatchPreviews([])
    } else {
      const matchPreviews = await getMatchMoods({ token: session!.token })
      setMatchPreviews(matchPreviews)
    }
  }

  const refreshData = () => {
    assembleAlerts()
    fetchAnnouncements()
    loadArticles()
    fetchReferralCode()
    fetchMatchPreviews()
  }

  const refreshSessionAndData = async () => {
    if (session) await refreshSession(session!.token)
    refreshData()
  }

  useFocusEffect(
    useCallback(() => {
      refreshSessionAndData()
    }, [matchMoodsFlagEnabled]),
  )

  useAppState({ onForeground: refreshSessionAndData })

  if (isLoading) return <Loader />

  return (
    <NewsPage_
      alerts={alerts}
      announcements={announcements}
      matchPreviews={matchPreviews}
      hideMatchMoods={hideMatchMoods}
      referralCode={referralCode}
      session={session!}
      onCloseAlert={handleCloseAlert}
    />
  )
}

type NewsPageProps = {
  alerts: NewsFeedAlert[]
  announcements: Announcement[]
  matchPreviews: ArticleMatchPreview[]
  hideMatchMoods: boolean
  referralCode: string | null
  session: Session
  onCloseAlert: (alert: NewsFeedAlert) => void
}

export const NewsPage_ = (props: NewsPageProps) => {
  const { featureIsOn } = useTestingContext()
  const gistsFlag = featureIsOn(Feature.Gists)
  const { activeTab, setActiveTab } = useNewsContext()

  const { top } = useSafeAreaInsets()

  const tabs: { name: NewsTabName; title: string }[] = [
    { name: "gists", title: "Gists" },
    { name: "newsfeed", title: "Feed" },
  ]

  const handleTabChange = (index: number) => {
    trackEvent(NewsEventType.NewsTabChanged, { data: { tab: tabs[index] } })
    setActiveTab(tabs[index].name)
  }

  const renderNewsFeed = () => <NewsFeed {...props} />

  if (gistsFlag) {
    return (
      <View style={[styles.container, { paddingTop: top }]}>
        <SegmentTab
          tabs={tabs.map((tab) => tab.title)}
          onTabChange={handleTabChange}
          activeIndex={tabs.findIndex((tab) => tab.name === activeTab)}
        />

        {activeTab === "gists" ? <GistsPage {...props} /> : renderNewsFeed()}
      </View>
    )
  } else {
    return renderNewsFeed()
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    backgroundColor: "white",
  },
})
