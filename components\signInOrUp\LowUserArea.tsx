import { Share } from "react-native"
import { EventType, trackEvent } from "@/utils/tracking"
import { FullImageScreen } from "./FullImageScreen"

interface Props {
  onNext: () => void
}

export const LowUserArea = ({ onNext }: Props) => {
  const handleInviteFriends = async () => {
    try {
      await trackEvent(EventType.TappedShareInOnboarding)
      await Share.share({
        message:
          "Hi! I thought you might enjoy InPress, a news app that gamifies your knowledge building and gives you opportunities for finding friends and dates based on your interests. Check it out here! https://inpress.app",
        url: "https://inpress.app",
      })
    } catch (error) {
      console.error("Error sharing:", error)
    }
  }

  return (
    <FullImageScreen
      title={"A news way to connect near you"}
      description={
        "InPress Social is just getting started in your area - connections may be limited for now. Invite friends and grow your network while we build!"
      }
      imageSource={require("../../app/assets/LowAreaBackground.png")}
      primaryButtonText={"Let's go"}
      secondaryButtonText={"Invite Friends"}
      onPrimaryButtonTap={onNext}
      onSecondaryButtonTap={handleInviteFriends}
    />
  )
}
