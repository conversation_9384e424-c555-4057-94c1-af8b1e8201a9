import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"
const SettingsIcon = (props: SvgProps) => (
  <Svg width={20} height={20} fill="none" {...props}>
    <Path
      fill="#000"
      d="M10 0c5.523 0 10 4.477 10 10s-4.477 10-10 10S0 15.523 0 10 4.477 0 10 0Zm.16 14a6.981 6.981 0 0 0-5.147 2.256A7.966 7.966 0 0 0 10 18c1.97 0 3.773-.712 5.167-1.892A6.979 6.979 0 0 0 10.16 14ZM10 2a8 8 0 0 0-6.384 12.821A8.975 8.975 0 0 1 10.16 12a8.972 8.972 0 0 1 6.362 2.634A8 8 0 0 0 10 2Zm0 1a4 4 0 1 1 0 8 4 4 0 0 1 0-8Zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"
    />
  </Svg>
)
export default SettingsIcon

export const SettingsSelectedIcon = (props: SvgProps) => (
  <Svg width={21} height={20} fill="none" {...props}>
    <Path
      fill="#000"
      d="M10.5 0c5.52 0 10 4.48 10 10s-4.48 10-10 10-10-4.48-10-10 4.48-10 10-10ZM4.523 13.416C5.991 15.606 8.195 17 10.66 17c2.464 0 4.669-1.393 6.136-3.584A8.968 8.968 0 0 0 10.66 11a8.968 8.968 0 0 0-6.137 2.416ZM10.5 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
    />
  </Svg>
)
