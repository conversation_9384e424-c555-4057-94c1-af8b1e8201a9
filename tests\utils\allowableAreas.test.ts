import {
  calculateDistanceInMiles,
  isInTheUSA,
  washingtonDC,
} from "@/utils/allowableAreas"

describe("Location", () => {
  it("calculates distance between dc and boston", async () => {
    const distance = calculateDistanceInMiles(
      washingtonDC.latitude,
      washingtonDC.longitude,
      42.3601,
      -71.0589,
    )
    expect(distance).toBeCloseTo(394)
  })

  it("does not allow nigera", async () => {
    const position = {
      coords: {
        latitude: 6.5244,
        longitude: 3.3792,
      },
    }
    expect(isInTheUSA(position.coords)).toBe(false)
  })

  it("detects border cities as in usa", async () => {
    const cities = [
      { name: "Seattle", latitude: 47.6062, longitude: -122.3321 },
      { name: "El Paso", latitude: 31.7619, longitude: -106.485 },
      { name: "Detroit", latitude: 42.3314, longitude: -83.0458 },
      { name: "Boston", latitude: 42.3601, longitude: -71.0589 },
      { name: "Miami", latitude: 25.7617, longitude: -80.1918 },
    ]

    cities.forEach((city) => {
      expect(isInTheUSA(city)).toBe(true)
    })
  })

  it("detects foreign cities as out of usa", async () => {
    const cities = [
      { name: "Mexico City", latitude: 19.4326, longitude: -99.1332 },
      { name: "Mumbai", latitude: 19.076, longitude: 72.8777 },
      { name: "Shanghai", latitude: 31.2304, longitude: 121.4737 },
      { name: "Paris", latitude: 48.8566, longitude: 2.3522 },
      { name: "Lagos", latitude: 6.5244, longitude: 3.3792 },
    ]

    cities.forEach((city) => {
      expect(isInTheUSA(city)).toBe(false)
    })
  })
})
