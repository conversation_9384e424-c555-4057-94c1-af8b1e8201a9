import { View, StyleSheet } from "react-native"
import { TextInput } from "../TextInput"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"

interface BiographyStepProps {
  biography: string
  onChange: (biography: string) => void
}

const NUMBER_OF_LINES = 4

export const textInputProps = {
  maxLength: 200,
  multiline: true,
  label: "Bio (optional)",
  numberOfLines: NUMBER_OF_LINES,
}

export const BiographyStep = ({ biography, onChange }: BiographyStepProps) => (
  <View style={styles.container}>
    <TextInput
      {...textInputProps}
      value={biography}
      onChangeText={onChange}
      style={styles.input}
    />
  </View>
)

export const styles = StyleSheet.create({
  input: { height: NUMBER_OF_LINES * 22 },
  container: {
    marginVertical: hp("3%"),
  },
})
