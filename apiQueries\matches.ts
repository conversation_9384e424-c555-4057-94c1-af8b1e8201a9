import { post } from "@/network"
import { INPRESS_API_URL } from "./constants"

type UnmatchParams = {
  channel_id: string
  unmatched_user_id: string
  connection_mode: string
}

export const unmatch = async ({
  token,
  channelId,
  unmatchedUserId,
  connectionMode,
}: {
  token: string
  channelId: string
  unmatchedUserId: string
  connectionMode: string
}) => {
  await post<UnmatchParams, void>(
    `${INPRESS_API_URL}/unmatch`,
    {
      channel_id: channelId,
      unmatched_user_id: unmatchedUserId,
      connection_mode: connectionMode,
    },
    token,
  )
}
