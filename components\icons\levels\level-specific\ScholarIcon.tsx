import Svg, { Path, SvgProps } from "react-native-svg"
export const ScholarIcon = (props: SvgProps) => (
  <Svg width={20} height={20} viewBox="0 0 30 29" fill="none">
    <Path
      d="M6.8877 15.8916C6.30466 15.5723 5.59863 15.9986 5.59863 16.6631V22.6816C5.59863 23.423 6.03963 24.1506 6.70117 24.4844L14.3984 28.3662C14.9734 28.6562 15.7203 28.657 16.2969 28.3662L23.9941 24.4844C24.6538 24.1516 25.0967 23.4243 25.0967 22.6816V16.6631C25.0967 15.9986 24.3907 15.5723 23.8076 15.8916L16.6875 19.791C16.3486 19.9766 15.8536 20.0742 15.3477 20.0742C14.8418 20.0742 14.3468 19.9767 14.0078 19.791L6.8877 15.8916ZM16.0488 1.04395C15.6121 0.803589 15.0833 0.803589 14.6465 1.04395L1.625 8.20898C0.609321 8.76791 0.609323 10.2389 1.625 10.7979L14.6465 17.9639C15.0833 18.2042 15.6121 18.2042 16.0488 17.9639L26.7939 12.0498V17.5293C26.7939 18.3386 27.4273 19.0039 28.2188 19.0039C29.01 19.0037 29.6426 18.3384 29.6426 17.5293V10.2305C30.0191 9.5571 29.8319 8.62811 29.0703 8.20898L16.0488 1.04395Z"
      fill={props.fill || "black"}
      stroke={props.fill || "black"}
      strokeWidth={0.273401}
      strokeLinecap="round"
    />
  </Svg>
)
