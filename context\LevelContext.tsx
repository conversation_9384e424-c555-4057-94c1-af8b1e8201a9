import React, { createContext, useContext, useEffect, useState } from "react"
import {
  cacheLevelAssets,
  cacheStreakAssets,
  getLevels,
  getStats,
  getStreaks,
} from "@/apiQueries/levels"
import { getSession } from "@/context/session"
import _ from "lodash"
import { Level, LevelStats, Streak } from "@/types/levels"
import {
  loadfromStorage,
  saveToStorage,
  StorageKeys,
} from "@/utils/localStorage"

interface LevelsContextProps {
  stats: LevelStats | null
  setStats: React.Dispatch<React.SetStateAction<LevelStats | null>>
  refreshStats: () => Promise<void>
  levels: Level[] | null
  streaks: Streak[] | null
  loading: boolean
  calculateLevel: (points: number) => Level | undefined
  calculateNextLevel: (points: number) => Level | undefined
}

const LevelsContext = createContext<LevelsContextProps | undefined>(undefined)

export type LevelsProviderProps = {
  initialData?: {
    stats?: LevelStats
    levels?: Level[]
    streaks?: Streak[]
  }
  simulatedLoadingDelay?: number
  children: React.ReactNode
}

export const LevelsProvider: React.FC<LevelsProviderProps> = ({
  initialData,
  simulatedLoadingDelay,
  children,
}) => {
  const [stats, setStats] = useState<LevelStats | null>(null)
  const [levels, setLevels] = useState<Level[] | null>(null)
  const [streaks, setStreaks] = useState<Streak[] | null>(null)
  const [loading, setLoading] = useState(true)

  const refreshStats = async () => {
    const session = getSession()
    const stats = await getStats(session!.token)
    setStats((prev) => (_.isEqual(prev, stats) ? prev : stats))
  }

  const calculateLevel = (points: number): Level | undefined => {
    if (!levels) return undefined
    return levels.findLast((level) => points >= level.pointsRequired)
  }

  const calculateNextLevel = (points: number): Level | undefined => {
    if (!levels) return undefined
    return levels.find((level) => points < level.pointsRequired)
  }

  useEffect(() => {
    setTimeout(() => {
      if (initialData?.stats) {
        setStats(initialData.stats)
      }
      if (initialData?.levels) {
        setLevels(initialData.levels)
      }
      if (initialData?.streaks) {
        setStreaks(initialData.streaks)
      }
    }, simulatedLoadingDelay || 0)
  }, [initialData])

  useEffect(() => {
    const fetchLevels = async () => {
      try {
        const storedStats = await loadfromStorage<LevelStats | null>(
          StorageKeys.Stats,
        )
        if (storedStats) {
          setStats(storedStats)
        }

        const storedLevels = await loadfromStorage<Level[]>(StorageKeys.Levels)
        if (storedLevels) {
          setLevels(storedLevels)
        }

        const session = getSession()
        if (!session) {
          throw new Error("No session available")
        }
        const token = session.token

        const stats = await getStats(token)
        setStats(stats)
        saveToStorage({ key: StorageKeys.Stats, value: stats })

        const fetchedLevels = await getLevels(token)
        setLevels(fetchedLevels)
        saveToStorage({ key: StorageKeys.Levels, value: fetchedLevels })

        const streaks = await getStreaks(token)
        setStreaks(streaks)
      } catch (error) {
        console.error("Error fetching levels:", error)
      } finally {
        setLoading(false)
      }
    }

    const cacheInitialState = async () => {
      const cachedLevels = await Promise.all(
        initialData!.levels!.map(async (level) => {
          return await cacheLevelAssets(level)
        }),
      )

      const cachedStreaks = await Promise.all(
        initialData!.streaks!.map(cacheStreakAssets),
      )

      setTimeout(() => {
        setLevels(cachedLevels)
        setStreaks(cachedStreaks)
        setLoading(false)
      }, simulatedLoadingDelay || 0)
    }

    if (!initialData) fetchLevels()
    else cacheInitialState()
  }, [initialData])

  return (
    <LevelsContext.Provider
      value={{
        stats,
        setStats,
        refreshStats,
        levels,
        streaks,
        loading,
        calculateLevel,
        calculateNextLevel,
      }}
    >
      {children}
    </LevelsContext.Provider>
  )
}

export const useLevels = () => {
  const context = useContext(LevelsContext)
  if (!context) {
    throw new Error("useLevels must be used within a LevelsProvider")
  }
  return context
}
