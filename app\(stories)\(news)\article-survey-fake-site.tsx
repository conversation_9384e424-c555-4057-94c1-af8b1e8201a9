import { ArticleSurvey } from "@/components/ratings/ArticleSurvey"
import { Text } from "@/components/Themed"
import _ from "lodash"
import { ScrollView, View } from "react-native"

export default function ArticleSurveyStory() {
  return (
    <View style={{ flex: 1 }}>
      <ScrollView>
        <Text>
          {_.range(15).map(
            (i) =>
              "Lorem ipsum dolor sit amet, consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Lorem ipsum dolor sit amet, consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
          )}
        </Text>
      </ScrollView>
      <ArticleSurvey
        initialPoints={0}
        soundsAndHapticsOn
        onSurveyComplete={async () => null as any}
        onFetchLeaderboard={async () => []}
      />
    </View>
  )
}
