import { post } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { getAnonymousUserId } from "@/utils/tracking"

export const uploadScreenshot = async (uri: string) => {
  const publicToken = process.env.EXPO_PUBLIC_PUBLIC_API_TOKEN

  const anonId = await getAnonymousUserId()

  try {
    const formData = new FormData()
    formData.append("image", {
      uri,
      name: "screenshot.png",
      type: "image/png",
    } as any)

    formData.append("anonymous_user_id", anonId)

    await post(`${INPRESS_API_URL}/upload-screenshot`, formData, publicToken, {
      "Content-Type": "multipart/form-data",
    })
  } catch (error) {
    console.error("Error uploading screenshot:", error)
    throw error
  }
}
