import { ImageProps } from "expo-image"
import { View, StyleSheet, Text } from "react-native"
import { Button } from "@/components/Button"
import { Screen } from "@/components/Themed"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Image } from "expo-image"
import LogoIcon from "../icons/onboarding/LogoIcon"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import { fontStyles } from "@/styles"

interface Props {
  title: string
  description: string
  imageSource: ImageProps["source"]
  primaryButtonText?: string
  secondaryButtonText?: string
  onPrimaryButtonTap?: () => void
  onSecondaryButtonTap?: () => void
}

export const FullImageScreen = ({
  title,
  description,
  imageSource,
  primaryButtonText,
  secondaryButtonText,
  onPrimaryButtonTap,
  onSecondaryButtonTap,
}: Props) => {
  const { top } = useSafeAreaInsets()

  return (
    <Screen style={styles.container} statusBarStyle="light-content">
      <Image
        contentFit="cover"
        contentPosition={"top"}
        style={styles.backgroundImage}
        source={imageSource}
      />
      <View style={[styles.topContainer, { paddingTop: top + 20 }]}>
        <LogoIcon />
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.description}>{description}</Text>
        </View>
      </View>

      <View style={styles.bottomContainer}>
        {primaryButtonText && onPrimaryButtonTap && (
          <Button
            text={primaryButtonText}
            onPress={onPrimaryButtonTap}
            style={styles.primaryButton}
            textStyle={styles.primaryButtonText}
          />
        )}
        {secondaryButtonText && onSecondaryButtonTap && (
          <Button
            text={secondaryButtonText}
            onPress={onSecondaryButtonTap}
            style={styles.secondaryButton}
            textStyle={styles.secondaryButtonText}
          />
        )}
      </View>
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 0,
    alignItems: "center",
    backgroundColor: "rgb(14, 14, 14)",
  },
  backgroundImage: {
    marginTop: 40,
    flex: 1,
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  topContainer: {
    alignItems: "center",
    width: wp("90%"),
    gap: hp(3),
  },
  textContainer: {
    gap: hp(1),
    width: wp(80),
  },
  title: {
    fontSize: 42,
    fontWeight: "400",
    color: "white",
    textAlign: "center",
    ...fontStyles.editorial,
  },
  description: {
    fontFamily: "InterTight-Regular",
    fontWeight: "400",
    fontSize: 16,
    color: "white",
    textAlign: "center",
    alignSelf: "center",
  },
  bottomContainer: {
    marginTop: "auto",
    marginBottom: 30,
    gap: 20,
  },
  primaryButton: {
    backgroundColor: "white",
  },
  primaryButtonText: {
    color: "black",
  },
  secondaryButton: {
    backgroundColor: "black",
  },
  secondaryButtonText: {
    color: "white",
  },
})
