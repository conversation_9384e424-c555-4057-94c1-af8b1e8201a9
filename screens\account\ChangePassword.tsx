import { useState } from "react"
import { StyleSheet, <PERSON><PERSON> } from "react-native"
import { Screen } from "@/components/Themed"
import { router } from "expo-router"
import { resetPassword } from "@/apiQueries/auth"
import { useSession } from "@/ctx"
import { PasswordStep } from "@/components/signInOrUp/PasswordStep"
import { findPasswordIssues } from "@/utils/password"
import { Button } from "@/components/Button"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"

export default function ChangePassword() {
  const [password, setPassword] = useState<string>()
  const [confirmedPassword, setConfirmedPassword] = useState<string>()

  const { session } = useSession()

  const handleResetPassword = async ({
    password,
    confirmedPassword,
  }: {
    password: string | undefined
    confirmedPassword: string | undefined
  }) => {
    if (!session) {
      return
    }

    const error = findPasswordIssues(password, confirmedPassword)

    if (!error && password && confirmedPassword) {
      try {
        await resetPassword({
          token: session.token,
          password: password,
          password_confirmation: confirmedPassword,
        })
        Alert.alert("Password changed successfully")
        router.back()
      } catch (error) {
        Alert.alert("Error changing password, please try again")
        console.error(error)
      }
    }
  }

  const passwordsAreValid = !findPasswordIssues(password, confirmedPassword)

  return (
    <Screen>
      <ScreenHeader title="Reset password" />
      <PasswordStep
        password={password}
        passwordConfirmation={confirmedPassword}
        onChangePassword={setPassword}
        onChangePasswordConfirmation={setConfirmedPassword}
      />
      <Button
        text="Reset password"
        onPress={() => handleResetPassword({ password, confirmedPassword })}
        style={styles.resetButton}
        disabled={!passwordsAreValid}
      />
    </Screen>
  )
}

const styles = StyleSheet.create({
  resetButton: {
    marginTop: 20,
  },
})
