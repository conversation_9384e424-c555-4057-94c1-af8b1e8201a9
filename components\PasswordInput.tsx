import { Feather } from "@expo/vector-icons"
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  TextInput,
} from "react-native"

const PasswordInput = ({
  value,
  onChange,
  onVisibilityChange,
  secureTextEntry,
  label,
}: {
  value: string | undefined
  onChange: (value: string) => void
  secureTextEntry: boolean
  onVisibilityChange: () => void
  label: string
}) => {
  return (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <View style={styles.passwordInputContainer}>
        <TextInput
          style={styles.passwordInput}
          value={value}
          placeholder="Must be 8 characters"
          placeholderTextColor="gray"
          onChangeText={onChange}
          autoCapitalize="none"
          secureTextEntry={secureTextEntry}
        />
        <TouchableOpacity style={styles.eyeIcon} onPress={onVisibilityChange}>
          <Feather
            name={secureTextEntry ? "eye" : "eye-off"}
            size={24}
            color="gray"
          />
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default PasswordInput

const styles = StyleSheet.create({
  inputContainer: {
    width: 353,
    height: 80,
    marginBottom: 20,
  },
  inputLabel: {
    fontFamily: "InterTight-Regular",
    fontWeight: "400",
    fontSize: 14,
    height: 18,
  },
  passwordInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    borderWidth: 1,
    borderColor: "gray",
    borderRadius: 4,
    paddingHorizontal: 20,
    backgroundColor: "white",
    height: 56,
  },
  passwordInput: {
    flex: 1,
    fontFamily: "InterTight-Regular",
    fontWeight: "400",
    fontSize: 15,
    color: "gray",
  },
  eyeIcon: {
    position: "absolute",
    right: 20,
  },
})
