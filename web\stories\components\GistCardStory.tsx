import { GistCard } from "@/components/gists/GistCard"
import React from "react"
import { Article } from "@/types/news"
import { articles } from "../../../app/(stories)/(news)/feed/news"
import { View, StyleSheet } from "react-native"

const GistCardStory: React.FC = () => {
  const article: Article = {
    ...articles[0],
    summaryPoints: [
      "This is a sample bullet point that summarizes the gist of the article.",
      "Another bullet point for the gist, which is a summary of the article.",
      "Yet another point to summarize the gist of the article, providing insights.",
    ],
  }

  return (
    <View style={styles.container}>
      <GistCard
        isVisible={true}
        article={article}
        demoMode={true}
        onImageLoaded={() => console.log("Image loaded:", article.imageUrl)}
        onTimerCompleted={() => console.log("Timer completed")}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    height: 600,
    width: 350,
    alignSelf: "center",
  },
})

export default GistCardStory
