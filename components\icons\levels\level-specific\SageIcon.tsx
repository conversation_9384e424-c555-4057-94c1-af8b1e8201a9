import Svg, { Path, SvgProps } from "react-native-svg"
export const SageIcon = (props: SvgProps) => (
  <Svg width={20} height={20} viewBox="0 0 23 29" fill="none">
    <Path
      d="M6.46875 11.5C6.06146 11.5 5.72029 11.362 5.44525 11.086C5.17021 10.81 5.03221 10.4688 5.03125 10.0625C5.03029 9.65617 5.16829 9.315 5.44525 9.039C5.72221 8.763 6.06337 8.625 6.46875 8.625C6.87413 8.625 7.21577 8.763 7.49369 9.039C7.7716 9.315 7.90913 9.65617 7.90625 10.0625C7.90337 10.4688 7.76537 10.8105 7.49225 11.0874C7.21912 11.3644 6.87796 11.5019 6.46875 11.5ZM16.5312 11.5C16.124 11.5 15.7828 11.362 15.5078 11.086C15.2327 10.81 15.0947 10.4688 15.0938 10.0625C15.0928 9.65617 15.2308 9.315 15.5078 9.039C15.7847 8.763 16.1259 8.625 16.5312 8.625C16.9366 8.625 17.2783 8.763 17.5562 9.039C17.8341 9.315 17.9716 9.65617 17.9688 10.0625C17.9659 10.4688 17.8279 10.8105 17.5548 11.0874C17.2816 11.3644 16.9405 11.5019 16.5312 11.5ZM11.5 28.75C8.28958 28.75 5.57031 27.6359 3.34219 25.4078C1.11406 23.1797 0 20.4604 0 17.25V10.0625C0 7.13958 1.15 4.73177 3.45 2.83906C5.75 0.946354 8.43333 0 11.5 0C14.5667 0 17.25 0.946354 19.55 2.83906C21.85 4.73177 23 7.13958 23 10.0625V24.4375C23 24.8448 22.862 25.1864 22.586 25.4624C22.31 25.7384 21.9688 25.876 21.5625 25.875H20.125C18.9271 25.875 17.9089 25.4557 17.0703 24.6172C16.2318 23.7786 15.8125 22.7604 15.8125 21.5625V21.4906C15.8125 21.0115 15.663 20.6521 15.364 20.4125C15.065 20.1729 14.7353 20.0531 14.375 20.0531C14.0147 20.0531 13.6855 20.1729 13.3874 20.4125C13.0894 20.6521 12.9394 21.0115 12.9375 21.4906V21.5625C12.9375 22.7125 13.1891 23.7786 13.6922 24.7609C14.1953 25.7432 14.8661 26.5578 15.7047 27.2047C16.0641 27.4682 16.1901 27.7917 16.0828 28.175C15.9754 28.5583 15.7416 28.75 15.3812 28.75H11.5ZM11.5 17.25C13.776 17.25 15.7828 16.5849 17.5203 15.2548C19.2577 13.9246 20.126 12.1938 20.125 10.0625C20.125 9.22396 19.9813 8.43908 19.6938 7.70787C19.4063 6.97667 18.999 6.32404 18.4719 5.75C16.9385 5.79792 15.6328 6.37292 14.5547 7.475C13.4766 8.57708 12.9375 9.91875 12.9375 11.5C12.9375 11.9073 12.8 12.2489 12.5249 12.5249C12.2499 12.8009 11.9083 12.9385 11.5 12.9375C11.0917 12.9365 10.7506 12.7985 10.4765 12.5235C10.2024 12.2485 10.0644 11.9073 10.0625 11.5C10.0625 9.91875 9.52344 8.58906 8.44531 7.51094C7.36719 6.43281 6.06146 5.85781 4.52813 5.78594C4.00104 6.36094 3.59375 7.00781 3.30625 7.72656C3.01875 8.44531 2.875 9.22396 2.875 10.0625C2.875 12.1948 3.74373 13.926 5.48119 15.2562C7.21865 16.5864 9.22492 17.251 11.5 17.25Z"
      fill={props.fill || "black"}
    />
  </Svg>
)
