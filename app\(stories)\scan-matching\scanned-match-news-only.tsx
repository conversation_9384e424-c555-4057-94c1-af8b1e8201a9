import { ScannedMatchModal_ } from "@/components/leads/ScannedMatchModal"
import { defaultProps, scannedMatch } from "./scanned-match-modal"
import { USER } from "../(account)/account"
import { User } from "@/types/user"

const newsOnlyUser: User = {
  ...USER,
  images: [],
  isNewsOnly: true,
}

export default function Story() {
  return (
    <ScannedMatchModal_
      {...defaultProps}
      scannedMatch={{ ...scannedMatch, thisUser: newsOnlyUser }}
    />
  )
}

export { scannedMatch }
