import GistsStack from "@/components/gists/GistsStack"
import SectionSelector from "@/components/gists/SectionSelector"
import { NewsFeedProps } from "@/components/news/NewsFeed"
import StatsBar from "@/components/news/statsBar/StatsBar"
import { Screen, View } from "@/components/Themed"
import { StyleSheet } from "react-native"

export type GistsPageProps = Pick<NewsFeedProps, "session">

export const GistsPage = ({ session }: GistsPageProps) => {
  return (
    <Screen style={styles.container}>
      <StatsBar />
      <View style={styles.gistsContainer}>
        <SectionSelector />
        <GistsStack />
      </View>
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    gap: 20,
    zIndex: 2,
    paddingHorizontal: 0,
  },
  gistsContainer: {
    gap: 20,
    flex: 1,
    paddingHorizontal: 24,
  },
})
