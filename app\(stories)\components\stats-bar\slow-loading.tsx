import { StoryLevelsProvider } from "../../story-components/StoryLevelsProvider"
import { NewsPage_ } from "@/screens/news/NewsPage"
import { NEWSFEED_PROPS } from "../../(news)/feed/news"
import { Screen } from "@/components/Themed"

export default function Story() {
  return (
    <StoryLevelsProvider simulatedLoadingDelay={3000}>
      <Screen style={{ paddingHorizontal: 0 }}>
        <NewsPage_ {...NEWSFEED_PROPS} />
      </Screen>
    </StoryLevelsProvider>
  )
}
