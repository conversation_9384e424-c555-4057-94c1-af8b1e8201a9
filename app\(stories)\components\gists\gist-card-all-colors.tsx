import { GistCard } from "@/components/gists/GistCard"
import { Screen } from "@/components/Themed"
import { articles } from "../../(news)/feed/news"
import { ScrollView, View } from "react-native"
import { Article } from "@/types/news"

export default function Story() {
  const article: Article = {
    ...articles[0],
    summaryPoints: [
      "This is a sample bullet point that summarizes the gist of the article.",
      "Another bullet point for the gist, which is a summary of the article.",
    ],
  }

  const sections = [
    "topStories",
    "local",
    "culture",
    "sports",
    "worldNews",
    "feelGood",
  ]

  return (
    <Screen>
      <ScrollView>
        {sections.map((section, index) => (
          <View style={{ height: 600 }} key={index}>
            <GistCard
              article={{ ...article, frontpageSection: section }}
              isVisible={true}
              onTimerCompleted={() => {}}
            />
          </View>
        ))}
      </ScrollView>
    </Screen>
  )
}
