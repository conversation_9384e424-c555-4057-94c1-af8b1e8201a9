-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:1:1-59:12
MERGED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:1:1-59:12
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:expo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:lottie-react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:react-native-picker_picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-16:12
MERGED from [:stream-io_flat-list-mvcp] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@stream-io\flat-list-mvcp\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:mixpanel-react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\mixpanel-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:1-50:12
MERGED from [:react-native-maps] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-purchases] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-purchases\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-view-shot] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-asset] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-av] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-av\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-blur] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:expo-constants] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-37:12
MERGED from [:expo-crypto] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-crypto\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-device] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-device\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:expo-image-loader] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-manipulator] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-manipulator\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-57:12
MERGED from [:expo-manifests] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-keep-awake] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linear-gradient] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linking] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-localization] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-localization\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-17:12
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-29:12
MERGED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:expo-splash-screen] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-system-ui] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-tracking-transparency] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-tracking-transparency\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b8d950ace3cb359707210e58db7abd6\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb7edd6c4a6a4db7c12a49d0d8e72c63\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0534a9054bf27e4790566ff28e2f112d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d799b77c255eed75f24a9bf55ca48f8f\transformed\facebook-login-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\977af47babcea9cccb9f7e8eb42ae4b5\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3fd05bc4f375e9bd686e94f570f0140\transformed\facebook-share-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:9:1-42:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8a37af8a95405e65f6c01734d759273\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\655f8e796df12793c11e60201ac13df0\transformed\glide-plugin-3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c10ab051383575401690831b6a17cce7\transformed\avif-integration-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71858c920684cd5406a4d240f9da646b\transformed\glide-transformations-4.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13163a34b81a53dc146e7d6ecff161ed\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b3347a055411cfaa73a8d0e5b66261\transformed\lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5ee820f4e7da6e30cce364b067c95a5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb33eb2bd08a89dcf71f021fda63e323\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39bede94ba66b0b16feaf12e7d1cc13d\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d874d656d3edc0a36a0b7a6124463c43\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\166bb1aae962427afb8e3b148b07eabf\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2befb358b8fb82507640b6dbe4c56fa\transformed\awebp-3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ed15c38866280370a59d9d62d3cb16\transformed\apng-3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d188075c79fdd4e5500862b567aa471\transformed\gif-3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3cedc0728cba80254f49e59e90e99a6\transformed\avif-3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5f2661155b37b34c475d3a6bbc83197\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4eb4058a19910a643797faa87a550fc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c37ceaed472349d4a4a0cc32b5c62106\transformed\frameanimation-3.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fd63cfeaf959e262224845b26be4452\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b523cce3192ee7592e4492a6cabbc8a\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.revenuecat.purchases:purchases-hybrid-common:11.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbbac0bf050279cea6c5d7c0c531c2dc\transformed\purchases-hybrid-common-11.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:2:1-35:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edc8fc3c6c7d99c6eacdb7423963a264\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacef3d3777c80b11306242ab09a95e\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4f20aabcae7962093e0fbd762da8fb\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7835283f83a1c1b35337064dad2202d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62f3a0ae34f89bbb0d445c29f05239f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83ca796bc4a3817c2de4ecea434ae9d7\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d3a26c867c873f2bbcf3c9523bc106a\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80ce655b717f8d0500f49bc0a89c3f79\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c145a2625628dc1692a07a5e172b5bb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b07156b6523fe77a52cb3b4361b39785\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea0148c2d5ec84520fed95ead0cd7d7\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6baf68c68fe37274bc7075655edbd953\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cee8421a8599efcc225514e5dd23a061\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8a2e7a47abfa6c5ae9d97111f9a4da\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1717c9d4f49c202931895069c5eacdb\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01193c995888536bd997d04e0cdddb15\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de35ef56d110f5bb03d40da766436b7f\transformed\sentry-android-7.22.5\AndroidManifest.xml:2:1-10:12
MERGED from [io.sentry:sentry-android-ndk:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\609e1134ca6dae892552d18d7174d246\transformed\sentry-android-ndk-7.22.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:2:1-23:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13ff7521b1675fdd563d46c8c8ff89b8\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483b0b4d2555cb7cbf1ba22a708479a3\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:9:1-53:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5584d12f2c37bc8e47f7ac394dd8d9e2\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74f2980e8f562367f144d2927d88a507\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\049739690fd710de79ff2535bcbe8cdb\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7432ec5afba9dc30ccd67a21ec2bd07d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f18b16637dbdcc969dd33e5f8a43d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b52d94765285fdc976358000c6e69535\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7f386dc6e1c6297614f0be80f0a4395\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01334412ef15649e74ec9893d78559a3\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431c8829f6f6b27b6455a795856a3344\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46058ee95a895bce0736fb77cdf9c28c\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27d8e68d5a57b657f05297cae5e4b495\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0845cd2c721677b3fca482ee21726580\transformed\animated-gif-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a795cba5141d712ecf2d9ef334380a13\transformed\webpsupport-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a3b1ef2f426ae2baf11951ed0664\transformed\fresco-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b8d16a39b9fe0899ce6b7fb81e1173b\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6edf58fea11e908378920dfe0b347a\transformed\animated-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7293c7f8feff3ceb025bba98dda16bd6\transformed\animated-drawable-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5feedd9d96b30d7a31a16aa723639fa0\transformed\vito-options-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85586c22835c13b9d3a8a27f8f46d86d\transformed\drawee-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5ed67c38d669cd2dff2013f65485a1d\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9bcc3f7d14bfc7d1a5a550f1d2558c\transformed\memory-type-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\712f706943b2f9e2bbffae1a75b4d417\transformed\memory-type-java-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83d94a538df045225779dc392165fda6\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c920b0772d4eca7fb216af9cc41579\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e40087345e8f199a8ab9777cebe4613c\transformed\imagepipeline-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b57ecf85b5b544ffc5ab3e845063f4\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cac981f9c047f247d9085a4b79492b5\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68e616a6cabdf12beeef65edd4b10a1a\transformed\middleware-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5e52fe4de0043982ed2cc46b6f28b7c\transformed\ui-common-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c8e87f614b82a51eabed9f96ab3b9a6\transformed\soloader-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a62f1488b51fb180440e91af920c4c7\transformed\fbcore-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a287922a3c2204766df39f0f00cd33f\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcfbb00bfb44d8c67b52adbc737fe3f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\435430fbaf443eda8529ebdd0e3c7bf1\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5146738535d3021a7116f39c42fe567a\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc4b7785b7f60e7f8449e110fc2cc723\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f927aff154629f3c8ace576ee7d80\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba320662f2878e53556aae079acc84fd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d62d9514e9e9cb330919eb1b00be688f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dccb93d324f2aeeafe661d4bbb7df67\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6f5913789ae904936625fb3c2a66956\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\338ba257c5fa7113414a02a100375ed8\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b0f59e36d37fb7c46f9729b83779f2\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a469c90230d0dbbff23d489378f153f4\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8360839840b029eae401d3de045f15\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c170203461d3e4e0d521b3019b09b9d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56871df614f2f5c9a79b4b54c9edca50\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15214162fcd85290223d3ecfb84d2ee5\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaa3f7797a90b961a6d492a0de03cfa6\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c2e74f5ebae07930583f59e1060ea8\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b99ebfa5cff4c3c38777aaa7b2191b\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f00ce2e2b7e2c6c6fc4e719bf2b702c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f59f02b7bbd1aabcc70c251eded0cec\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\996e6ac5dce730a58e974de0c4fd1f63\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47a9c898aa0eb32234d150f2eadf2db\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32934037b033d108e264c0811eca2dd2\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d3f0a98accf3c3f3da3a8731edebae\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0765ed377bf3d44ecfda4906552fc54\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e072103a8b3d480b477db02d208f061\transformed\lifecycle-runtime-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f97a256d1c5f7c498ac1fcc5354ff1c\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5c522a3f043374cf9fb8c527af84193\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3079fc4a3a067f116fdc198af709606\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04bd76be230be3afda015e7c0cd50c4f\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3722047ea58d3e32ab834186a09a4add\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\135d45547a57ba4ef6d73102fe49363e\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4de29c54e3244094408eb217e4741f9c\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f290a05a084430f6a0c046fad5a13d64\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d0bcc65bed66d2181e2a2293229def3\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739cc41763d5deb2d5267b2bd7911623\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\941a9e15e034d8a2189a15fca62a413d\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\964575875962add97bbfbf58ca5ad772\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\868bac4eefab012de29ba5d141b7a6e4\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05a74691b8cd1b38d4395dfe50a0b4ee\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\797455f429f09da3ef1b9ec5ccd1f4cb\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:2:1-46:12
MERGED from [io.sentry:sentry-android-replay:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52709b16bfe4b5af8f5a5eabe0c766ee\transformed\sentry-android-replay-7.22.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79daa22662937dd899049551008c5871\transformed\vito-renderer-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86fe3428fb49648b88cd1b5b200fc124\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a130c94486d708f4697639d7d8d14415\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee8dc5aa0b1ccacdd316db7adb3cde2e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3354bc65abbd27a7ac207ece4b46bdb9\transformed\hermes-android-0.76.9-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8aa82df4ddf8339506f4afc921b6b4\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71f4855643d6ce49683684f629eb05c0\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\447637e3c436fc6104bb179c28646f95\transformed\viewbinding-8.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8177085846dba10b910889f894527d2f\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc7d22c5cacfd3c89bcbe1bdd26188de\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18712e53c24b49c1a495a3627a9be715\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\692110d5710e4745679c61385a3c3853\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71950d6bc8f566841daa9a357bf3c243\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7737405b45068d9ec56d8a3fa674d4d\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d12fa09022a714b4d137f5bc4014f79\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe67c9bee19cce2d0871fa0ea7ea074\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e60968fb9a48511e10a89487d8faf8e4\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9d0681bba8d2adc0d2049b30c6fe280\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f47940e7fe986e5236d3501a1436f189\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1372a0db8479314fc56da48b388483ad\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\231cba9907b2e796ad2bd58bd045de17\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2b3d6d162c714a5b6cd4dcbceb22d64\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f425149ea7ed6954b26e5d901a6cd4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071b2577bf84f70c4a8c067e6f7b12\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1199f669f3e81f19e89663e4ebda2c6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6841415a811a852755cb4235ca8d54a6\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071cc2099c9bf7e7d24e7fdbb394e138\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:2:1-26:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3cbfd02ec95e6afb105434963506110\transformed\fbjni-0.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b7b7964f47632f9df96ec8f517abe7d\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:2:1-30:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51cb6e99fc37e441ccaa921889720566\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7219e558874300ce5364390263e13a10\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:2:3-78
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:12:5-81
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:12:5-81
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:3:3-76
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:14:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:14:5-79
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:4:3-62
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:4:20-60
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:5:3-64
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:7:5-67
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:7:5-67
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:7:5-67
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:26:5-67
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:8:5-67
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:8:5-67
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:5:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:6:3-77
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:6:20-75
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:7:3-69
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:7:20-67
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:8:3-77
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:5-17:38
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:5-17:38
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-80
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-80
	android:maxSdkVersion
		ADDED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:8:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:9:3-68
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:9:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:10:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:11:3-63
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:11:20-61
uses-permission#android.permission.WRITE_CONTACTS
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:12:3-70
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:12:20-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:13:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:13:20-76
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:14:3-76
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:14:5-79
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:14:5-79
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:28:5-79
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:28:5-79
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:14:20-74
queries
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:15:3-21:13
MERGED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-35:15
MERGED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-35:15
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-25:15
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-15:15
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-15:15
MERGED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-32:15
MERGED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-32:15
MERGED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:12:5-16:15
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:12:5-16:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:10:5-14:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:10:5-14:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:15:5-17:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:15:5-17:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:18:5-20:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:18:5-20:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:21:5-23:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:21:5-23:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:33:5-35:15
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:33:5-35:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:16:5-20:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:7-58
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:17:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:7-67
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:18:17-65
data
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:7-37
	android:scheme
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:19:13-35
application
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:3-58:17
MERGED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:3-58:17
MERGED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:3-58:17
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-14:19
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-14:19
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:5-48:19
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:5-48:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-55:19
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-15:19
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-15:19
MERGED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-41:19
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-27:19
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-27:19
MERGED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b8d950ace3cb359707210e58db7abd6\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b8d950ace3cb359707210e58db7abd6\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb7edd6c4a6a4db7c12a49d0d8e72c63\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb7edd6c4a6a4db7c12a49d0d8e72c63\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0534a9054bf27e4790566ff28e2f112d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0534a9054bf27e4790566ff28e2f112d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d799b77c255eed75f24a9bf55ca48f8f\transformed\facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d799b77c255eed75f24a9bf55ca48f8f\transformed\facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\977af47babcea9cccb9f7e8eb42ae4b5\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\977af47babcea9cccb9f7e8eb42ae4b5\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3fd05bc4f375e9bd686e94f570f0140\transformed\facebook-share-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3fd05bc4f375e9bd686e94f570f0140\transformed\facebook-share-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:10:5-14:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b3347a055411cfaa73a8d0e5b66261\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b3347a055411cfaa73a8d0e5b66261\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\166bb1aae962427afb8e3b148b07eabf\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\166bb1aae962427afb8e3b148b07eabf\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:7:5-16:19
MERGED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:7:5-16:19
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:9:5-14:19
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:9:5-14:19
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:18:5-33:19
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:18:5-33:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edc8fc3c6c7d99c6eacdb7423963a264\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edc8fc3c6c7d99c6eacdb7423963a264\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4f20aabcae7962093e0fbd762da8fb\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4f20aabcae7962093e0fbd762da8fb\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7835283f83a1c1b35337064dad2202d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7835283f83a1c1b35337064dad2202d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62f3a0ae34f89bbb0d445c29f05239f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62f3a0ae34f89bbb0d445c29f05239f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:9:5-21:19
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:9:5-21:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13ff7521b1675fdd563d46c8c8ff89b8\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13ff7521b1675fdd563d46c8c8ff89b8\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483b0b4d2555cb7cbf1ba22a708479a3\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483b0b4d2555cb7cbf1ba22a708479a3\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a287922a3c2204766df39f0f00cd33f\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a287922a3c2204766df39f0f00cd33f\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\964575875962add97bbfbf58ca5ad772\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\964575875962add97bbfbf58ca5ad772\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:40:5-44:47
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:40:5-44:47
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8aa82df4ddf8339506f4afc921b6b4\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8aa82df4ddf8339506f4afc921b6b4\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f425149ea7ed6954b26e5d901a6cd4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f425149ea7ed6954b26e5d901a6cd4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:18:5-28:19
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:18:5-28:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51cb6e99fc37e441ccaa921889720566\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51cb6e99fc37e441ccaa921889720566\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7219e558874300ce5364390263e13a10\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7219e558874300ce5364390263e13a10\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:248-291
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:248-291
	tools:ignore
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:116-161
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:116-161
	android:icon
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:81-115
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:81-115
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:221-247
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:221-247
	android:label
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:48-80
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:48-80
	android:fullBackupContent
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:43:9-64
	tools:targetApi
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:allowBackup
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:162-188
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:162-188
	android:dataExtractionRules
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:42:9-75
	android:theme
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:189-220
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:189-220
	tools:replace
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:16-47
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:22:16-47
meta-data#com.facebook.sdk.AdvertiserIDCollectionEnabled
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:5-100
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:78-98
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:23:16-77
meta-data#com.facebook.sdk.ApplicationId
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:5-103
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:62-101
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:24:16-61
meta-data#com.facebook.sdk.ApplicationName
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:5-89
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:64-87
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:25:16-63
meta-data#com.facebook.sdk.AutoInitEnabled
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:5-87
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:64-85
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:26:16-63
meta-data#com.facebook.sdk.AutoLogAppEventsEnabled
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:5-94
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:72-92
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:27:16-71
meta-data#com.facebook.sdk.ClientToken
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:5-107
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:60-105
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:28:16-59
meta-data#com.google.android.geo.API_KEY
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:5-119
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:62-117
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:29:16-61
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:5-83
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:60-81
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:30:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:5-119
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:73-117
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:31:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:5-105
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:81-103
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:32:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:5-99
	android:value
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:80-97
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:33:16-79
activity#com.scoopt.inpress.MainActivity
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:5-47:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:303-339
	android:launchMode
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:158-189
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:190-232
	android:exported
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:279-302
	android:configChanges
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:44-157
	android:theme
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:233-278
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:34:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:35:7-38:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:36:9-60
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:36:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:37:9-68
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:37:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.scoopt.inpress+data:scheme:exp+inpress-expo-router+data:scheme:myapp
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:39:7-46:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:9-67
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:41:19-65
activity#com.facebook.FacebookActivity
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:5-178
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
	android:label
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:144-176
	android:configChanges
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:60-143
	android:theme
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:23:13-63
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:48:15-59
activity#com.facebook.CustomTabActivity
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:5-56:16
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
	android:exported
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:61-84
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:49:15-60
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:@string/fb_login_protocol_scheme
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:50:7-55:23
uses-library#org.apache.http.legacy
ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:5-83
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:57-81
	android:name
		ADDED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\AndroidManifest.xml:57:19-56
uses-sdk
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
MERGED from [:expo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stream-io_flat-list-mvcp] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@stream-io\flat-list-mvcp\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stream-io_flat-list-mvcp] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@stream-io\flat-list-mvcp\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:mixpanel-react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\mixpanel-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:mixpanel-react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\mixpanel-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:5-44
MERGED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-purchases] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-purchases\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-purchases] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-purchases\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-view-shot] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-view-shot] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-asset] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-asset\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-av] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-av\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-av] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-av\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-blur\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-crypto] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-crypto\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-crypto] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-crypto\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-device] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-device\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-device] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-device\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:4:5-44
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:4:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-manipulator] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-manipulator\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-manipulator] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-manipulator\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-linking] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linking\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-localization] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-localization\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-localization] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-localization\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-system-ui\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-tracking-transparency] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-tracking-transparency\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-tracking-transparency] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-tracking-transparency\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b8d950ace3cb359707210e58db7abd6\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-android-sdk:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b8d950ace3cb359707210e58db7abd6\transformed\facebook-android-sdk-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb7edd6c4a6a4db7c12a49d0d8e72c63\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb7edd6c4a6a4db7c12a49d0d8e72c63\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0534a9054bf27e4790566ff28e2f112d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0534a9054bf27e4790566ff28e2f112d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d799b77c255eed75f24a9bf55ca48f8f\transformed\facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d799b77c255eed75f24a9bf55ca48f8f\transformed\facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\977af47babcea9cccb9f7e8eb42ae4b5\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-gamingservices:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\977af47babcea9cccb9f7e8eb42ae4b5\transformed\facebook-gamingservices-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3fd05bc4f375e9bd686e94f570f0140\transformed\facebook-share-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-share:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3fd05bc4f375e9bd686e94f570f0140\transformed\facebook-share-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8a37af8a95405e65f6c01734d759273\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8a37af8a95405e65f6c01734d759273\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\655f8e796df12793c11e60201ac13df0\transformed\glide-plugin-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:glide-plugin:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\655f8e796df12793c11e60201ac13df0\transformed\glide-plugin-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c10ab051383575401690831b6a17cce7\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:avif-integration:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c10ab051383575401690831b6a17cce7\transformed\avif-integration-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71858c920684cd5406a4d240f9da646b\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:glide-transformations:4.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71858c920684cd5406a4d240f9da646b\transformed\glide-transformations-4.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13163a34b81a53dc146e7d6ecff161ed\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13163a34b81a53dc146e7d6ecff161ed\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b3347a055411cfaa73a8d0e5b66261\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b3347a055411cfaa73a8d0e5b66261\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5ee820f4e7da6e30cce364b067c95a5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5ee820f4e7da6e30cce364b067c95a5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb33eb2bd08a89dcf71f021fda63e323\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb33eb2bd08a89dcf71f021fda63e323\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39bede94ba66b0b16feaf12e7d1cc13d\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39bede94ba66b0b16feaf12e7d1cc13d\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d874d656d3edc0a36a0b7a6124463c43\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d874d656d3edc0a36a0b7a6124463c43\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\166bb1aae962427afb8e3b148b07eabf\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\166bb1aae962427afb8e3b148b07eabf\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2befb358b8fb82507640b6dbe4c56fa\transformed\awebp-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:awebp:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2befb358b8fb82507640b6dbe4c56fa\transformed\awebp-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ed15c38866280370a59d9d62d3cb16\transformed\apng-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:apng:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ed15c38866280370a59d9d62d3cb16\transformed\apng-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d188075c79fdd4e5500862b567aa471\transformed\gif-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:gif:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d188075c79fdd4e5500862b567aa471\transformed\gif-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3cedc0728cba80254f49e59e90e99a6\transformed\avif-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:avif:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3cedc0728cba80254f49e59e90e99a6\transformed\avif-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5f2661155b37b34c475d3a6bbc83197\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5f2661155b37b34c475d3a6bbc83197\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4eb4058a19910a643797faa87a550fc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4eb4058a19910a643797faa87a550fc\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c37ceaed472349d4a4a0cc32b5c62106\transformed\frameanimation-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.penfeizhou.android.animation:frameanimation:3.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c37ceaed472349d4a4a0cc32b5c62106\transformed\frameanimation-3.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fd63cfeaf959e262224845b26be4452\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fd63cfeaf959e262224845b26be4452\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b523cce3192ee7592e4492a6cabbc8a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b523cce3192ee7592e4492a6cabbc8a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.revenuecat.purchases:purchases-hybrid-common:11.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbbac0bf050279cea6c5d7c0c531c2dc\transformed\purchases-hybrid-common-11.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases-hybrid-common:11.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbbac0bf050279cea6c5d7c0c531c2dc\transformed\purchases-hybrid-common-11.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edc8fc3c6c7d99c6eacdb7423963a264\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\edc8fc3c6c7d99c6eacdb7423963a264\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29c86a4f31eb4aa64bee72b043aab9c\transformed\play-services-ads-identifier-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacef3d3777c80b11306242ab09a95e\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacef3d3777c80b11306242ab09a95e\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4f20aabcae7962093e0fbd762da8fb\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad4f20aabcae7962093e0fbd762da8fb\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7835283f83a1c1b35337064dad2202d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7835283f83a1c1b35337064dad2202d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62f3a0ae34f89bbb0d445c29f05239f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f62f3a0ae34f89bbb0d445c29f05239f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83ca796bc4a3817c2de4ecea434ae9d7\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83ca796bc4a3817c2de4ecea434ae9d7\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d3a26c867c873f2bbcf3c9523bc106a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d3a26c867c873f2bbcf3c9523bc106a\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80ce655b717f8d0500f49bc0a89c3f79\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80ce655b717f8d0500f49bc0a89c3f79\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c145a2625628dc1692a07a5e172b5bb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c145a2625628dc1692a07a5e172b5bb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b07156b6523fe77a52cb3b4361b39785\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b07156b6523fe77a52cb3b4361b39785\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea0148c2d5ec84520fed95ead0cd7d7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea0148c2d5ec84520fed95ead0cd7d7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6baf68c68fe37274bc7075655edbd953\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6baf68c68fe37274bc7075655edbd953\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cee8421a8599efcc225514e5dd23a061\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cee8421a8599efcc225514e5dd23a061\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8a2e7a47abfa6c5ae9d97111f9a4da\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8a2e7a47abfa6c5ae9d97111f9a4da\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1717c9d4f49c202931895069c5eacdb\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1717c9d4f49c202931895069c5eacdb\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01193c995888536bd997d04e0cdddb15\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01193c995888536bd997d04e0cdddb15\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de35ef56d110f5bb03d40da766436b7f\transformed\sentry-android-7.22.5\AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de35ef56d110f5bb03d40da766436b7f\transformed\sentry-android-7.22.5\AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android-ndk:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\609e1134ca6dae892552d18d7174d246\transformed\sentry-android-ndk-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-ndk:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\609e1134ca6dae892552d18d7174d246\transformed\sentry-android-ndk-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13ff7521b1675fdd563d46c8c8ff89b8\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-applinks:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13ff7521b1675fdd563d46c8c8ff89b8\transformed\facebook-applinks-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483b0b4d2555cb7cbf1ba22a708479a3\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-messenger:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483b0b4d2555cb7cbf1ba22a708479a3\transformed\facebook-messenger-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5584d12f2c37bc8e47f7ac394dd8d9e2\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5584d12f2c37bc8e47f7ac394dd8d9e2\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74f2980e8f562367f144d2927d88a507\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74f2980e8f562367f144d2927d88a507\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\049739690fd710de79ff2535bcbe8cdb\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\049739690fd710de79ff2535bcbe8cdb\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7432ec5afba9dc30ccd67a21ec2bd07d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7432ec5afba9dc30ccd67a21ec2bd07d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f18b16637dbdcc969dd33e5f8a43d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f18b16637dbdcc969dd33e5f8a43d\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b52d94765285fdc976358000c6e69535\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b52d94765285fdc976358000c6e69535\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7f386dc6e1c6297614f0be80f0a4395\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7f386dc6e1c6297614f0be80f0a4395\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01334412ef15649e74ec9893d78559a3\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01334412ef15649e74ec9893d78559a3\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431c8829f6f6b27b6455a795856a3344\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431c8829f6f6b27b6455a795856a3344\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46058ee95a895bce0736fb77cdf9c28c\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46058ee95a895bce0736fb77cdf9c28c\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27d8e68d5a57b657f05297cae5e4b495\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27d8e68d5a57b657f05297cae5e4b495\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0845cd2c721677b3fca482ee21726580\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0845cd2c721677b3fca482ee21726580\transformed\animated-gif-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a795cba5141d712ecf2d9ef334380a13\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a795cba5141d712ecf2d9ef334380a13\transformed\webpsupport-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a3b1ef2f426ae2baf11951ed0664\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a3b1ef2f426ae2baf11951ed0664\transformed\fresco-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b8d16a39b9fe0899ce6b7fb81e1173b\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b8d16a39b9fe0899ce6b7fb81e1173b\transformed\imagepipeline-okhttp3-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6edf58fea11e908378920dfe0b347a\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee6edf58fea11e908378920dfe0b347a\transformed\animated-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7293c7f8feff3ceb025bba98dda16bd6\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7293c7f8feff3ceb025bba98dda16bd6\transformed\animated-drawable-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5feedd9d96b30d7a31a16aa723639fa0\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5feedd9d96b30d7a31a16aa723639fa0\transformed\vito-options-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85586c22835c13b9d3a8a27f8f46d86d\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85586c22835c13b9d3a8a27f8f46d86d\transformed\drawee-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5ed67c38d669cd2dff2013f65485a1d\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5ed67c38d669cd2dff2013f65485a1d\transformed\nativeimagefilters-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9bcc3f7d14bfc7d1a5a550f1d2558c\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e9bcc3f7d14bfc7d1a5a550f1d2558c\transformed\memory-type-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\712f706943b2f9e2bbffae1a75b4d417\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\712f706943b2f9e2bbffae1a75b4d417\transformed\memory-type-java-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83d94a538df045225779dc392165fda6\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83d94a538df045225779dc392165fda6\transformed\imagepipeline-native-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c920b0772d4eca7fb216af9cc41579\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9c920b0772d4eca7fb216af9cc41579\transformed\memory-type-ashmem-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e40087345e8f199a8ab9777cebe4613c\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e40087345e8f199a8ab9777cebe4613c\transformed\imagepipeline-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b57ecf85b5b544ffc5ab3e845063f4\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13b57ecf85b5b544ffc5ab3e845063f4\transformed\nativeimagetranscoder-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cac981f9c047f247d9085a4b79492b5\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cac981f9c047f247d9085a4b79492b5\transformed\imagepipeline-base-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68e616a6cabdf12beeef65edd4b10a1a\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68e616a6cabdf12beeef65edd4b10a1a\transformed\middleware-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5e52fe4de0043982ed2cc46b6f28b7c\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5e52fe4de0043982ed2cc46b6f28b7c\transformed\ui-common-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c8e87f614b82a51eabed9f96ab3b9a6\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c8e87f614b82a51eabed9f96ab3b9a6\transformed\soloader-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a62f1488b51fb180440e91af920c4c7\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a62f1488b51fb180440e91af920c4c7\transformed\fbcore-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a287922a3c2204766df39f0f00cd33f\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a287922a3c2204766df39f0f00cd33f\transformed\facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcfbb00bfb44d8c67b52adbc737fe3f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcfbb00bfb44d8c67b52adbc737fe3f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\435430fbaf443eda8529ebdd0e3c7bf1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\435430fbaf443eda8529ebdd0e3c7bf1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5146738535d3021a7116f39c42fe567a\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5146738535d3021a7116f39c42fe567a\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc4b7785b7f60e7f8449e110fc2cc723\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc4b7785b7f60e7f8449e110fc2cc723\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f927aff154629f3c8ace576ee7d80\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f927aff154629f3c8ace576ee7d80\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba320662f2878e53556aae079acc84fd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba320662f2878e53556aae079acc84fd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d62d9514e9e9cb330919eb1b00be688f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d62d9514e9e9cb330919eb1b00be688f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dccb93d324f2aeeafe661d4bbb7df67\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9dccb93d324f2aeeafe661d4bbb7df67\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6f5913789ae904936625fb3c2a66956\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6f5913789ae904936625fb3c2a66956\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\338ba257c5fa7113414a02a100375ed8\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\338ba257c5fa7113414a02a100375ed8\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b0f59e36d37fb7c46f9729b83779f2\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b0f59e36d37fb7c46f9729b83779f2\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a469c90230d0dbbff23d489378f153f4\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a469c90230d0dbbff23d489378f153f4\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8360839840b029eae401d3de045f15\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8360839840b029eae401d3de045f15\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c170203461d3e4e0d521b3019b09b9d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c170203461d3e4e0d521b3019b09b9d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56871df614f2f5c9a79b4b54c9edca50\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56871df614f2f5c9a79b4b54c9edca50\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15214162fcd85290223d3ecfb84d2ee5\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15214162fcd85290223d3ecfb84d2ee5\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaa3f7797a90b961a6d492a0de03cfa6\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaa3f7797a90b961a6d492a0de03cfa6\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c2e74f5ebae07930583f59e1060ea8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c2e74f5ebae07930583f59e1060ea8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b99ebfa5cff4c3c38777aaa7b2191b\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b99ebfa5cff4c3c38777aaa7b2191b\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f00ce2e2b7e2c6c6fc4e719bf2b702c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f00ce2e2b7e2c6c6fc4e719bf2b702c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f59f02b7bbd1aabcc70c251eded0cec\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f59f02b7bbd1aabcc70c251eded0cec\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\996e6ac5dce730a58e974de0c4fd1f63\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\996e6ac5dce730a58e974de0c4fd1f63\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47a9c898aa0eb32234d150f2eadf2db\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47a9c898aa0eb32234d150f2eadf2db\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32934037b033d108e264c0811eca2dd2\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32934037b033d108e264c0811eca2dd2\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d3f0a98accf3c3f3da3a8731edebae\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d3f0a98accf3c3f3da3a8731edebae\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0765ed377bf3d44ecfda4906552fc54\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0765ed377bf3d44ecfda4906552fc54\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e072103a8b3d480b477db02d208f061\transformed\lifecycle-runtime-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e072103a8b3d480b477db02d208f061\transformed\lifecycle-runtime-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f97a256d1c5f7c498ac1fcc5354ff1c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f97a256d1c5f7c498ac1fcc5354ff1c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5c522a3f043374cf9fb8c527af84193\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5c522a3f043374cf9fb8c527af84193\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3079fc4a3a067f116fdc198af709606\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3079fc4a3a067f116fdc198af709606\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04bd76be230be3afda015e7c0cd50c4f\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04bd76be230be3afda015e7c0cd50c4f\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3722047ea58d3e32ab834186a09a4add\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3722047ea58d3e32ab834186a09a4add\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\135d45547a57ba4ef6d73102fe49363e\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\135d45547a57ba4ef6d73102fe49363e\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4de29c54e3244094408eb217e4741f9c\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4de29c54e3244094408eb217e4741f9c\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f290a05a084430f6a0c046fad5a13d64\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f290a05a084430f6a0c046fad5a13d64\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d0bcc65bed66d2181e2a2293229def3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d0bcc65bed66d2181e2a2293229def3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739cc41763d5deb2d5267b2bd7911623\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739cc41763d5deb2d5267b2bd7911623\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\941a9e15e034d8a2189a15fca62a413d\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\941a9e15e034d8a2189a15fca62a413d\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\964575875962add97bbfbf58ca5ad772\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\964575875962add97bbfbf58ca5ad772\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\868bac4eefab012de29ba5d141b7a6e4\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\868bac4eefab012de29ba5d141b7a6e4\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05a74691b8cd1b38d4395dfe50a0b4ee\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05a74691b8cd1b38d4395dfe50a0b4ee\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\797455f429f09da3ef1b9ec5ccd1f4cb\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\797455f429f09da3ef1b9ec5ccd1f4cb\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:6:5-8:98
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:6:5-8:98
MERGED from [io.sentry:sentry-android-replay:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52709b16bfe4b5af8f5a5eabe0c766ee\transformed\sentry-android-replay-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52709b16bfe4b5af8f5a5eabe0c766ee\transformed\sentry-android-replay-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79daa22662937dd899049551008c5871\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79daa22662937dd899049551008c5871\transformed\vito-renderer-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86fe3428fb49648b88cd1b5b200fc124\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86fe3428fb49648b88cd1b5b200fc124\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a130c94486d708f4697639d7d8d14415\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a130c94486d708f4697639d7d8d14415\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee8dc5aa0b1ccacdd316db7adb3cde2e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee8dc5aa0b1ccacdd316db7adb3cde2e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3354bc65abbd27a7ac207ece4b46bdb9\transformed\hermes-android-0.76.9-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3354bc65abbd27a7ac207ece4b46bdb9\transformed\hermes-android-0.76.9-debug\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8aa82df4ddf8339506f4afc921b6b4\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8aa82df4ddf8339506f4afc921b6b4\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71f4855643d6ce49683684f629eb05c0\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71f4855643d6ce49683684f629eb05c0\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\447637e3c436fc6104bb179c28646f95\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\447637e3c436fc6104bb179c28646f95\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8177085846dba10b910889f894527d2f\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8177085846dba10b910889f894527d2f\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc7d22c5cacfd3c89bcbe1bdd26188de\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc7d22c5cacfd3c89bcbe1bdd26188de\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18712e53c24b49c1a495a3627a9be715\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18712e53c24b49c1a495a3627a9be715\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\692110d5710e4745679c61385a3c3853\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\692110d5710e4745679c61385a3c3853\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71950d6bc8f566841daa9a357bf3c243\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71950d6bc8f566841daa9a357bf3c243\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7737405b45068d9ec56d8a3fa674d4d\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7737405b45068d9ec56d8a3fa674d4d\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d12fa09022a714b4d137f5bc4014f79\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d12fa09022a714b4d137f5bc4014f79\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe67c9bee19cce2d0871fa0ea7ea074\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbe67c9bee19cce2d0871fa0ea7ea074\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e60968fb9a48511e10a89487d8faf8e4\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e60968fb9a48511e10a89487d8faf8e4\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9d0681bba8d2adc0d2049b30c6fe280\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9d0681bba8d2adc0d2049b30c6fe280\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f47940e7fe986e5236d3501a1436f189\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f47940e7fe986e5236d3501a1436f189\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1372a0db8479314fc56da48b388483ad\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1372a0db8479314fc56da48b388483ad\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\231cba9907b2e796ad2bd58bd045de17\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\231cba9907b2e796ad2bd58bd045de17\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2b3d6d162c714a5b6cd4dcbceb22d64\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2b3d6d162c714a5b6cd4dcbceb22d64\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f425149ea7ed6954b26e5d901a6cd4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09f425149ea7ed6954b26e5d901a6cd4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071b2577bf84f70c4a8c067e6f7b12\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14071b2577bf84f70c4a8c067e6f7b12\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1199f669f3e81f19e89663e4ebda2c6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1199f669f3e81f19e89663e4ebda2c6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6841415a811a852755cb4235ca8d54a6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6841415a811a852755cb4235ca8d54a6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071cc2099c9bf7e7d24e7fdbb394e138\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071cc2099c9bf7e7d24e7fdbb394e138\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.mixpanel.android:mixpanel-android:8.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a98342615b1e27151878f9b27c1c8028\transformed\mixpanel-android-8.0.3\AndroidManifest.xml:5:5-44
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3cbfd02ec95e6afb105434963506110\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3cbfd02ec95e6afb105434963506110\transformed\fbjni-0.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b7b7964f47632f9df96ec8f517abe7d\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b7b7964f47632f9df96ec8f517abe7d\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:8:5-10:41
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:8:5-10:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51cb6e99fc37e441ccaa921889720566\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
MERGED from [org.aomedia.avif.android:avif:1.0.1.262e11d] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51cb6e99fc37e441ccaa921889720566\transformed\avif-1.0.1.262e11d\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7219e558874300ce5364390263e13a10\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7219e558874300ce5364390263e13a10\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5ee820f4e7da6e30cce364b067c95a5\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:react-native-appsflyer] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-79
MERGED from [:expo-image] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8360839840b029eae401d3de045f15\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8360839840b029eae401d3de045f15\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:27:5-79
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e60968fb9a48511e10a89487d8faf8e4\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e60968fb9a48511e10a89487d8faf8e4\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:13:5-79
MERGED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:13:5-79
	android:name
		ADDED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
	android:name
		ADDED from [:react-native-community_netinfo] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
meta-data#io.sentry.auto-init
ADDED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:37
	android:value
		ADDED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-34
	android:name
		ADDED from [:sentry_react-native] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-47:48
	android:resource
		ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-55
	tools:replace
		ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-45
	android:name
		ADDED from [:react-native-fbsdk-next] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-65
intent#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:application/octet-stream
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
action#android.intent.action.SEND
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
	android:name
		ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-62
intent#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:text/x-vcard
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:18
intent#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:text/vcard
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-28:18
intent#action:name:android.intent.action.EDIT
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:9-31:18
action#android.intent.action.EDIT
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-65
	android:name
		ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:21-62
intent#action:name:android.intent.action.INSERT
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:18
action#android.intent.action.INSERT
ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-67
	android:name
		ADDED from [:expo-contacts] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:21-64
package#host.exp.exponent
ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-73
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-24:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-80
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-40:19
	android:enabled
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-37
	tools:ignore
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-40
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-35:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:17-94
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:36
	android:value
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:17-33
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:9-44:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-56
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-47
	android:authorities
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:13-75
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:47:13-89
service#expo.modules.location.services.LocationTaskService
ADDED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [:expo-location] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-75
	android:name
		ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-75
	android:name
		ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-90
	android:name
		ADDED from [:expo-media-library] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-87
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [:expo-notifications] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-92
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-14:18
provider#expo.modules.sharing.SharingFileProvider
ADDED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-26:20
	android:grantUriPermissions
		ADDED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-47
	android:authorities
		ADDED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-71
	android:exported
		ADDED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [:expo-sharing] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
intent#action:name:android.intent.action.SENDTO+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:sms
ADDED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-23:18
action#android.intent.action.SENDTO
ADDED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-67
	android:name
		ADDED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:21-64
intent#action:name:android.intent.action.SENDTO+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:smsto
ADDED from [:expo-sms] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-31:18
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:expo-web-browser] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2438200ebd883d34752ad287f0df74\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:9-55
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:16:9-55
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:16:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:16:18-52
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:24:19-68
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.scoopt.inpress+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
meta-data#com.bumptech.glide.integration.okhttp3.OkHttpGlideModule
ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
	android:value
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
	android:name
		ADDED from [com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34e8bbc3fd57a3f642d1d6074e547d33\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
category#android.intent.category.OPENABLE
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:13-73
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:13:23-70
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
receiver#com.amazon.device.iap.ResponseReceiver
ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:8:9-15:20
	android:exported
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:10:13-36
	android:permission
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:11:13-79
	android:name
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:9:13-66
intent-filter#action:name:com.amazon.inapp.purchasing.NOTIFY
ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:12:13-14:29
action#com.amazon.inapp.purchasing.NOTIFY
ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:13:17-77
	android:name
		ADDED from [com.revenuecat.purchases:purchases-store-amazon:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c004bba3e5b580a4e60c09164aa5aa1\transformed\purchases-store-amazon-7.12.0\AndroidManifest.xml:13:25-74
activity#com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity
ADDED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:10:9-13:75
	android:configChanges
		ADDED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:12:13-96
	android:theme
		ADDED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [com.revenuecat.purchases:purchases:7.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\AndroidManifest.xml:11:13-97
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:10:22-64
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:14:21-88
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:19:9-21:37
	android:value
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:21:13-34
	android:name
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:20:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:23:9-27:75
	android:exported
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:26:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:25:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:24:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:28:9-32:75
	android:exported
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:31:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:30:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:32:13-72
	android:name
		ADDED from [com.android.billingclient:billing:6.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\AndroidManifest.xml:29:13-80
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0737aeb54bf4be80863d191c87a2c5a0\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47210d99e9c0d3ae1020ae9d764c948\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f1d4b533ded76a536565bb26a3c636\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1d9fa620b7c9590134162ecb8d2c812\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f41c6db1355c526b14745a41c757aa3\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9af7d63893180d00ebdd32b5fe996274\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#io.sentry.android.core.SentryInitProvider
ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:12:9-15:40
	android:authorities
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:14:13-70
	android:exported
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:15:13-37
	android:name
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:13:13-69
provider#io.sentry.android.core.SentryPerformanceProvider
ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:16:9-20:39
	android:authorities
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:18:13-77
	android:exported
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:19:13-37
	android:initOrder
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:20:13-36
	android:name
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\AndroidManifest.xml:17:13-76
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:5-88
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:25:5-88
MERGED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:25:5-88
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:16:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:5-82
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:17:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:5-92
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:18:22-89
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:5-83
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:19:22-80
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:34:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:33:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:38:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:41:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:45:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\AndroidManifest.xml:48:25-100
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.scoopt.inpress.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.scoopt.inpress.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
intent#action:name:com.appsflyer.referrer.INSTALL_PROVIDER
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:11:9-13:18
action#com.appsflyer.referrer.INSTALL_PROVIDER
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:12:13-78
	android:name
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:12:21-75
package#com.instagram.android
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:19:9-57
	android:name
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:19:18-54
package#com.facebook.lite
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:22:9-53
	android:name
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:22:18-50
uses-permission#com.samsung.android.mapsagent.permission.READ_APP_INFO
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:31:5-94
	android:name
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:31:22-91
package#com.samsung.android.mapsagent
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:34:9-65
	android:name
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:34:18-62
uses-permission#com.huawei.appmarket.service.commondata.permission.GET_COMMON_DATA
ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:38:5-106
	android:name
		ADDED from [com.appsflyer:af-android-sdk:6.16.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\AndroidManifest.xml:38:22-103
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7a12a76e838f49eb9439b8b5e8637d5\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\286afa4e021c994106d45dd8aa2cf8aa\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b4d253663dc89f32d1fd95e8e2eadea\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55c18959cc0e68e77f5854d6489e64d3\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\265090a567ffe9bb3232686035122218\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:5-98
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:22-95
uses-permission#com.google.android.gms.permission.ACTIVITY_RECOGNITION
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:5-94
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:22-91
service#io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:20:13-132
service#io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:22:9-24:40
	android:exported
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:24:13-37
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:23:13-127
service#io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService
ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:25:9-27:40
	android:exported
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\555b3e009e1d1f54e2fd3790b3ce36f7\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:26:13-120
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b2de9c850d1b0e5f28a2813235e69c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
