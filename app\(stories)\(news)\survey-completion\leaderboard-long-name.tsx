import { Screen } from "@/components/Themed"
import _ from "lodash"
import { Leaderboard } from "@/components/ratings/Leaderboard"
import { LEADERBOARD_USERS } from "./leaderboard"
import { faker } from "@faker-js/faker"

export default function Story() {
  const offset = _.random(10000, 15000)
  const users = LEADERBOARD_USERS.map((user, index) => ({
    ...user,
    firstName: `${faker.person.firstName()}-${faker.person.middleName()}`,
    points: user.points * 100 + offset,
  }))

  return (
    <Screen>
      <Leaderboard users={users} currentUserId={users[1].id} />
    </Screen>
  )
}
