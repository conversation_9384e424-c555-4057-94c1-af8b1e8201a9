import { useEffect, useState, useRef } from "react"
import { View, StyleSheet } from "react-native"
import Geocoder from "react-native-geocoding"
import { getUserPosition as defaultGetUserPosition } from "@/app/hooks/useLocation"
import { IMAGES } from "@/constants/Images"
import {
  defaultLatitudeDelta,
  MapHeight,
  WindowWidth,
  animateCamera,
  getAddressData,
  requestLocationPermission,
  showPermissionsAlert,
  defaultLongitudeDelta,
} from "@/utils/location"
import MapView, {
  Details,
  LatLng,
  PROVIDER_GOOGLE,
  Region,
} from "react-native-maps"
import { APIKEY } from "@/constants/Key"
import { Button } from "./Button"
import { LiteModeLocationMarker } from "./maps/LiteModeLocationMarker"
import { LocationMarker } from "./maps/LocationMarker"

Geocoder.init(APIKEY)

interface MapProps {
  defaultLocation: LatLng
  defaultDeltas?: { latitudeDelta: number; longitudeDelta: number }
  liteMode?: boolean
  getUserPosition?: () => Promise<LatLng>
  onLocationChange?: (values: LatLng) => void
}

const Map = ({
  defaultLocation,
  defaultDeltas = {
    latitudeDelta: defaultLatitudeDelta,
    longitudeDelta: defaultLongitudeDelta,
  },
  liteMode = false,
  getUserPosition = defaultGetUserPosition,
  onLocationChange,
}: MapProps) => {
  const [region, setRegion] = useState<Region>({
    ...defaultLocation,
    ...defaultDeltas,
  })
  const mapView = useRef<MapView | null>(null)
  const [address, setAddress] = useState("Loading...")

  useEffect(() => {
    updateAddress(defaultLocation)
    setRegion({
      ...defaultLocation,
      latitudeDelta: region.latitudeDelta,
      longitudeDelta: region.longitudeDelta,
    })
  }, [defaultLocation])

  const updateAddress = async (location: LatLng) => {
    const address = await getAddressData(location)
    setAddress(address)
  }

  const getLocationStatus = async (): Promise<void> => {
    const result = await requestLocationPermission()
    if (result === "granted") {
      getCurrentLocation()
    }
  }

  const getCurrentLocation = async () => {
    let position = null
    try {
      position = await getUserPosition()
    } catch (error) {
      showPermissionsAlert({ onConfirm: getLocationStatus })
      return
    }

    if (!position) return

    onLocationChange && onLocationChange(position)
    setRegion({
      ...position,
      latitudeDelta: defaultLatitudeDelta,
      longitudeDelta: defaultLongitudeDelta,
    })
    updateAddress(position)
    setTimeout(() => {
      return animateCamera(mapView.current, position)
    }, 0)
  }

  const CurrentLocationButton = () => (
    <View style={styles.locationButtonContainer}>
      <Button
        style={styles.locationButton}
        textStyle={styles.buttonText}
        text="Go to current location"
        iconSource={IMAGES.Location}
        iconTintColor="white"
        onPress={() => getCurrentLocation()}
      />
    </View>
  )

  const onRegionChangeComplete = (region: Region, details: Details) => {
    if (details.isGesture) {
      onLocationChange && onLocationChange(region)
      setRegion(region)
      updateAddress({ latitude: region.latitude, longitude: region.longitude })
    }
  }

  return (
    <View>
      <View style={liteMode ? styles.litemodeContainer : styles.container}>
        <MapView
          provider={PROVIDER_GOOGLE}
          toolbarEnabled={false}
          ref={mapView}
          liteMode={liteMode}
          onRegionChangeComplete={onRegionChangeComplete}
          style={styles.map}
          region={region}
        >
          {liteMode && (
            <LiteModeLocationMarker address={address} location={region} />
          )}
        </MapView>
        {!liteMode && <LocationMarker address={address} />}
        {!liteMode && <CurrentLocationButton />}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  container: {
    height: MapHeight,
    width: "100%",
    borderRadius: 30,
    overflow: "hidden",
  },
  litemodeContainer: {
    height: 100,
    width: "100%",
    borderRadius: 10,
    overflow: "hidden",
  },
  noPermissionbutton: {
    justifyContent: "center",
    alignItems: "center",
  },
  noPermissionText: {
    color: "black",
    textAlign: "center",
  },
  mapStyle: {
    width: WindowWidth,
    height: MapHeight,
  },
  locationButtonContainer: {
    position: "absolute",
    bottom: 12,
    alignSelf: "center",
  },
  locationButton: {
    borderRadius: 25,
    width: 300,
    zIndex: 2,
  },
  buttonText: {
    color: "white",
    paddingLeft: 15,
    fontFamily: "InterTight-SemiBold",
  },
})
export default Map
