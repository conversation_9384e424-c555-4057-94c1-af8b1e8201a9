import { View, Text, StyleSheet } from "react-native"
import QRCode from "react-native-qrcode-svg"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { useSession } from "@/ctx"
import { fontStyles } from "@/styles"

function QrCodeView() {
  const { session } = useSession()
  if (!session) return null
  return (
    <View style={styles.container}>
      <Text style={styles.profileName}>{session.user.firstName}</Text>
      <View style={styles.qrCodeContainer}>
        <View style={styles.qrCode}>
          <QRCode
            logo={require("../../assets/images/inpress-icon-white-on-black.png")}
            logoBackgroundColor="white"
            logoMargin={5}
            value={session.user.id.toString()}
            size={wp(50)}
            color="black"
            backgroundColor="white"
          />
        </View>
      </View>
      <Text style={styles.qrSubText}>
        Scan {session.user.firstName}'s code to see which interests you share
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 30,
  },
  profileName: {
    fontSize: 56,
    textAlign: "center",
    ...fontStyles.editorial,
  },
  qrCodeIconStyle: {
    height: "95%",
    width: "95%",
    resizeMode: "contain",
  },
  qrSubText: {
    width: wp(50),
    fontFamily: "InterTight-Regular",
    fontSize: 16,
    textAlign: "center",
  },
  qrCodeContainer: {
    padding: 15,
    borderColor: "black",
    borderWidth: 5,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  qrCode: {
    borderColor: "black",
    borderWidth: 15,
    borderRadius: 5,
  },
})

export default QrCodeView
