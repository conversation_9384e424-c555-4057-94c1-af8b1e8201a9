import { Screen } from "@/components/Themed"
import { articles } from "../feed/news"
import { FullImageArticleLayout } from "@/components/news/FullImageArticleLayout"

export default function Story() {
  return (
    <Screen style={{ width: "100%", paddingHorizontal: 0 }}>
      <FullImageArticleLayout
        articles={articles.concat(articles).map((a, i) => ({
          ...a,
          id: i,
        }))}
      />
    </Screen>
  )
}
