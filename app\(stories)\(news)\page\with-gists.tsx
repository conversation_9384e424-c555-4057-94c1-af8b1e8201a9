import { Screen } from "@/components/Themed"
import { newUpdateAlert } from "@/components/news/alerts"
import { NewsFeedAlert } from "@/components/news/AlertCard"
import { NewsPage_ } from "@/screens/news/NewsPage"
import { setStoryFeatureFlag } from "@/utils/featureFlags"
import { NEWSFEED_PROPS } from "../feed/news"

export const alerts: NewsFeedAlert[] = [newUpdateAlert]

export default function Story() {
  setStoryFeatureFlag("gists", true)
  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsPage_ {...NEWSFEED_PROPS} />
    </Screen>
  )
}
