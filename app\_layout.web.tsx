import { useFonts } from "expo-font"
import { useEffect } from "react"
import * as SplashScreen from "expo-splash-screen"

export default function RootLayout() {
  const [fontsLoaded, fontLoadingError] = useFonts({
    "Inter-Regular": require("../assets/fonts/Inter-Regular.ttf"),
    "Inter-Medium": require("../assets/fonts/Inter-Medium.ttf"),
    "Inter-SemiBold": require("../assets/fonts/Inter-SemiBold.ttf"),
    "InterTight-Regular": require("../assets/fonts/InterTight-Regular.ttf"),
    "InterTight-Medium": require("../assets/fonts/InterTight-Medium.ttf"),
    "InterTight-SemiBold": require("../assets/fonts/InterTight-SemiBold.ttf"),
  })

  useEffect(() => {
    if (fontLoadingError) throw fontLoadingError
  }, [fontLoadingError])

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync()
    }
  }, [fontsLoaded])

  if (!fontsLoaded) {
    return null
  }

  return <></>
}
