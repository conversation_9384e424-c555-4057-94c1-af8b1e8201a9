import { SwipeIndicator } from "@/components/gists/SwipeIndicator"
import { Screen, View } from "@/components/Themed"

export default function Story() {
  return (
    <Screen style={{ width: "100%", gap: 200, flex: 1 }}>
      <View style={{ position: "relative" }}>
        <SwipeIndicator type="like" overlayStyle={{}} />
      </View>
      <View style={{ position: "relative" }}>
        <SwipeIndicator type="dislike" overlayStyle={{}} />
      </View>
    </Screen>
  )
}
