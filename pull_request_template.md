## 🤔 Journaling

_Enter any journaling or notes on your thought process while working on this code. The point is simply to provide a place to put down thoughts without filtering or worrying that it's messy. These can be valuable in understanding the code changes in the future._

Things to journal about:

- What this change is
- Why this change was made
- Any points that are confusing
- Any work planned or needed after this PR
- If this is a bug, any retrospection on how it occurred and how to improve systems accordingly
- Screenshots of any UI changes

<details>
<summary>Screenshots</summary>
  
</details>

## ✅ Checklist

### Pull request structure

- [ ] Pull request is smaller than 300 lines of code changes (break into multiple PRs if larger)
- [ ] Pull request has a clear concise title
- [ ] Journaling filled out (at least write, "nothing to note", in that section)

### Tests

Work was tested with at least one of the following (check at least one):

- [ ] Unit tests
- [ ] Storybook
- [ ] UI tests
- [ ] Scripted manual tests (with script saved to source code)
- [ ] Manual testing (last resort)
- [ ] Tested in all relevant platforms (i.e. Android and iOS)

### Code quality

- [ ] Code refactored and cleaned up

### Deployment

- [ ] Any affected deployed systems have been updated accordingly (or there is plan to update them after review)
- [ ] Feature flag added and tested in both states (if appropriate)
- [ ] Any relevant documentation or setup instructions (e.g. environment variables) have been updated
- [ ] Tracking has been added to monitor this change (if appropriate)
