import { GenericNotificationItem } from "./GenericNotificationItem"
import { HitDensityBonus2Notification } from "@/apiQueries/notificationTypes"
import { NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function HitDensityBonus2NotificationItem({
  item,
}: {
  item: HitDensityBonus2Notification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          25 bonus points unlocked! Your streak of 6 articles today is
          impressive!
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
