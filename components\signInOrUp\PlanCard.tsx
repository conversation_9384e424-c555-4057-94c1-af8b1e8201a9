import { Card, Text } from "react-native-paper"
import { StyleSheet } from "react-native"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { PurchasesPackage } from "react-native-purchases"
import { View } from "../Themed"

export const PlanCard = ({
  onSelectPlan,
  isSelected,
  plan,
  monthlyPlanAmount,
}: {
  plan: PurchasesPackage
  isSelected: boolean
  onSelectPlan: (purchasePackage: PurchasesPackage) => void
  monthlyPlanAmount?: number
}) => {
  const {
    product: { price, subscriptionPeriod },
  } = plan
  const intervalString = subscriptionPeriod === "P1M" ? "/ mo" : "/ yr"
  const title = "$" + price.toFixed(2) + intervalString

  return (
    <Card
      style={{
        ...styles.card,
        borderWidth: isSelected ? 1 : 0,
        backgroundColor: isSelected ? "#E18260" : "white",
      }}
      onPress={() => onSelectPlan(plan)}
    >
      <View style={styles.container}>
        <View
          style={{
            ...styles.label,
            backgroundColor: isSelected ? "black" : "#E0E0E0",
          }}
        >
          <Text
            style={{
              ...styles.labelText,
              color: isSelected ? "white" : "black",
            }}
          >
            {subscriptionPeriod === "P1M" ? "Monthly" : "Yearly"}
          </Text>
        </View>
        <Card.Content style={styles.content}>
          <Text style={styles.title}>{title}</Text>
          {subscriptionPeriod === "P1Y" && (
            <>
              <Text style={styles.subtitle}>
                {`(that's $${(price / 12).toFixed(2)} / month)`}
              </Text>
              {monthlyPlanAmount && (
                <View
                  style={{
                    ...styles.subtitleLabelContainer,
                    backgroundColor: isSelected ? "white" : "#E0E0E0",
                  }}
                >
                  <Text style={styles.subtitleLabel}>
                    {`Save ${(
                      100 -
                      (price * 100) / (monthlyPlanAmount * 12)
                    ).toFixed(0)}%`}
                  </Text>
                </View>
              )}
            </>
          )}
        </Card.Content>
      </View>
    </Card>
  )
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "white",
    height: hp("13%"),
    width: wp("40%"),
    display: "flex",
    justifyContent: "center",
    borderColor: "black",
  },
  container: {
    width: "100%",
    height: "100%",
    position: "relative",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
  },
  content: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 19,
    fontFamily: "InterTight-SemiBold",
    fontWeight: "bold",
    marginBottom: 3,
  },
  subtitle: {
    fontSize: 10.5,
    textAlign: "center",
  },
  subtitleLabelContainer: {
    paddingHorizontal: 5,
    marginTop: 6,
    paddingVertical: 2,
    borderRadius: 3,
  },
  subtitleLabel: {
    fontWeight: "bold",
    fontSize: 10.5,
    textAlign: "center",
    color: "black",
  },
  label: {
    display: "flex",
    justifyContent: "center",
    zIndex: 20,
    position: "absolute",
    borderRadius: 5,
    top: "-13%",
    width: 90,
    alignSelf: "center",
    paddingVertical: 3.5,
  },
  labelText: {
    color: "black",
    fontSize: 15.5,
    fontWeight: "bold",
    textAlign: "center",
  },
})
