import EditProfileScreen, {
  EditProfileScreenProps,
  SaveProps,
} from "@/app/(app)/account/edit-profile"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import TabBar from "@/components/widgets/TabBar"
import { datesModePalette, friendsModePalette } from "@/constants/Colors"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { router, useLocalSearchParams } from "expo-router"
import { useEffect, useState } from "react"
import { Alert, View } from "react-native"
import { ViewProfileScreen, ViewProfileScreenProps } from "./ViewProfileScreen"
import { Loader } from "@/components/widgets/Loader"
import {
  fetchPrompts,
  getCurrentUser,
  updateProfile,
  updateProfileImages,
} from "@/apiQueries/auth"
import { Prompt } from "@/components/signInOrUp/SignUp"
import { useSession } from "@/ctx"
import { isUserWithProfile, UnknownTypeUserWithPrivateData } from "@/types/user"
import { getUserSettings, UserSettings } from "@/apiQueries/userSettings"

type TabTitle = "Edit" | "View"

const ProfileScreen = () => {
  const { session, refreshSession } = useSession()
  const [userBeingEdited, setUserBeingEdited] = useState<
    UnknownTypeUserWithPrivateData | undefined
  >()
  const [userSettings, setUserSettings] = useState<UserSettings>()
  const [promptChoices, setPromptChoices] = useState<Prompt[]>([])
  const params = useLocalSearchParams()
  const connectionMode = params.connectionMode as
    | ActiveConnectionMode
    | undefined

  useEffect(() => {
    if (!session) {
      return
    }

    getCurrentUser({ token: session.token, connectionMode }).then(
      setUserBeingEdited,
    )

    getUserSettings({ token: session.token }).then(setUserSettings)
  }, [])

  useEffect(() => {
    if (connectionMode) fetchPrompts(connectionMode).then(setPromptChoices)
  }, [])

  const handleSave = async ({ user, images }: SaveProps) => {
    if (!session) {
      return
    }

    await updateProfile({
      token: session.token,
      userUpdate: user,
      connectionMode,
    })

    if (images.length > 0) {
      await updateProfileImages({
        token: session.token,
        pics: images,
        connectionMode,
      })
    }

    Alert.alert("Profile updated!")
    refreshSession(session.token)
    router.back()
  }

  if (!session || !userBeingEdited || !userSettings) {
    return <Loader />
  }

  return (
    <ProfileScreen_
      user={userBeingEdited}
      userSettings={userSettings}
      connectionMode={connectionMode}
      initialUser={userBeingEdited}
      promptChoices={promptChoices}
      onSave={handleSave}
    />
  )
}

interface ProfileScreenProps_
  extends EditProfileScreenProps,
    Omit<ViewProfileScreenProps, "user"> {
  user: UnknownTypeUserWithPrivateData
  connectionMode?: ActiveConnectionMode
}

const ProfileScreen_ = ({
  user,
  connectionMode,
  ...editProps
}: ProfileScreenProps_) => {
  const [selectedTab, setSelectedTab] = useState<TabTitle>("Edit")
  const tabs = [
    {
      title: "Edit",
      onPress: () => setSelectedTab("Edit"),
      selected: selectedTab === "Edit",
    },
    {
      title: "View",
      onPress: () => setSelectedTab("View"),
      selected: selectedTab === "View",
    },
  ]

  const backgroundColor =
    connectionMode === ConnectionMode.Dates
      ? datesModePalette.backgroundColor
      : friendsModePalette.backgroundColor

  return (
    <View style={{ flex: 1, backgroundColor }}>
      {isUserWithProfile(user) ? (
        <>
          <TabBar tabs={tabs} />
          {selectedTab === "Edit" ? (
            <EditProfileScreen {...editProps} />
          ) : (
            <ViewProfileScreen user={user} />
          )}
        </>
      ) : (
        <EditProfileScreen {...editProps} />
      )}
    </View>
  )
}

export default ProfileScreen
export { ProfileScreen_ }
