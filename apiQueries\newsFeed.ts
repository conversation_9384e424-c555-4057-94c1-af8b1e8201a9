import { get, post } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { Destination } from "@/components/news/constants"
import { UserImage } from "@/types/user"
import { ExternalPathString } from "expo-router"
import { GistSwipeType } from "@/types/news"

export interface Announcement {
  id: number
  title: string
  imageUrl: string
  url?: ExternalPathString
  inAppDestination?: Destination
}

interface RawAnnouncement
  extends Pick<Announcement, "id" | "title" | "url" | "inAppDestination"> {
  image_url: string
  in_app_destination?: Destination
}

export const getAnnouncements = async ({
  token,
}: {
  token: string
}): Promise<Announcement[]> => {
  const result = await get<null, RawAnnouncement[]>(
    `${INPRESS_API_URL}/announcements`,
    token,
  )

  return result.map((raw) => ({
    ...raw,
    imageUrl: raw.image_url,
    inAppDestination: raw.in_app_destination,
  }))
}

export interface ArticleMatchPreview {
  articleId: number
  matchCount: number
  matchImages: UserImage[]
}

interface RawArticleMatchPreview {
  article_id: ArticleMatchPreview["articleId"]
  match_count: ArticleMatchPreview["matchCount"]
  match_images: ArticleMatchPreview["matchImages"]
}

export const getMatchMoods = async ({
  token,
}: {
  token: string
}): Promise<ArticleMatchPreview[]> => {
  const result = await get<null, RawArticleMatchPreview[]>(
    `${INPRESS_API_URL}/match-moods`,
    token,
  )

  return result.map((raw) => ({
    articleId: raw.article_id,
    matchCount: raw.match_count,
    matchImages: raw.match_images,
  }))
}

type GistRating = {
  articleId: number
  rating: GistSwipeType
}

export type SubmitGistRatingProps = {
  token: string
} & GistRating

export const submitGistRating = async ({
  token,
  articleId,
  rating,
}: SubmitGistRatingProps): Promise<void> => {
  type Params = { article_id: number; rating: GistSwipeType }
  await post<Params, void>(
    `${INPRESS_API_URL}/gist-rating`,
    { article_id: articleId, rating },
    token,
  )
}
