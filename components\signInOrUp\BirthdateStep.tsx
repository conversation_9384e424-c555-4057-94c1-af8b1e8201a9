import moment from "moment"
import { useRef } from "react"
import { View, StyleSheet, TextInput, Text } from "react-native"

export const isAcceptableBirthdate = (birthdate: string) => {
  const regex = /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/
  if (!regex.test(birthdate)) return false

  const date = moment(birthdate, "YYYY-MM-DD")
  const today = moment()
  const age = today.diff(date, "years")
  return age >= 18
}

type BirthdateStepProps = {
  birthdate?: string
  onChange: (value: string) => void
}

export default function BirthdateStep({
  birthdate = "",
  onChange,
}: BirthdateStepProps) {
  const [year = "", month = "", day = ""] = birthdate.split("-") as [
    string,
    string,
    string,
  ]

  const dayRef = useRef<TextInput>(null)
  const yearRef = useRef<TextInput>(null)

  const update = (m: string, d: string, y: string) => {
    onChange(`${y}-${m}-${d}`)
  }

  return (
    <View style={styles.inputRow}>
      <TextInput
        style={styles.input}
        placeholder="MM"
        keyboardType="number-pad"
        maxLength={2}
        value={month}
        onChangeText={(month) => {
          update(month, day, year)
          if (month.length === 2) dayRef.current?.focus()
        }}
      />
      <Text style={styles.slash}>/</Text>
      <TextInput
        ref={dayRef}
        style={styles.input}
        placeholder="DD"
        keyboardType="number-pad"
        maxLength={2}
        value={day}
        onChangeText={(day) => {
          update(month, day, year)
          if (day.length === 2) yearRef.current?.focus()
        }}
      />
      <Text style={styles.slash}>/</Text>
      <TextInput
        ref={yearRef}
        style={styles.inputYear}
        placeholder="YYYY"
        keyboardType="number-pad"
        maxLength={4}
        value={year}
        onChangeText={(year) => {
          update(month, day, year)
        }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  inputRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  input: {
    width: 50,
    fontSize: 24,
    borderBottomWidth: 1,
    textAlign: "center",
    marginHorizontal: 4,
  },
  inputYear: {
    width: 80,
    fontSize: 24,
    borderBottomWidth: 1,
    textAlign: "center",
    marginLeft: 4,
  },
  slash: {
    fontSize: 24,
    marginTop: 2,
  },
})
