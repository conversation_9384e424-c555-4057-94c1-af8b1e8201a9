import { BEI<PERSON>, SANDSTONE } from "@/constants/Colors"
import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import { Image } from "expo-image"
import { fontStyles } from "@/styles"

interface StatsCardProps {
  imageUri?: string
  icon?: React.ReactNode
  title: string
  subtitle: string
}

const StatsCard = ({ imageUri, icon, title, subtitle }: StatsCardProps) => {
  return (
    <View style={styles.card}>
      {imageUri ? (
        <Image source={imageUri} style={styles.mediumBadgeStyle} />
      ) : (
        icon
      )}

      <View style={styles.cardTextContainer}>
        <Text style={styles.cardTitleText}>{title}</Text>
        <Text style={styles.cardSubtitleText}>{subtitle}</Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: "black",
    width: "48%",
    borderRadius: 22,
    height: 165,
    alignItems: "center",
    justifyContent: "center",
  },
  mediumBadgeStyle: {
    width: 48,
    height: 56,
    resizeMode: "contain",
  },
  cardTextContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: 7,
    gap: 4,
  },
  cardTitleText: {
    color: BEIGE,
    fontSize: 28,
    ...fontStyles.editorial,
  },
  cardSubtitleText: {
    color: SANDSTONE,
    fontSize: 10,
    fontFamily: "InterTight-SemiBold",
  },
})

export default StatsCard
