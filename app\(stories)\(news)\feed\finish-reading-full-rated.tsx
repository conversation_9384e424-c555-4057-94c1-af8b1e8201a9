import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_ARTICLES, NEWSFEED_PROPS } from "./news"
import _ from "lodash"
import { useNewsContext } from "@/context/NewsContext"
import { useEffect } from "react"

export default function Story() {
  const { setArticles } = useNewsContext()

  useEffect(() => {
    setArticles(
      NEWSFEED_ARTICLES.map((article) => ({
        ...article,
        isOpened: true,
        isSurveyed: true,
        gistRating: "like",
      })),
    )
  }, [setArticles])

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} />
    </Screen>
  )
}
