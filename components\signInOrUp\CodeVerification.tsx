import { TextInput } from "../TextInput"
import { useState } from "react"
import { Button } from "../Button"
import { StyleSheet, Text, TouchableOpacity, View } from "react-native"
import Checkbox from "expo-checkbox"

interface CodeVerificationProps {
  initialEmail?: string
  initialCode?: string
  onNext: ({
    email,
    code,
    codeType,
    isBypassCode,
  }: {
    email: string
    code?: string
    codeType?: string
    isBypassCode?: boolean
  }) => void
}

const CodeVerification = ({
  initialEmail,
  initialCode,
  onNext,
}: CodeVerificationProps) => {
  const [email, setEmail] = useState(initialEmail ?? "")
  const [showingCodeInput, setShowingCodeInput] = useState(!!initialCode)
  const [code, setCode] = useState(initialCode ?? "")

  const handleContinue = () => {
    onNext({ email, code: showingCodeInput ? code : undefined })
  }

  return (
    <View style={styles.container}>
      <TextInput
        label="Email"
        keyboardType="email-address"
        autoComplete="email"
        autoCapitalize="none"
        value={email}
        onChangeText={setEmail}
      />
      <TouchableOpacity
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 10,
          marginVertical: 15,
          marginBottom: 20,
        }}
        onPress={() => setShowingCodeInput(!showingCodeInput)}
      >
        <Checkbox
          value={showingCodeInput}
          onValueChange={setShowingCodeInput}
        />
        <Text>I have an access code (not required)</Text>
      </TouchableOpacity>
      {showingCodeInput && (
        <TextInput
          label="Access code"
          placeholder="Enter your code"
          autoComplete="off"
          autoCapitalize="none"
          value={code}
          onChangeText={setCode}
        />
      )}
      <Button
        text="Continue"
        onPress={handleContinue}
        disabled={
          !email || email.indexOf("@") === -1 || (showingCodeInput && !code)
        }
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    rowGap: 10,
  },
  linkText: {
    textAlign: "center",
  },
  link: {
    textAlign: "center",
    textDecorationLine: "underline",
  },
})

export default CodeVerification
