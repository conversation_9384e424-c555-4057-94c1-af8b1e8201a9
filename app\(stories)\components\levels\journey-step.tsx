import React from "react"
import { Screen } from "@/components/Themed"
import { View, ScrollView, Text } from "react-native"
import JourneyStep from "@/components/levels/JourneyStep"
import { LEVELS } from "../badge"

export default function Story() {
  return (
    <ScrollView>
      <Screen style={{ gap: 0 }}>
        <Text>No level yet (in progress)</Text>
        <JourneyStep level={LEVELS[0]} points={10} />
        <View style={{ height: 10 }} />

        <Text>Reached first level (completed)</Text>
        <JourneyStep level={LEVELS[0]} points={40} />
        <View style={{ height: 10 }} />

        <Text>Working on second level (in progress)</Text>
        <JourneyStep level={LEVELS[1]} points={40} previousLevel={LEVELS[0]} />
        <View style={{ height: 10 }} />

        <Text>Level minimum not reached (not started)</Text>
        <JourneyStep level={LEVELS[2]} points={50} previousLevel={LEVELS[1]} />
      </Screen>
    </ScrollView>
  )
}
