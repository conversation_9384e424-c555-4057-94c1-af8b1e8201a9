import { StyleSheet } from "react-native"
import { useLevels } from "@/context/LevelContext"
import { CelebrateStep } from "./CelebrateStep"
import { shareLevelUp } from "@/utils/sharing"

type LevelUpProps = {
  points: number
}

export const LevelUpStep = ({ points }: LevelUpProps) => {
  const { loading, calculateLevel } = useLevels()

  if (loading) return null

  const level = calculateLevel(points)!

  const handleShare = async () => {
    await shareLevelUp(level)
  }

  return (
    <CelebrateStep
      imageUrl={level.badgeUrl}
      title={level.levelUpTitle}
      subtitle={level.levelUpSubtitle}
      handleShare={handleShare}
    />
  )
}

const styles = StyleSheet.create({
  badge: {
    width: 70,
    height: 82,
  },
  subtitle: {
    fontFamily: "Inter-Regular",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 38,
  },
})
