import { abbreviateNumber } from "@/utils/numberAbbreviation"

describe("Number abbreviation", () => {
  it("abbreviates numbers", () => {
    const num = 1_480
    const result = abbreviateNumber(num)
    expect(result).toBe("1.4k")
  })
  it("does not abbreviate small numbers", () => {
    const num = 480
    const result = abbreviateNumber(num)
    expect(result).toBe("480")
  })
  it("abbreviates large numbers", () => {
    const num = 56_210
    const result = abbreviateNumber(num)
    expect(result).toBe("56.2k")
  })
  it("does not include zeros after the decimal point", () => {
    const num = 1_000
    const result = abbreviateNumber(num)
    expect(result).toBe("1k")
  })
  it("does nothing with zero", () => {
    const num = 0
    const result = abbreviateNumber(num)
    expect(result).toBe("0")
  })
})
