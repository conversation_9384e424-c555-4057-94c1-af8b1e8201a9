<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>InPress</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
    <key>CFBundleShortVersionString</key>
    <string>235.0.0</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>myapp</string>
          <string>com.scoopt.inpress</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>exp+inpress-expo-router</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>fb437278972477723</string>
        </array>
      </dict>
    </array>
    <key>CFBundleVersion</key>
    <string>235</string>
    <key>FacebookAdvertiserIDCollectionEnabled</key>
    <true/>
    <key>FacebookAppID</key>
    <string>437278972477723</string>
    <key>FacebookAutoInitEnabled</key>
    <false/>
    <key>FacebookAutoLogAppEventsEnabled</key>
    <true/>
    <key>FacebookClientToken</key>
    <string>********************************</string>
    <key>FacebookDisplayName</key>
    <string>InPress</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    <key>LSApplicationQueriesSchemes</key>
    <array>
      <string>fbapi</string>
      <string>fb-messenger-api</string>
      <string>fbauth2</string>
      <string>fbshareextension</string>
    </array>
    <key>LSMinimumSystemVersion</key>
    <string>12.0</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
      <key>NSAllowsArbitraryLoads</key>
      <false/>
      <key>NSAllowsLocalNetworking</key>
      <true/>
    </dict>
    <key>NSCameraUsageDescription</key>
    <string>InPress uses your camera for taking pictures for your profile.</string>
    <key>NSContactsUsageDescription</key>
    <string>InPress uses your contacts to allow for contact syncing.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>InPress uses your location to deliver local news and match you with people in your area.</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>InPress uses your location to deliver local news and match you with people in your area.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>InPress uses your location to deliver local news and match you with people in your area.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>InPress uses your microphone for recording audio in chats.</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>InPress uses the photo library to allow you to select pictures for your profile.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>InPress uses the photo library to allow you to select pictures for your profile.</string>
    <key>NSUserActivityTypes</key>
    <array>
      <string>$(PRODUCT_BUNDLE_IDENTIFIER).expo.index_route</string>
    </array>
    <key>NSUserTrackingUsageDescription</key>
    <string>InPress uses tracking data to determine how users found the app.</string>
    <key>SKAdNetworkItems</key>
    <array>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <array>
          <string>cstr6suwn9.skadnetwork</string>
        </array>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>v9wttpbfk9.skadnetwork</string>
      </dict>
      <dict>
        <key>SKAdNetworkIdentifier</key>
        <string>n38lu8286q.skadnetwork</string>
      </dict>
    </array>
    <key>UIAppFonts</key>
    <array>
      <string>PPEditorialOld-Regular.otf</string>
      <string>PPEditorialOld-Italic.otf</string>
      <string>InterTight-Regular.ttf</string>
      <string>InterTight-SemiBold.ttf</string>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>SplashScreen</string>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
    </array>
    <key>UIRequiresFullScreen</key>
    <false/>
    <key>UIStatusBarStyle</key>
    <string>UIStatusBarStyleDefault</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UIUserInterfaceStyle</key>
    <string>Automatic</string>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
  </dict>
</plist>