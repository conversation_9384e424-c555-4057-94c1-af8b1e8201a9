import { TouchableOpacity, View, StyleSheet } from "react-native"
import StatItem from "./StatItem" // adjust path as needed
import { IMAGES } from "@/constants/Images"
import { useLevels } from "@/context/LevelContext"
import { calculateProgress } from "@/utils/levels"
import _ from "lodash"
import { DARK_GREY } from "@/constants/Colors"
import { useFocusEffect } from "expo-router"
import { useCallback } from "react"
import { router } from "expo-router"
import { trackEvent } from "@/utils/tracking"
import { NewsEventType } from "@/types/news"
import Color from "color"
import { LevelIcon } from "@/components/levels/LevelIcon"
import { useNewsNavContext } from "@/context/NewsNavContext"

const StatsBar = () => {
  const { loading, stats, refreshStats, calculateLevel, calculateNextLevel } =
    useLevels()
  const { activeTab } = useNewsNavContext()

  useFocusEffect(
    useCallback(() => {
      refreshStats()
    }, []),
  )

  const handlePress = () => {
    trackEvent(NewsEventType.StatsBarTapped, {
      data: { active_tab: activeTab },
    })
    router.push("/account/journey")
  }

  if (loading)
    return (
      <View key={1} style={styles.container}>
        <StatItem icon={IMAGES.Fire} value={0} marker="d" />
        <StatItem icon={IMAGES.Brain} value={0} />
        <StatItem icon={IMAGES.Leaf} value={0} marker="%" diffMarker="%" />
      </View>
    )

  if (!stats) return null
  const { points } = stats
  const currentLevel = calculateLevel(points)
  const nextLevel = calculateNextLevel(points)
  const progress = calculateProgress({ points, currentLevel, nextLevel })

  const levelToUse = nextLevel || currentLevel
  if (!levelToUse) return null
  const levelColor = Color(levelToUse.color)
  const levelIconColor = levelColor.darken(0.1).hex()
  return (
    <TouchableOpacity
      key={2}
      style={styles.container}
      activeOpacity={0.5}
      onPress={handlePress}
    >
      <StatItem icon={IMAGES.Fire} value={stats?.streakDays || 0} marker="d" />
      <StatItem icon={IMAGES.Brain} value={points} />
      <StatItem
        icon={() => <LevelIcon level={levelToUse!} fill={levelIconColor} />}
        value={_.round(progress * 100, 0)}
        marker="%"
        diffMarker="%"
      />
    </TouchableOpacity>
  )
}

export default StatsBar

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: DARK_GREY,
    borderRadius: 12,
    height: 40,
    marginTop: 20,
    marginHorizontal: 24,
  },
})
