import { StyleSheet, View } from "react-native"
import { Text } from "../Themed"
import { SvgProps } from "react-native-svg"
import { screenSubtitleStyle } from "./styles"
import { HeaderText } from "../StyledText"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"

export interface ScreenHeaderProps {
  iconComponent?: (props: SvgProps) => JSX.Element
  title: string | JSX.Element
  subtitle?: string | JSX.Element
}

export const ScreenHeader = ({
  iconComponent,
  title,
  subtitle,
}: ScreenHeaderProps) => {
  return (
    <View>
      {iconComponent?.({ style: styles.icon })}
      <HeaderText
        style={{
          paddingTop: iconComponent ? hp("3%") : hp("5%"),
        }}
      >
        {title}
      </HeaderText>
      {subtitle &&
        (typeof subtitle === "string" ? (
          <Text style={styles.subtitle}>{subtitle}</Text>
        ) : (
          subtitle
        ))}
    </View>
  )
}

const styles = StyleSheet.create({
  icon: {
    marginTop: 24,
    width: 32,
    height: 32,
  },
  subtitle: screenSubtitleStyle,
})
