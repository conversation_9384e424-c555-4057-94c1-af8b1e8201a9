import { Article, NewsEventType } from "@/types/news"
import { LinearGradient } from "expo-linear-gradient"
import { AppState, AppStateStatus } from "react-native"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import {
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
} from "react-native"
import { Button } from "../Button"
import { ProgressBar } from "react-native-paper"
import { openArticle } from "../ArticlePage"
import { DARK_GREY } from "@/constants/Colors"
import { trackEvent } from "@/utils/tracking"
import ReportArticleModal from "./ReportAiSummaryModal"
import { sectionGroups } from "../news/constants"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { TAB_HEIGHT } from "@/app/(app)/_layout"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import LottieView from "lottie-react-native"
import { useFocusEffect } from "expo-router"
import { useNewsContext } from "@/context/NewsContext"
import { OtherEventTypes } from "@/types/other"
import { BOTTOM_TEXT_HEIGHT } from "../signInOrUp/GistsStep"
import { NAV_HEIGHT, SCREEN_PADDING } from "../signInOrUp/SignUpWrapper"

type GistCardProps = {
  article: Article
  isVisible: boolean
  demoMode?: boolean
  onImageLoaded?: () => void
  onTimerCompleted: () => void
}

export const GistCard = ({
  article,
  isVisible = true,
  demoMode = false,
  onImageLoaded,
  onTimerCompleted,
}: GistCardProps) => {
  const { swipeHintShown, setSwipeHintShown, gistIsSwiping } = useNewsContext()

  const { id, title, source, faviconUrl, imageUrl, summaryPoints } = article
  const [progress, setProgress] = useState(0)
  const [articleIsOpen, setArticleIsOpen] = useState(false)
  const [reportModalIsOpen, setReportModalIsOpen] = useState<boolean>(false)
  const [lostFocus, setLostFocus] = useState(false)

  const timerPaused = useMemo(() => {
    return (
      !isVisible ||
      gistIsSwiping ||
      articleIsOpen ||
      lostFocus ||
      reportModalIsOpen ||
      demoMode
    )
  }, [
    isVisible,
    gistIsSwiping,
    articleIsOpen,
    lostFocus,
    reportModalIsOpen,
    demoMode,
  ])

  const group = useMemo(
    () =>
      sectionGroups.find((g) =>
        g.sections.some((s) => s.name === article.frontpageSection),
      )!,
    [],
  )!

  const accumulatedProgressRef = useRef<number>(0)
  const animationTimer = useRef<NodeJS.Timeout | null>(null)

  const textColorStyle = { color: group.textColor }

  const handleOpenArticle = () => {
    setArticleIsOpen(true)
    trackEvent(NewsEventType.ArticleOpenedFromGist, {
      data: { article_id: id },
    })
    openArticle(article)
  }

  useEffect(() => {
    if (!isVisible || demoMode) return

    setTimeout(() => {
      setSwipeHintShown(true)
    }, 4000)

    trackEvent(NewsEventType.GistViewed, {
      data: { article_id: id },
    })

    return () => {
      trackEvent(NewsEventType.GistUnmounted, {
        data: { article_id: id },
      })
    }
  }, [isVisible])

  useEffect(() => {
    if (timerPaused) return
    const refreshRateMs = 15
    const duration = 15000

    let startTime = Date.now()

    const progressPadding = accumulatedProgressRef.current

    const updateProgress = () => {
      const elapsedTime = Date.now() - startTime
      const newProgress = Math.min(elapsedTime / duration, 1)
      const totalProgress = newProgress + progressPadding
      setProgress(totalProgress)
      accumulatedProgressRef.current = totalProgress

      if (totalProgress < 1) {
        animationTimer.current = setTimeout(updateProgress, refreshRateMs)
      } else {
        onTimerCompleted()
      }
    }

    animationTimer.current = setTimeout(updateProgress, refreshRateMs)

    return () => {
      if (animationTimer.current) {
        clearTimeout(animationTimer.current)
      }
    }
  }, [isVisible, timerPaused])

  const handleLostFocus = useCallback(() => {
    setLostFocus(true)
    if (animationTimer.current) {
      clearTimeout(animationTimer.current)
    }
  }, [])

  const handleRestoreFocus = useCallback(() => {
    setLostFocus(false)
    setArticleIsOpen(false)
    setProgress(0)
    accumulatedProgressRef.current = 0
  }, [])

  useFocusEffect(
    useCallback(() => {
      handleRestoreFocus()

      return () => {
        handleLostFocus()
      }
    }, []),
  )

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === "inactive" || nextAppState === "background") {
        handleLostFocus()
      } else if (nextAppState === "active") {
        handleRestoreFocus()
      }
    }
    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange,
    )
    return () => {
      subscription.remove()
    }
  }, [])

  const renderSummaryItem = (text: string, index: number) => {
    const bulletColorStyle = { backgroundColor: group.textColor }
    return (
      <View style={styles.bulletContainer} key={index}>
        <View style={[styles.bulletPoint, bulletColorStyle]} />
        <Text style={[styles.bulletText, textColorStyle]} numberOfLines={2}>
          {text}
        </Text>
      </View>
    )
  }

  const { bottom: safeBottom } = useSafeAreaInsets()

  const paddingBottom = useMemo(() => {
    const basePadding = safeBottom + hp("2%")
    if (demoMode) {
      const buffer = hp("2%")
      return (
        buffer + BOTTOM_TEXT_HEIGHT + NAV_HEIGHT + SCREEN_PADDING + basePadding
      )
    } else return TAB_HEIGHT + basePadding
  }, [demoMode, safeBottom])

  return (
    <View style={[styles.container, { paddingBottom }]}>
      <ImageBackground
        source={demoMode ? article.localImage : { uri: imageUrl }}
        style={[
          styles.imageBackground,
          { backgroundColor: group.backgroundColor },
        ]}
        onLoad={onImageLoaded}
        onError={() =>
          trackEvent(OtherEventTypes.ImageLoadFailed, {
            data: { article_id: id, image_url: imageUrl, source: "GistCard" },
          })
        }
      >
        <LinearGradient
          colors={["transparent", "rgba(0, 0, 0, 0.4)"]}
          style={{ width: "100%" }}
          start={{ x: 1, y: 1 }}
          end={{ x: 1, y: 0.1 }}
        >
          <View style={styles.topSection}>
            <ProgressBar
              style={[styles.progressBar, { width: `${progress * 100}%` }]}
              color="white"
              animatedValue={progress}
            />
            <View style={styles.header}>
              <View style={styles.sourceContainer}>
                <Image source={{ uri: faviconUrl }} style={styles.favicon} />
                <Text style={styles.sourceText}>{source}</Text>
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                disabled={demoMode}
                onPress={() => setReportModalIsOpen(true)}
              >
                <Image
                  source={require("../../assets/images/red-flag.png")}
                  style={styles.flagIcon}
                />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>

        <LinearGradient
          colors={["#A4571300", group.backgroundColor, group.backgroundColor]}
          locations={[0, 0.4, 1]}
          style={styles.gradientContainer}
          start={{ x: 1, y: 0.1 }}
          end={{ x: 1, y: 1 }}
        >
          <Text style={styles.titleText}>{title}</Text>
          <View style={styles.summaryContainer}>
            <Text style={[styles.summaryText, textColorStyle]}>
              {"AI Summary"}
            </Text>
            {summaryPoints ? (
              summaryPoints.map(renderSummaryItem)
            ) : (
              <Text style={styles.summaryText}>No summary available</Text>
            )}
          </View>
          {!demoMode ? (
            <Button
              style={styles.readButton}
              text="Read full article"
              onPress={handleOpenArticle}
              textStyle={styles.readButtonText}
            />
          ) : (
            <View style={{ height: 10 }} />
          )}
        </LinearGradient>
      </ImageBackground>
      {!swipeHintShown && (
        <View style={styles.overlay}>
          <LottieView
            source={require("../../assets/animation/swipe-gesture.json")}
            autoPlay
            style={styles.animation}
          />
        </View>
      )}
      <ReportArticleModal
        article={article}
        visible={reportModalIsOpen}
        onClose={() => setReportModalIsOpen(false)}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
  },
  imageBackground: {
    flex: 1,
    alignItems: "center",
    borderRadius: 15,
    overflow: "hidden",
  },
  topSection: {
    width: "100%",
    flexDirection: "column",
    gap: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  progressBar: {
    backgroundColor: `${DARK_GREY}20`,
    height: 2,
    borderRadius: 10,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  sourceContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  favicon: {
    width: 16,
    height: 16,
    backgroundColor: "#FFF",
    borderWidth: 0.5,
    borderColor: "#E6E6E6",
    borderRadius: 2.5,
    overflow: "hidden",
  },
  sourceText: {
    color: "#FFF",
    fontFamily: "InterTight-Regular",
    fontSize: 12,
  },
  flagIcon: {
    width: 20,
    height: 20,
  },
  gradientContainer: {
    flex: 1,
    width: "100%",
    justifyContent: "flex-end",
    paddingVertical: 15,
    paddingHorizontal: 16,
  },
  titleText: {
    fontSize: 24,
    fontFamily: "InterTight-SemiBold",
    color: "white",
  },
  summaryContainer: {
    justifyContent: "flex-end",
    gap: 16,
  },
  summaryText: {
    fontSize: 16,
    fontFamily: "InterTight-Medium",
    color: "white",
    marginTop: 12,
  },
  bulletContainer: {
    fontFamily: "Inter-Regular",
    flexDirection: "row",
    gap: 10,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 100,
    backgroundColor: "white",
    marginTop: 6,
  },
  bulletText: {
    color: "white",
    fontFamily: "Inter-SemiBold",
    fontSize: 16,
    flex: 1,
  },
  readButton: {
    width: "100%",
    backgroundColor: "white",
    paddingVertical: 12.5,
    height: 44,
    marginTop: 24,
  },
  readButtonText: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 16,
    color: DARK_GREY,
  },
  overlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "transparent",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999,
  },
  animation: {
    width: 200,
    height: 200,
    marginBottom: 20,
  },
})
