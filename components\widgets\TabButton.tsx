import { TouchableOpacity, Text, StyleSheet } from "react-native"

export const TabButton = ({
  title,
  isDisabled,
  onPress,
  ...props
}: {
  title: string
  isDisabled?: boolean
  onPress: () => void
} & React.ComponentProps<typeof TouchableOpacity>) => (
  <TouchableOpacity
    hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
    onPress={onPress}
    disabled={isDisabled}
    {...props}
  >
    <Text style={[styles.tabButton, isDisabled && { opacity: 0.5 }]}>
      {title}
    </Text>
  </TouchableOpacity>
)

const styles = StyleSheet.create({
  tabButton: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 14,
  },
})
