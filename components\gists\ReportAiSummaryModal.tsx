import React, { useCallback, useRef, useState } from "react"
import { FlatList, StyleSheet, Text, View } from "react-native"

import { AntDesign, Ionicons } from "@expo/vector-icons"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"

import { Modal } from "../Modal"
import { Button } from "../Button"
import { TextInput } from "../TextInput"
import {
  DARK_GREY,
  GREY,
  LIGHT_GREY,
  MEDIUM_GREY,
  RED,
  WHITE,
} from "@/constants/Colors"
import { HeaderText, SubheaderText } from "../StyledText"
import { useNewsContext } from "@/context/NewsContext"
import { useSession } from "@/ctx"
import { Article } from "@/types/news"

type Props = {
  article: Article
  visible: boolean
  onClose: () => void
  demoMode?: boolean
}

const ReportArticleModal = ({ article, visible, onClose, demoMode = false }: Props) => {
  const { session } = useSession()

  // Use mock values in demo mode, real context otherwise
  const newsContext = demoMode ? null : useNewsContext()
  const reportArticle = demoMode ? async () => {} : newsContext!.reportArticle

  const flatListRef = useRef<FlatList>(null)

  const [description, setDescription] = useState("")

  enum Page {
    Form = 0,
    Success = 1,
  }

  const scrollToPage = (index: Page) => {
    if (flatListRef?.current) {
      flatListRef?.current?.scrollToIndex({
        index,
        animated: true,
      })
    }
  }

  const handleSubmit = async () => {
    reportArticle({
      token: session!.token,
      reason: "AI Summary Inaccurate",
      note: description,
      articleId: article.id,
    })
    scrollToPage(Page.Success)
  }

  const renderFormPage = useCallback(() => {
    return (
      <View style={styles.formPage}>
        <Ionicons
          name="chevron-back-circle"
          color={LIGHT_GREY}
          size={hp(3.4)}
          style={styles.backIcon}
          onPress={onClose}
        />
        <View style={styles.formPageInnerView}>
          <HeaderText style={styles.title}>
            Is this AI Summary inaccurate?
          </HeaderText>
          <SubheaderText style={styles.subtitle}>
            Add more details to help us improve.
          </SubheaderText>

          <TextInput
            value={description}
            placeholder="Type here..."
            multiline
            inputStyle={styles.textInput}
            inputContainerStyle={styles.textInputContainer}
            textAlignVertical="top"
            onChangeText={(text) => setDescription(text)}
          />
        </View>
        <Button
          text={"Report Article"}
          style={styles.reportButton}
          disabled={description?.length <= 0}
          onPress={handleSubmit}
        />
      </View>
    )
  }, [description])

  const renderSuccessPage = () => {
    return (
      <View style={styles.successPage}>
        <View style={styles.descriptionContainer}>
          <HeaderText style={styles.title}>
            Your report was received.
          </HeaderText>
          <SubheaderText style={styles.subtitle}>
            Your report will be reviewed internally. Thanks for helping us
            improve!
          </SubheaderText>
        </View>
        <Button
          text={"Back to Gists"}
          style={styles.backToGistButton}
          onPress={onClose}
        />
      </View>
    )
  }

  const pages = [renderFormPage, renderSuccessPage]

  const renderItem = useCallback(
    ({ item, index }: { item: () => JSX.Element; index: number }) => (
      <View style={styles.itemContainer} key={index}>
        {item()}
      </View>
    ),
    [],
  )

  return (
    <Modal
      visible={visible}
      modalProps={{ onRequestClose: onClose, animationType: "fade" }}
      containerStyle={styles.modalContainer}
    >
      <FlatList
        ref={flatListRef}
        data={pages}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
      />
    </Modal>
  )
}

export default ReportArticleModal

const styles = StyleSheet.create({
  descriptionContainer: {
    paddingHorizontal: wp(4),
    marginTop: hp(1.5),
  },
  title: {
    fontSize: 32,
    lineHeight: 50,
    color: DARK_GREY,
  },
  subtitle: {
    color: MEDIUM_GREY,
    fontSize: 14,
  },
  reportButton: {
    backgroundColor: RED,
    width: wp(80),
  },

  formPage: {
    alignItems: "center",
    paddingHorizontal: wp(5),
  },
  backIcon: {
    alignSelf: "flex-start",
  },
  formPageInnerView: {
    marginVertical: 32,
  },
  textInput: {
    height: hp(20.3),
    paddingHorizontal: 18,
    paddingVertical: 16,
  },
  textInputContainer: {
    borderColor: GREY,
    marginTop: 24,
  },

  successPage: {
    paddingHorizontal: wp(5),
  },
  backToGistButton: {
    width: wp(80),
    marginTop: 32,
  },

  itemContainer: {
    width: wp(90),
    marginHorizontal: wp(5),
    paddingVertical: wp(6),
    alignItems: "center",
    backgroundColor: WHITE,
    alignSelf: "center",
    borderWidth: 1,
    borderColor: LIGHT_GREY,
    borderRadius: 15,
  },
  modalContainer: {
    padding: 0,
    width: wp(100),
    backgroundColor: "transparent",
    zIndex: 9999999,
  },
})
