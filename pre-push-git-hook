#!/bin/bash
protected_branches=( "main" "master" "dev" "develop" "branch_for_debugging_this_script" )
current_branch=$(git branch | grep \* | cut -d ' ' -f2)

for protected_branch in "${protected_branches[@]}"
do
    :
    if [ $protected_branch = $current_branch ]  
    then  
        read -p "You're about to push to $protected_branch, is that what you intended? [y|n] " -n 1 -r < /dev/tty
        echo
        if echo $REPLY | grep -E '^[Yy]$' > /dev/null
        then
            echo "Okie dokie, will push to $protected_branch"
            : # do nothing
        else
            echo "Push aborted"
            exit 1 # push will not execute
        fi
    fi  

done
if npx tsc ; then
    echo "TypeScript checking passed, pushing!"
else
    read -p "Tests failed, do you still want to push? [y|n]" -n 1 -r < /dev/tty
    if echo $REPLY | grep -E '^[Yy]$' > /dev/null
    then
        echo "Ok, pushing without tests"
    else
        echo "Go fix those tests!"
        echo "Push aborted"
        exit 1 # push will not execute
    fi
fi
exit 0 # push will execute

