import { useState } from "react"
import { ProfileItem, ProfileList } from "./ProfileItems"
import { Switch } from "react-native-paper"
import { BROWNSTONE } from "@/constants/Colors"

export default function SettingsSwitch({
  title,
  subtitle,
  initialValue,
  onChange,
}: {
  title: string
  subtitle?: string
  initialValue: boolean
  onChange: (value: boolean) => Promise<void | boolean>
}) {
  const [enabled, setEnabled] = useState(initialValue)

  const handleChange = async (newValue: boolean) => {
    const originalValue = enabled
    setEnabled(newValue)
    const succeeded = await onChange(newValue)
    if (succeeded !== undefined && !succeeded) {
      setEnabled(originalValue)
    }
  }

  const settingsItem: ProfileItem = {
    title,
    endComponent: (
      <Switch
        color={BROWNSTONE}
        value={enabled}
        onValueChange={(value) => handleChange(value)}
      />
    ),
    subtitle,
  }

  return <ProfileList items={[settingsItem]} />
}
