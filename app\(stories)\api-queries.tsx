import { login } from "@/apiQueries/auth"
import { getGeoAreaStatus } from "@/apiQueries/geoAreas"
import { registerWithReferralSystem } from "@/apiQueries/inviteCode"
import { getLeaderboard } from "@/apiQueries/levels"
import { Screen } from "@/components/Themed"
import { combineTopicFields, convertRawLead, RawLead } from "@/types/social"
import { RawUser, Session } from "@/types/user"
import _ from "lodash"
import { useEffect, useState } from "react"
import { ScrollView, Text } from "react-native"
import { Button } from "react-native-paper"
import { USER } from "./(account)/account"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { getLeads, getMatches } from "@/apiQueries/apiQueries"
import { getScannedMatch } from "@/apiQueries/scannedMatch"

const topics = ["Test Topic 1", "Test Topic 2"]

const topicsWithRoots = [
  { name: "Test Topic 1", root_id: 123 },
  { name: "Test Topic 2", root_id: 456 },
]

const rawUser: RawUser = {
  ...USER,
  id: 1,
  first_name: "Test",
  last_name: "User",
  scoop_responses: [],
  connection_mode: ConnectionMode.Dates,
  dates_mode_is_activated: true,
  friends_mode_is_activated: false,
  settings: undefined,
  level: undefined,
  preferences: {
    genders: [],
    min_age: 0,
    max_age: 0,
    min_similarity: 0,
    max_similarity: 0,
  },
  is_news_only: false,
  is_generated: false,
  is_archived: false,
  created_at: "2023-01-01T00:00:00Z",
}

const rawLead: RawLead = {
  id: 1,
  score: 10,
  topics: JSON.stringify(topics),
  topics_with_roots: JSON.stringify(topicsWithRoots),
  user: rawUser,
}

export default function Story() {
  const randomNumber = _.random(0, 100000)
  const email = `alex+${randomNumber}@inpress.app`
  const [session, setSession] = useState<Session>()
  const [result, setResult] = useState<any>()

  const loadSession = async () => {
    const session = await login({
      email: "<EMAIL>",
      password: "a",
    })

    setSession(session)
  }

  useEffect(() => {
    loadSession()
  }, [])

  const updateResult = (result: any) => {
    setResult(JSON.stringify(result))
  }

  const run = async (func: () => Promise<any>) => {
    setResult("")
    try {
      const result = await func()
      console.log("Function result:", JSON.stringify(result))
      updateResult(result)
    } catch (e) {
      console.error("Error running function:", e)
      updateResult(e)
    }
  }

  const registerWithInvalidCode = async () =>
    registerWithReferralSystem({
      email,
      invitationCode: "123",
    })

  const registerWithValidCode = async () =>
    registerWithReferralSystem({
      email,
      invitationCode: "alextesting",
    })

  const registerWithInvalidUserCode = async () =>
    registerWithReferralSystem({
      email,
      referralCode: "abcdef",
    })

  const registerWithValidUserCode = async () =>
    registerWithReferralSystem({
      email,
      referralCode: "wOJ1K",
    })

  const getLeaderboard_ = async () => getLeaderboard({ token: session!.token })

  const tests = [
    {
      name: "Get leads",
      fn: async () =>
        getLeads({
          token: session!.token,
          connectionMode: ConnectionMode.Dates,
        }),
    },
    {
      name: "Get scanned match",
      fn: async () =>
        getScannedMatch({
          token: session!.token,
          otherUserId: 8,
        }),
    },
    {
      name: "Get matches",
      fn: async () =>
        getMatches({
          token: session!.token,
          connectionMode: ConnectionMode.Dates,
        }),
    },
    {
      name: "Convert raw lead without root IDs",
      fn: async () => combineTopicFields(topics, null),
    },
    {
      name: "Convert raw lead with root IDs",
      fn: async () => combineTopicFields(topics, topicsWithRoots),
    },
    {
      name: "Convert raw lead",
      fn: async () => convertRawLead(rawLead),
    },
    {
      name: "Get low activity area status",
      fn: async () =>
        getGeoAreaStatus({ latitude: 40.7549, longitude: -73.9845 }),
    },
    {
      name: "Get high activity area status",
      fn: async () =>
        getGeoAreaStatus({ latitude: 37.7749, longitude: -122.4194 }),
    },
    {
      name: "Register with invalid invitation code",
      fn: registerWithInvalidCode,
    },
    {
      name: "Register with valid invitation code",
      fn: registerWithValidCode,
    },
    {
      name: "Register with invalid user code",
      fn: registerWithInvalidUserCode,
    },
    {
      name: "Register with valid user code",
      fn: registerWithValidUserCode,
    },
    {
      name: "Get leaderboard",
      fn: getLeaderboard_,
    },
  ]

  return (
    <Screen>
      <ScrollView style={{ maxHeight: 500 }}>
        <Text>Using new email for registering: {email}</Text>
        {session && (
          <Text>
            Logged in as {session.user.firstName} ({session.user.id})
          </Text>
        )}
        {tests.map((test) => (
          <Button
            key={test.name}
            mode="contained"
            onPress={() => run(test.fn)}
            style={{ marginVertical: 5 }}
          >
            {test.name}
          </Button>
        ))}
      </ScrollView>
      <ScrollView>
        <Text>Result:</Text>
        <Text>{result}</Text>
      </ScrollView>
    </Screen>
  )
}
