import { login } from "@/apiQueries/auth"
import { getGeoAreaStatus } from "@/apiQueries/geoAreas"
import { registerWithReferralSystem } from "@/apiQueries/inviteCode"
import { getLeaderboard } from "@/apiQueries/levels"
import { Screen } from "@/components/Themed"
import { Session } from "@/types/user"
import _ from "lodash"
import { useEffect, useState } from "react"
import { Text } from "react-native"
import { Button } from "react-native-paper"

export default function Story() {
  const randomNumber = _.random(0, 100000)
  const email = `alex+${randomNumber}@inpress.app`
  const [session, setSession] = useState<Session>()
  const [result, setResult] = useState<any>()

  const loadSession = async () => {
    const session = await login({
      email: "<EMAIL>",
      password: "a",
    })

    setSession(session)
  }

  useEffect(() => {
    loadSession()
  }, [])

  const updateResult = (result: any) => {
    setResult(JSON.stringify(result))
  }

  const run = async (func: () => Promise<any>) => {
    setResult("")
    try {
      const result = await func()
      updateResult(result)
    } catch (e) {
      updateResult(e)
    }
  }

  const registerWithInvalidCode = async () =>
    registerWithReferralSystem({
      email,
      invitationCode: "123",
    })

  const registerWithValidCode = async () =>
    registerWithReferralSystem({
      email,
      invitationCode: "alextesting",
    })

  const registerWithInvalidUserCode = async () =>
    registerWithReferralSystem({
      email,
      referralCode: "abcdef",
    })

  const registerWithValidUserCode = async () =>
    registerWithReferralSystem({
      email,
      referralCode: "wOJ1K",
    })

  const getLeaderboard_ = async () => getLeaderboard({ token: session!.token })

  const tests = [
    {
      name: "Get low activity area status",
      fn: async () =>
        getGeoAreaStatus({ latitude: 40.7549, longitude: -73.9845 }),
    },
    {
      name: "Get high activity area status",
      fn: async () =>
        getGeoAreaStatus({ latitude: 37.7749, longitude: -122.4194 }),
    },
    {
      name: "Register with invalid invitation code",
      fn: registerWithInvalidCode,
    },
    {
      name: "Register with valid invitation code",
      fn: registerWithValidCode,
    },
    {
      name: "Register with invalid user code",
      fn: registerWithInvalidUserCode,
    },
    {
      name: "Register with valid user code",
      fn: registerWithValidUserCode,
    },
    {
      name: "Get leaderboard",
      fn: getLeaderboard_,
    },
  ]

  return (
    <Screen>
      <Text>Using new email for registering: {email}</Text>
      {session && (
        <Text>
          Logged in as {session.user.firstName} ({session.user.id})
        </Text>
      )}
      {tests.map((test) => (
        <Button
          key={test.name}
          mode="contained"
          onPress={() => run(test.fn)}
          style={{ marginVertical: 5 }}
        >
          {test.name}
        </Button>
      ))}
      <Text>Result:</Text>
      <Text>{result}</Text>
    </Screen>
  )
}
