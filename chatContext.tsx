import React, { useEffect, useRef, useState } from "react"
import { Channel, Thread } from "stream-chat"
import { useSession } from "./ctx"
import { StreamChat, Event, DefaultGenerics } from "stream-chat"
import { getStreamChatToken } from "./apiQueries/apiQueries"
import _ from "lodash"
import { Session } from "./types/user"

const API_KEY = process.env.EXPO_PUBLIC_STREAM_API_KEY

export const initializeChat = async ({
  session,
  handleEvent,
}: {
  session: Session
  handleEvent: (event: Event<DefaultGenerics>) => void
}): Promise<StreamChat<DefaultGenerics>> => {
  const { user } = session
  console.log("Setting up chat client")

  if (!API_KEY) {
    throw new Error("Missing Stream API key")
  }

  console.log("Connecting user to stream chat")
  const chatClient = StreamChat.getInstance(API_KEY)
  const chatToken = await getStreamChatToken(session.token)

  if (!chatClient.userID) {
    console.log("User not connected to stream chat, connecting")
    await chatClient
      .connectUser(
        {
          id: user.id.toString(),
        },
        chatToken,
      )
      .catch((e) => {
        console.error("Error connecting user to stream chat", e)
      })
  } else {
    console.log("User already connected to stream chat")
  }
  chatClient.on(handleEvent)
  console.log("User connected to stream chat")
  return chatClient
}

interface ContextProps {
  channel: Channel | null
  setChannel: (channel: Channel | null) => void
  client: StreamChat<DefaultGenerics> | null
  clientIsConnected: boolean
  thread: Thread | null
  setThread: (thread: Thread) => void
  refreshNeeded: boolean
  setRefreshNeeded: (refreshNeeded: boolean) => void
  triggerInitializeChat: () => void
  disconnectClient: () => void
  unreadCountPerChannel: Record<string, number>
  resetChannelUnreadCount: (channel: Channel) => void
}

const contextProps: ContextProps = {
  channel: null,
  setChannel: (channel: Channel | null) => {},
  client: null,
  clientIsConnected: false,
  thread: null,
  setThread: (thread: Thread) => {},
  refreshNeeded: false,
  setRefreshNeeded: (refreshNeeded: boolean) => {},
  triggerInitializeChat: () => {},
  disconnectClient: () => {},
  unreadCountPerChannel: {},
  resetChannelUnreadCount: (channel: Channel) => {},
}

export const ChatContext = React.createContext(contextProps)

export const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  const [channel, setChannel] = useState<Channel | null>(null)
  const [client, setClient] = useState<StreamChat<DefaultGenerics> | null>(null)
  const [clientIsConnected, setClientIsConnected] = useState(false)
  const [thread, setThread] = useState<Thread | null>(null)
  const [refreshNeeded, setRefreshNeeded] = useState(false)
  const [unreadCountPerChannel, setUnreadCountPerChannel] = useState<
    Record<string, number>
  >({})
  const { session } = useSession()
  const sessionLoaded = useRef(false)

  const triggerInitializeChat = () => {
    setClientIsConnected(false)
    initializeChat({
      session: session!,
      handleEvent: (event: Event) => {
        if (event.type === "notification.added_to_channel") {
          setRefreshNeeded(true)
        }
        const channelId = event?.channel_id?.toString()
        if (
          (event.type === "message.new" ||
            event.type === "notification.message_new") &&
          channelId
        ) {
          setUnreadCountPerChannel((prev) => ({
            ...prev,
            [channelId]: (prev[channelId] || 0) + 1,
          }))
        }
      },
    })
      .then((client) => {
        setClient(client)
        setClientIsConnected(true)
      })
      .catch((e) => {
        console.error("Error initializing chat", e)
      })
  }

  const disconnectClient = () => {
    client && client.disconnectUser()
    setClientIsConnected(false)
  }

  const resetChannelUnreadCount = async (channel: Channel) => {
    setChannel(channel)

    try {
      const hasUnreadMessages = _.values(unreadCountPerChannel).some(
        (c) => c > 0,
      )
      const channelId = channel?.id?.toString()
      if (hasUnreadMessages && channelId) {
        setUnreadCountPerChannel((prev) => ({
          ...prev,
          [channelId]: 0,
        }))
      }
    } catch (error) {
      console.error("Error marking channel as read:", error)
    }
  }

  useEffect(() => {
    if (!session || sessionLoaded.current) return
    sessionLoaded.current = true

    triggerInitializeChat()

    return () => {
      disconnectClient()
    }
  }, [session])

  return (
    <ChatContext.Provider
      value={{
        channel,
        setChannel,
        client,
        clientIsConnected,
        thread,
        setThread,
        refreshNeeded,
        setRefreshNeeded,
        triggerInitializeChat,
        disconnectClient,
        unreadCountPerChannel,
        resetChannelUnreadCount,
      }}
    >
      {children}
    </ChatContext.Provider>
  )
}

export const useChatContext = () => React.useContext(ChatContext)
