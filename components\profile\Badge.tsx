import { useMemo } from "react"
import { StyleSheet, Text, View, TouchableOpacity } from "react-native"
import { useLevels } from "@/context/LevelContext"
import { ICON_SIZE } from "@/constants/Numbers"
import _ from "lodash"
import { LinearGradient } from "expo-linear-gradient"
import RibbonBadge from "../levels/RibbonBadge"
import LockIcon from "../icons/levels/LockIcon"
import { abbreviateNumber } from "@/utils/numberAbbreviation"
import { router } from "expo-router"
import { Image } from "expo-image"

interface BadgeProps {
  points: number
  variant: "badge" | "icon" | "points"
}

const Badge = ({ points, variant = "icon" }: BadgeProps) => {
  const { loading, calculateLevel, calculateNextLevel } = useLevels()
  const level = calculateLevel(points)
  const nextLevel = calculateNextLevel(points)
  const levelStyles = useStyles(level?.color || "#ccc")

  if (loading) return null

  if (variant === "badge") {
    return <RibbonBadge level={level} showLabel={true} />
  }

  if (variant === "icon") {
    if (!level) return null

    return (
      <View style={levelStyles.container}>
        <Image source={{ uri: level.iconUrl }} style={levelStyles.iconImage} />
        <Text style={levelStyles.text}>{level.name}</Text>
      </View>
    )
  }

  if (variant === "points") {
    const pointsRequired = nextLevel?.pointsRequired
    const iconUrl = level?.iconUrl

    return (
      <View style={{ flexDirection: "row" }}>
        <LinearGradient
          colors={["rgba(255,255,255,255)", "rgba(255,255,255,0)"]}
          style={levelStyles.leftGradient}
          start={{ x: 0.5, y: 0.5 }}
          end={{ x: 0, y: 0.5 }}
        />
        <View style={levelStyles.container}>
          <TouchableOpacity
            onPress={() => router.push("/account/journey")}
            style={levelStyles.pointsTouchable}
            activeOpacity={1}
          >
            {!_.isNil(iconUrl) ? (
              <Image source={{ uri: iconUrl }} style={levelStyles.iconImage} />
            ) : (
              <LockIcon />
            )}
            {!_.isNil(pointsRequired) ? (
              <Text style={[levelStyles.text, levelStyles.boldPoints]}>
                {abbreviateNumber(points)}/{abbreviateNumber(pointsRequired)}
              </Text>
            ) : (
              <Text style={[levelStyles.text, levelStyles.boldPoints]}>
                {abbreviateNumber(points)}
              </Text>
            )}
          </TouchableOpacity>
        </View>
        <View style={levelStyles.rightCover} />
      </View>
    )
  }
}

export const useStyles = (color: string) => {
  const sharedStyle = StyleSheet.create({
    innerContainer: {
      flexDirection: "row",
      gap: 4,
      alignItems: "center",
    },
  })

  return useMemo(
    () =>
      StyleSheet.create({
        container: {
          backgroundColor: color,
          borderRadius: 20,
          paddingHorizontal: 10,
          paddingVertical: 5,
          zIndex: 11,
          ...sharedStyle.innerContainer,
        },
        pointsTouchable: sharedStyle.innerContainer,
        progressOuterContainer: {
          flexDirection: "row",
          justifyContent: "center",
        },
        progressContainer: {
          paddingVertical: 8,
        },
        nextLevelTextContainer: {
          position: "absolute",
          top: -18,
        },
        nextLevelText: {
          fontSize: 12,
        },
        leftGradient: {
          position: "absolute",
          pointerEvents: "none",
          height: "100%",
          width: 50,
          left: -35,
          zIndex: 10,
        },
        rightCover: {
          position: "absolute",
          zIndex: 10,
          right: -100,
          width: 130,
          height: "100%",
          backgroundColor: "white",
        },
        text: {
          color: "#000",
          fontSize: 12,
        },
        boldPoints: {
          fontFamily: "InterTight-SemiBold",
        },
        progressPoints: {
          fontSize: 15,
        },
        iconImage: {
          width: ICON_SIZE,
          height: ICON_SIZE,
          resizeMode: "contain",
        },
      }),
    [color],
  )
}

export default Badge
