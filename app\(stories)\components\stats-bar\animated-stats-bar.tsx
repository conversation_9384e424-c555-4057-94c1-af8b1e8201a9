import { Screen } from "@/components/Themed"
import StatsBar from "@/components/news/statsBar/StatsBar"
import { useLevels } from "@/context/LevelContext"
import _ from "lodash"
import { useEffect, useState } from "react"
import { Button } from "react-native"

export default function Story() {
  const { stats, setStats, calculateNextLevel } = useLevels()

  const zeroOutStats = () => {
    setStats({
      points: 0,
      streakDays: 0,
      articlesRead: 0,
      articlesRated: 0,
    })
  }

  const incrementStreakDays = () => {
    setStats((prev) => {
      return {
        ...prev!,
        streakDays: prev!.streakDays + 1,
      }
    })
  }

  const incrementPoints = () => {
    setStats((prev) => ({ ...prev!, points: prev!.points + 2 }))
  }

  const incrementStreakAndPoints = () => {
    incrementStreakDays()
    incrementPoints()
  }

  const incrementByMany = () => {
    incrementStreakDays()
    setStats((prev) => ({
      ...prev!,
      points: prev!.points + _.random(5, 40),
    }))
  }

  const getFirstLevel = () => {
    setStats({
      points: 28,
      streakDays: 1,
      articlesRead: 0,
      articlesRated: 0,
    })
    setTimeout(() => {
      setStats({
        points: 30,
        streakDays: 1,
        articlesRead: 0,
        articlesRated: 0,
      })
    }, 3000)
  }

  const setToNextLevel = () => {
    const nextLevel = calculateNextLevel(stats!.points)
    if (nextLevel) {
      setStats({
        points: nextLevel.pointsRequired,
        streakDays: 1,
        articlesRead: 0,
        articlesRated: 0,
      })
    }
  }

  const setToLastLevel = () => {
    setStats({
      points: 11050,
      streakDays: _.random(100, 300),
      articlesRead: 1000,
      articlesRated: 1000,
    })
  }

  const setToRandomStats = () => {
    setStats({
      points: _.random(0, 12000),
      streakDays: _.random(0, 300),
      articlesRead: _.random(0, 1000),
      articlesRated: _.random(0, 1000),
    })
  }

  useEffect(() => {
    const interval = setTimeout(incrementStreakDays, 500)
    return () => clearTimeout(interval)
  }, [])

  return (
    <Screen style={{ justifyContent: "center" }}>
      <Button onPress={zeroOutStats} title="Zero Out Stats" />
      <Button onPress={incrementStreakDays} title="Increment Streak Days" />
      <Button onPress={incrementPoints} title="Increment Points" />
      <Button onPress={incrementStreakAndPoints} title="Increment All" />
      <Button onPress={incrementByMany} title="Increment by Many" />
      <Button onPress={getFirstLevel} title="Get First Level" />
      <Button onPress={setToNextLevel} title="Set to Next Level" />
      <Button onPress={setToLastLevel} title="Set to Last Level" />
      <Button onPress={setToRandomStats} title="Set to Random Stats" />
      <StatsBar />
    </Screen>
  )
}
