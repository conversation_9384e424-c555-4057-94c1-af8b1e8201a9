import { registerRootComponent } from "expo"
import { ExpoRoot } from "expo-router"
import { Text, TextInput } from "react-native"

// Must be exported or Fast Refresh won't update the context
export function App() {
  const ctx = require.context("./app")
  return <ExpoRoot context={ctx} />
}

Text.defaultProps = Text.defaultProps || {}
Text.defaultProps.allowFontScaling = false

TextInput.defaultProps = TextInput.defaultProps || {}
TextInput.defaultProps.allowFontScaling = false

registerRootComponent(App)
