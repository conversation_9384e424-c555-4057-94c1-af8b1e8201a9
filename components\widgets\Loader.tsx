import { ActiveConnectionMode } from "@/context/ModeContext"
import { Screen } from "../Themed"
import { useEffect, useRef } from "react"
import { Animated, Image, StyleSheet, View, Platform } from "react-native"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"
import { datesModePalette, friendsModePalette } from "@/constants/Colors"
import * as Sentry from "@sentry/react-native"
import { EventType, trackEvent } from "@/utils/tracking"

// Conditional import for expo-router (not available on web)
let usePathname: () => string
if (Platform.OS === "web") {
  usePathname = () => "/web-story" // Fallback for web
} else {
  usePathname = require("expo-router").usePathname
}

const TIMEOUT = 60 * 1000

interface LoadingProps {
  connectionMode?: ActiveConnectionMode
  errorTag?: string
}

export const Loader = ({ connectionMode, errorTag }: LoadingProps) => {
  const scaleValue = useRef(new Animated.Value(1)).current
  const pathname = usePathname()

  let style
  if (!connectionMode) {
    style = {}
  } else {
    const backgroundColor =
      connectionMode === ConnectionMode.Dates
        ? datesModePalette.backgroundColor
        : friendsModePalette.backgroundColor
    style = { backgroundColor }
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      trackEvent(EventType.LoaderTimeout, { pathname })
      Sentry.captureException(
        new Error(
          `Loader has been displayed for ${
            TIMEOUT / 1000
          } seconds on ${pathname}`,
        ),
        {
          extra: { pathname, errorTag },
          fingerprint: ["Loader"],
        },
      )
    }, TIMEOUT)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    const startAnimation = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleValue, {
            toValue: 1.1,
            duration: 1650,
            useNativeDriver: true,
          }),
          Animated.timing(scaleValue, {
            toValue: 1,
            duration: 1650,
            useNativeDriver: true,
          }),
        ]),
      ).start()
    }

    startAnimation()
  }, [scaleValue])

  return (
    <Screen style={style}>
      <View style={styles.loaderView}>
        <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
          <Image
            source={require("../../assets/images/loader.gif")}
            style={styles.loaderImageStyle}
          />
        </Animated.View>
      </View>
    </Screen>
  )
}

const size = 90

const styles = StyleSheet.create({
  loaderView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loaderImageStyle: {
    height: size,
    width: size,
    borderRadius: wp(4),
  },
})
