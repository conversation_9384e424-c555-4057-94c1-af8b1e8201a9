import { INPRESS_URL } from "@/apiQueries/constants"
import { TextInput } from "@/components/TextInput"
import { Screen } from "@/components/Themed"
import { SignInProps, useSession } from "@/ctx"
import { Link, router } from "expo-router"
import { useState } from "react"
import { View, Text, TouchableOpacity, StyleSheet } from "react-native"
import { styles as initialScreenStyles } from "./index"
import { newsFeedPath } from "@/utils/deepLinks"
import { fontStyles } from "@/styles"
import { trackAttributionEvent } from "@/utils/tracking"

const LoginView = ({}) => {
  const { signIn } = useSession()

  const [error, setError] = useState("")

  const handleLogin = async ({ email, password }: SignInProps) => {
    try {
      const session = await signIn({ email, password })
      trackAttributionEvent("af_login", { user_id: session.user.id })
      router.replace(newsFeedPath)
    } catch (error: any) {
      setError(error.message)
    }
  }

  return <LoginView_ onSubmit={handleLogin} error={error} />
}

interface LoginViewProps_ {
  onSubmit: (props: SignInProps) => void
  error?: string
}

export const LoginView_ = ({ onSubmit, error }: LoginViewProps_) => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")

  const handleLogin = () => {
    onSubmit({ email, password })
  }

  return (
    <Screen style={styles.container}>
      <View style={styles.spacer} />
      <Text style={styles.title}>Welcome back</Text>
      <TextInput
        label="Email"
        returnKeyType="next"
        blurOnSubmit
        multiline
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        label="Password"
        returnKeyType="done"
        blurOnSubmit
        value={password}
        onChangeText={setPassword}
        secureTextEntry
        autoCapitalize="none"
      />
      <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
        <Text style={styles.loginButtonText}>Log in</Text>
      </TouchableOpacity>
      {error ? <Text style={styles.errorText}>{error}</Text> : null}
      <Link
        style={styles.forgotPassword}
        href={`${INPRESS_URL}/forgot-password` as any}
      >
        Forgot your password?
      </Link>
      <View style={styles.spacer} />
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  spacer: {
    flex: 1,
  },
  title: {
    fontSize: 48,
    letterSpacing: 0.48,
    marginBottom: 20,
    ...fontStyles.editorial,
  },
  loginButton: {
    backgroundColor: "black",
    borderRadius: 4,
    paddingVertical: 12,
    alignItems: "center",
    marginTop: 10,
  },
  loginButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  errorText: {
    textAlign: "center",
    marginTop: 15,
    color: "darkred",
  },
  hidden: {
    display: "none",
  },
  forgotPassword: {
    ...initialScreenStyles.linkText,
    textAlign: "center",
    marginTop: 20,
    color: "black",
  },
})

export default LoginView
