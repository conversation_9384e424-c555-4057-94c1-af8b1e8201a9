import {
  View,
  StyleSheet,
  Text,
  StyleProp,
  ViewStyle,
  TouchableOpacity,
} from "react-native"
import { AntDesign } from "@expo/vector-icons"
import { Href, router } from "expo-router"
import { SvgProps } from "react-native-svg"

export type ProfileItem = {
  iconComponent?: (props: SvgProps) => JSX.Element
  endComponent?: React.JSX.Element
  title: string
  subtitle?: string
  href?: Href
  disabled?: boolean
  onPressed?: () => void
}

export const ProfileList = ({ items }: { items: ProfileItem[] }) => {
  return (
    <View>
      {items.map((d, index) => (
        <ProfileListItem
          key={index}
          item={d}
          style={[
            index == items.length - 1 && styles.outerBottomBorder,
            index == 0 && styles.outerTopBorder,
          ]}
        />
      ))}
    </View>
  )
}

export const ProfileListItem = ({
  item: {
    iconComponent,
    endComponent,
    title,
    subtitle,
    href,
    disabled,
    onPressed,
  },
  style,
}: {
  item: ProfileItem
  style?: StyleProp<ViewStyle>
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.innerContainer,
        disabled && { opacity: 0.5 },
        subtitle ? styles.withSubtitle : styles.withoutSubtitle,
        style,
      ]}
      activeOpacity={0.5}
      onPress={() =>
        !disabled ? (href ? router.push(href) : onPressed?.()) : null
      }
    >
      {iconComponent?.({})}
      <View style={styles.textContainer}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>
      {endComponent ? (
        endComponent
      ) : (
        <AntDesign name="right" size={16} color="black" style={styles.icon} />
      )}
    </TouchableOpacity>
  )
}

const BORDER_RADIUS = 10

const styles = StyleSheet.create({
  innerContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    backgroundColor: "white",
    borderColor: "#DDDDDD",
    borderWidth: 1,
    gap: 12,
  },
  withSubtitle: {
    height: 65,
  },
  withoutSubtitle: {
    height: 56,
  },
  outerBottomBorder: {
    borderBottomEndRadius: BORDER_RADIUS,
    borderBottomStartRadius: BORDER_RADIUS,
  },
  outerTopBorder: {
    borderTopEndRadius: BORDER_RADIUS,
    borderTopStartRadius: BORDER_RADIUS,
  },
  textContainer: {
    flex: 1,
    gap: 6,
  },
  title: {
    fontSize: 14,
    fontWeight: "600",
    color: "black",
  },
  subtitle: {
    fontSize: 12,
    fontWeight: "400",
    color: "gray",
  },
  icon: {
    marginLeft: 10,
    height: 24,
    paddingVertical: 4,
  },
})
