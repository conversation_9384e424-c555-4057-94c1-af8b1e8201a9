import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"

const LeadsIcon = (props: SvgProps) => (
  <Svg width={22} height={22} fill="none" {...props}>
    <Path
      fill="#000"
      d="M9.614 16.796a.946.946 0 0 1-1.744 0l-.877-2.01a7.742 7.742 0 0 0-3.94-3.992L.636 9.722c-.768-.341-.768-1.458 0-1.8l2.34-1.038a7.747 7.747 0 0 0 3.997-4.125l.89-2.142a.946.946 0 0 1 1.757 0l.89 2.142a7.747 7.747 0 0 0 3.997 4.125l2.34 1.039c.768.34.768 1.458 0 1.799l-2.415 1.072a7.742 7.742 0 0 0-3.941 3.991l-.878 2.01ZM3.54 8.822c2.285 1.015 4.144 2.683 5.202 4.978 1.059-2.295 2.917-3.963 5.203-4.978-2.313-1.026-4.177-2.776-5.203-5.111-1.025 2.335-2.89 4.085-5.202 5.111ZM18.4 21.69l.247-.566a4.365 4.365 0 0 1 2.221-2.25l.76-.339a.53.53 0 0 0 0-.963l-.717-.319a4.368 4.368 0 0 1-2.253-2.326l-.254-.611a.507.507 0 0 0-.942 0l-.254.61a4.368 4.368 0 0 1-2.253 2.327l-.718.32a.53.53 0 0 0 0 .962l.76.338a4.365 4.365 0 0 1 2.222 2.251l.247.566c.18.414.754.414.934 0Zm-1.026-3.643.562-.559.55.559-.55.543-.563-.543Z"
    />
  </Svg>
)
export default LeadsIcon

export const LeadsSelectedIcon = (props: SvgProps) => (
  <Svg width={23} height={22} fill="none" {...props}>
    <Path
      fill="#000"
      d="m10.114 16.796.878-2.01a7.742 7.742 0 0 1 3.94-3.992l2.416-1.072c.768-.341.768-1.458 0-1.8l-2.34-1.038a7.747 7.747 0 0 1-3.997-4.125L10.12.617a.946.946 0 0 0-1.758 0l-.889 2.142a7.747 7.747 0 0 1-3.997 4.125l-2.34 1.039c-.768.34-.768 1.458 0 1.799l2.415 1.072a7.742 7.742 0 0 1 3.94 3.991l.878 2.01a.946.946 0 0 0 1.744 0Zm8.787 4.894.247-.566a4.365 4.365 0 0 1 2.221-2.25l.76-.339a.53.53 0 0 0 0-.963l-.717-.319a4.368 4.368 0 0 1-2.253-2.326l-.254-.611a.507.507 0 0 0-.942 0l-.254.61a4.368 4.368 0 0 1-2.253 2.327l-.718.32a.53.53 0 0 0 0 .962l.76.338a4.365 4.365 0 0 1 2.222 2.251l.247.566c.18.414.754.414.934 0Z"
    />
  </Svg>
)
