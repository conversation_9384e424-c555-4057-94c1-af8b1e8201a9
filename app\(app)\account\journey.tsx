import { useSession } from "@/ctx"
import { Loader } from "@/components/widgets/Loader"
import { JourneyScreen } from "@/screens/account/JourneyScreen"
import { useCallback, useEffect, useMemo, useState } from "react"
import { useFeatureFlag } from "@/utils/featureFlags"
import { JourneyScreenOld } from "@/screens/account/JourneyScreenOld"
import { LevelStats } from "@/types/levels"
import { getLeaderboard, getStats } from "@/apiQueries/levels"
import { updateProfileImages } from "@/apiQueries/auth"
import { isUserWithProfile, LeaderboardUser } from "@/types/user"
import { useActiveConnectionMode } from "@/context/ModeContext"
import { useFocusEffect } from "expo-router"

export default function Route() {
  const enableNewLevelsScreen = useFeatureFlag("new_levels_screen")
  const { activeConnectionMode } = useActiveConnectionMode()
  const { session, refreshSession } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [stats, setStats] = useState<LevelStats | undefined>()
  const [leaderboardUsers, setLeaderboardUsers] = useState<
    LeaderboardUser[] | undefined
  >()

  if (!session) {
    return <Loader />
  }

  const fetchStats = async () => {
    const response = await getStats(session.token)
    setStats(response)
  }

  const fetchLeaderboardUsers = async () => {
    const leaderboardUsers = await getLeaderboard({
      token: session!.token,
      connectionMode: activeConnectionMode,
    })
    setLeaderboardUsers(leaderboardUsers)
  }

  useFocusEffect(
    useCallback(() => {
      refreshSession(session.token)
      fetchStats()
      fetchLeaderboardUsers()
    }, []),
  )

  const handleUpdatePicture = async (imageUri: string) => {
    setIsLoading(true)
    if (isUserWithProfile(session.user)) {
      console.error(
        "User with profile should not be able to update picture here",
      )
      return
    }

    await updateProfileImages({
      token: session.token,
      pics: [{ uri: imageUri }],
    })
    refreshSession(session.token)
    setIsLoading(false)
  }

  if (enableNewLevelsScreen) {
    if (isLoading || !stats || !leaderboardUsers) {
      return <Loader />
    }

    return (
      <JourneyScreen
        user={session.user}
        {...stats}
        leaderboardUsers={leaderboardUsers}
        onUpdatePicture={handleUpdatePicture}
      />
    )
  } else {
    return <JourneyScreenOld points={session.user.points} />
  }
}
