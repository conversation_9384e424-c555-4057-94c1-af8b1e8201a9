import { ConnectionModeContextValue } from "@/context/ModeContext"
import { handleResponse } from "@/utils/pushNotifications"
import * as Notifications from "expo-notifications"

export const NEW_MESSAGE_REQUEST: Notifications.NotificationRequest = {
  content: {
    title: "New message",
    subtitle: "You have a new message",
    body: "You have a new message",
    data: {
      type: "new_message",
      connectionMode: "friends",
    },
    sound: "default",
  },
  identifier: "new_message",
  trigger: {
    type: "push",
  },
}

export const triggerResponseHandler = async ({
  request,
  setActiveConnectionMode,
}: {
  request: Notifications.NotificationRequest
  setActiveConnectionMode: ConnectionModeContextValue["setActiveConnectionMode"]
}) => {
  const response: Notifications.NotificationResponse = {
    notification: {
      request: request,
      date: new Date().getMilliseconds(),
    },
    actionIdentifier: "default",
  }

  handleResponse({ response, setActiveConnectionMode })
}
