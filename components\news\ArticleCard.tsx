import { View, StyleSheet } from "react-native"
import { Card, Title } from "react-native-paper"
import ArticleSource from "./ArticleSource"
import Footer from "./Footer"
import { useFeatureFlag } from "@/utils/featureFlags"
import { ReadRatedCover } from "./ReadRatedCover"
import { useState } from "react"
import ReportArticleModal from "./ReportArticleModal"
import { openArticle } from "../ArticlePage"
import { Image } from "expo-image"
import { newsStyles } from "./constants"
import { Article } from "@/types/news"

export const ArticleCard = ({ article }: { article: Article }) => {
  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")

  const [isReporting, setIsReporting] = useState(false)

  const handlePress = async () => {
    openArticle(article)
  }

  return (
    <Card onPress={handlePress} onLongPress={() => setIsReporting(true)}>
      <Card.Content style={styles.container}>
        <View style={styles.leftContainer}>
          <View>
            <ArticleSource article={article} />
            <Title
              numberOfLines={newReadRatedFlag ? 4 : 3}
              style={styles.title}
            >
              {article.title}
            </Title>
          </View>
          {!newReadRatedFlag && (
            <Footer
              isRead={article.isOpened}
              isRated={article.isSurveyed}
              articleId={article.id}
            />
          )}
        </View>
        <View>
          <Image source={{ uri: article.imageUrl }} style={styles.image} />
          {newReadRatedFlag && <ReadRatedCover article={article} />}
        </View>
        <ReportArticleModal
          articleId={article.id}
          modalVisible={isReporting}
          onClose={() => setIsReporting(false)}
        />
      </Card.Content>
    </Card>
  )
}

const CONTENT_HEIGHT = 124
export const ARTICLE_CARD_HEIGHT = CONTENT_HEIGHT + 28

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    height: ARTICLE_CARD_HEIGHT,
    backgroundColor: "white",
    borderRadius: 10,
  },

  leftContainer: {
    borderRadius: 10,
    marginRight: 35,
    flex: 1,
    height: CONTENT_HEIGHT,
    justifyContent: "space-between",
  },
  title: { ...newsStyles.smallArticleTitle, marginTop: -4 },
  image: {
    width: 120,
    height: 120,
    borderRadius: 10,
  },
})
