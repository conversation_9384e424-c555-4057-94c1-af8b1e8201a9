import React from "react"
import { Platform, View } from "react-native"
import { LinearGradient as ExpoLinearGradient } from "expo-linear-gradient"

interface LinearGradientProps {
  colors: string[]
  style?: any
  start?: { x: number; y: number }
  end?: { x: number; y: number }
  locations?: number[]
  children?: React.ReactNode
}

const LinearGradient: React.FC<LinearGradientProps> = ({
  colors,
  style,
  start,
  end,
  locations,
  children,
}) => {
  if (Platform.OS === "web") {
    // Convert React Native gradient props to CSS gradient
    const direction = getGradientDirection(start, end)
    const colorStops = colors.map((color, index) => {
      const location = locations?.[index]
      return location !== undefined ? `${color} ${location * 100}%` : color
    }).join(", ")
    
    const backgroundImage = `linear-gradient(${direction}, ${colorStops})`
    
    return (
      <View style={[style, { backgroundImage }]}>
        {children}
      </View>
    )
  }

  // Native platforms use ExpoLinearGradient
  return (
    <ExpoLinearGradient
      colors={colors}
      style={style}
      start={start}
      end={end}
      locations={locations}
    >
      {children}
    </ExpoLinearGradient>
  )
}

// Helper function to convert start/end points to CSS direction
const getGradientDirection = (
  start?: { x: number; y: number },
  end?: { x: number; y: number }
): string => {
  if (!start || !end) return "to bottom"

  // Convert React Native coordinates to CSS direction
  const deltaX = end.x - start.x
  const deltaY = end.y - start.y

  if (deltaY > 0 && Math.abs(deltaX) < 0.1) return "to bottom"
  if (deltaY < 0 && Math.abs(deltaX) < 0.1) return "to top"
  if (deltaX > 0 && Math.abs(deltaY) < 0.1) return "to right"
  if (deltaX < 0 && Math.abs(deltaY) < 0.1) return "to left"

  // For diagonal gradients, approximate the angle
  if (deltaY < 0) return "to top"
  return "to bottom"
}

export default LinearGradient
