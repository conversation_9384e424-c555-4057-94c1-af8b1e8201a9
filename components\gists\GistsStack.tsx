import { Animated, Pressable, StyleSheet, View } from "react-native"
import { Screen } from "../Themed"
import { Swiper, SwiperInnerProps } from "../Swiper"
import { GistCard } from "./GistCard"
import { useEffect, useMemo, useRef, useState } from "react"
import { BEIGE } from "@/constants/Colors"
import { useNewsContext } from "@/context/NewsContext"
import { Article, GistSwipeType, NewsEventType } from "@/types/news"
import { useSession } from "@/ctx"
import { trackEvent } from "@/utils/tracking"
import { SwipeIndicator } from "./SwipeIndicator"
import { NoMoreGists } from "../NoMoreGists"
import RNSwiper from "react-native-deck-swiper"
import { getSectionByName } from "../news/constants"
import { useLevels } from "@/context/LevelContext"
import { isUnratedArticle } from "@/utils/processArticles"
import { Loader } from "../widgets/Loader"

type HandleSwipeProps = {
  article: Article
  rating: GistSwipeType
  nextArticle: Article
}

const GistsStack = () => {
  const { articles, setActiveSection, handleGistRating } = useNewsContext()
  const { refreshStats } = useLevels()

  const { session } = useSession()

  const handleSwipe = async ({
    article,
    rating,
    nextArticle,
  }: HandleSwipeProps) => {
    const articleId = article.id

    await handleGistRating({
      token: session!.token,
      articleId,
      rating,
    })

    trackEvent(NewsEventType.GistSwiped, {
      data: { article_id: articleId, rating },
    })

    if (nextArticle) {
      setActiveSection(getSectionByName(nextArticle.frontpageSection))
    }

    refreshStats()
  }

  return <GistsStack_ articles={articles} onSwipe={handleSwipe} />
}

type GistsStackProps = {
  articles: Article[]
  swiperProps?: Partial<SwiperInnerProps<Article>>
  demoMode?: boolean
  onSwipe: (args: HandleSwipeProps) => Promise<void>
}

export const GistsStack_ = ({
  articles,
  swiperProps,
  demoMode = false,
  onSwipe,
}: GistsStackProps) => {
  const { setGistIsSwiping } = useNewsContext()
  const [swiperKey, setSwiperKey] = useState(0)
  const [unratedArticles, setUnratedArticles] = useState<Article[]>(
    articles.filter(isUnratedArticle),
  )
  const [activeCardIndex, setActiveCardIndex] = useState<number>(0)
  const [loadedImageUrls, setLoadedImageUrls] = useState<string[]>([])
  const [loadingCompleted, setLoadingCompleted] = useState(false)
  const [allGistsAreSwiped, setAllGistsAreSwiped] = useState<boolean>(false)

  const isMounted = useRef(false)
  const swiperRef = useRef<RNSwiper<Article> | null>(null)
  const loadAnim = useRef(new Animated.Value(0)).current

  const articleIds = useMemo(() => {
    return articles.map((article) => article.id).join(",")
  }, [articles])

  const fullRatedCount = useMemo(() => {
    return articles.filter((article) => article.isSurveyed).length
  }, [articles])

  useEffect(() => {
    const unratedArticles = articles.filter(isUnratedArticle)
    setUnratedArticles(unratedArticles)
    setActiveCardIndex(0)
  }, [articleIds, fullRatedCount])

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      return
    }

    setSwiperKey((prevKey) => prevKey + 1)
    setLoadedImageUrls([])
    setLoadingCompleted(false)
  }, [unratedArticles])

  useEffect(() => {
    const firstThreeArticles = unratedArticles.slice(0, 3)
    const allImagesLoaded = firstThreeArticles.every((article) =>
      loadedImageUrls.includes(article.imageUrl),
    )
    if (allImagesLoaded) {
      setLoadingCompleted(true)
      return
    } else {
      setLoadingCompleted(false)
    }

    const timeout = setTimeout(() => {
      trackEvent(NewsEventType.GistsForceLoaded, {
        data: { article_ids: firstThreeArticles.map((a) => a.id) },
      })
      setLoadingCompleted(true)
    }, 3000)
    return () => clearTimeout(timeout)
  }, [loadedImageUrls, unratedArticles])

  useEffect(() => {
    if (loadingCompleted) {
      Animated.timing(loadAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start()
    } else {
      loadAnim.setValue(0)
    }
  }, [loadingCompleted])

  const handleImageLoaded = (imageUrl: string) => {
    if (loadedImageUrls.includes(imageUrl)) return
    setLoadedImageUrls((prev) => [...prev, imageUrl])
  }

  const handleSwipe = async ({
    cardIndex,
    rating,
  }: {
    cardIndex: number
    rating: GistSwipeType
  }) => {
    setActiveCardIndex(cardIndex + 1)
    await onSwipe({
      article: unratedArticles[cardIndex],
      rating,
      nextArticle: unratedArticles[cardIndex + 1],
    })
  }

  const handleTimerCompleted = () => {
    swiperRef.current?.swipeTop()
    const article = unratedArticles[activeCardIndex]
    trackEvent(NewsEventType.GistAutoSwiped, {
      data: { article_id: article?.id },
    })
  }

  if (unratedArticles.length === 0 || allGistsAreSwiped) {
    return (
      <Screen style={styles.container}>{!demoMode && <NoMoreGists />}</Screen>
    )
  }

  return (
    <View style={styles.container}>
      {!loadingCompleted && !demoMode && (
        <View style={styles.loaderContainer}>
          <Loader errorTag="GistsStack" />
        </View>
      )}

      <Animated.View style={[styles.swiperContainer, { opacity: loadAnim }]}>
        <Swiper<Article>
          {...swiperProps}
          ref={swiperRef}
          key={swiperKey}
          cards={unratedArticles}
          stackScale={3}
          showSecondCard={true}
          verticalSwipe={false}
          cardIndex={0}
          stackSize={3}
          stackSeparation={-18}
          disableBottomSwipe
          cardVerticalMargin={0}
          cardHorizontalMargin={0}
          marginTop={6}
          marginBottom={0}
          backgroundColor={BEIGE}
          containerStyle={styles.cardStyle}
          cardStyle={styles.cardStyle}
          keyExtractor={(article) =>
            article ? article.id.toString() : `${Math.random()}`
          }
          thresholdMultiplier={4}
          renderCard={(article, cardIndex) => {
            return (
              article && (
                <Pressable
                  style={{ flex: 0.7 }}
                  key={article.id}
                  onPressIn={() => setGistIsSwiping(true)}
                  onPressOut={() => setGistIsSwiping(false)}
                >
                  <GistCard
                    isVisible={activeCardIndex === cardIndex}
                    article={article}
                    demoMode={demoMode}
                    onImageLoaded={() => handleImageLoaded(article.imageUrl)}
                    onTimerCompleted={handleTimerCompleted}
                  />
                </Pressable>
              )
            )
          }}
          renderAcceptComponent={(overlay) =>
            !swiperProps?.disableRightSwipe && (
              <SwipeIndicator type="like" overlayStyle={overlay} />
            )
          }
          renderRejectComponent={(overlay) =>
            !swiperProps?.disableLeftSwipe && (
              <SwipeIndicator type="dislike" overlayStyle={overlay} />
            )
          }
          onTapCard={() => setGistIsSwiping(false)}
          onSwiping={() => setGistIsSwiping(true)}
          onSwipedAborted={() => setGistIsSwiping(false)}
          onSwiped={() => setGistIsSwiping(false)}
          onSwipedLeft={(cardIndex) =>
            handleSwipe({ cardIndex, rating: "dislike" })
          }
          onSwipedRight={(cardIndex) =>
            handleSwipe({ cardIndex, rating: "like" })
          }
          onSwipedTop={(cardIndex) =>
            handleSwipe({ cardIndex, rating: "skip" })
          }
          onSwipedAll={() => setAllGistsAreSwiped(true)}
        />
      </Animated.View>
    </View>
  )
}

export default GistsStack

const styles = StyleSheet.create({
  container: {
    backgroundColor: BEIGE,
    paddingHorizontal: 0,
    justifyContent: "center",
    flex: 1,
    zIndex: 1,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  swiperContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: "center",
  },
  cardStyle: {
    margin: 0,
    padding: 0,
    width: "100%",
  },
})
