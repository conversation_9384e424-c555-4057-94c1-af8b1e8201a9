import {
  Animated,
  Pressable,
  StyleSheet,
  View,
  Platform,
  Text,
} from "react-native"
import { useEffect, useMemo, useRef, useState } from "react"
import { Screen } from "../Themed"
import { GistCard } from "./GistCard"
import { BEIGE } from "@/constants/Colors"
import { useNewsContext } from "@/context/NewsContext"
import { Article, GistSwipeType, NewsEventType } from "@/types/news"
import { useSession } from "@/ctx"
import { trackEvent } from "@/utils/tracking"
import { SwipeIndicator } from "./SwipeIndicator"
import { NoMoreGists } from "../NoMoreGists"
import { getSectionByName } from "../news/constants"
import { useLevels } from "@/context/LevelContext"

let Swiper: any
let SwiperInnerPropsType: any
let RNSwiperType: any
let TinderCard: any

let onWeb = Platform.OS === "web" ? true : false

if (!onWeb) {
  const SwiperModule = require("../Swiper")
  Swiper = SwiperModule.Swiper
  SwiperInnerPropsType = SwiperModule.SwiperInnerProps
  RNSwiperType = require("react-native-deck-swiper").default
} else {
  Swiper = require("../swiper-web").default
  TinderCard = require("react-tinder-card")
  SwiperInnerPropsType = {} as any
  RNSwiperType = {} as any
}
import { isUnratedArticle } from "@/utils/processArticles"
import { Loader } from "../widgets/Loader"

type HandleSwipeProps = {
  article: Article
  rating: GistSwipeType
  nextArticle: Article
}

const GistsStack = () => {
  const { articles, setActiveSection, handleGistRating } = useNewsContext()
  const { refreshStats } = useLevels()

  const { session } = useSession()

  const handleSwipe = async ({
    article,
    rating,
    nextArticle,
  }: HandleSwipeProps) => {
    const articleId = article.id

    await handleGistRating({
      token: session!.token,
      articleId,
      rating,
    })

    trackEvent(NewsEventType.GistSwiped, {
      data: { article_id: articleId, rating },
    })

    if (nextArticle) {
      setActiveSection(getSectionByName(nextArticle.frontpageSection))
    }

    refreshStats()
  }

  return <GistsStack_ articles={articles} onSwipe={handleSwipe} />
}

type GistsStackProps = {
  articles: Article[]
  swiperProps?: any
  demoMode?: boolean
  onSwipe: (args: HandleSwipeProps) => Promise<void>
}

export const GistsStack_ = ({
  articles,
  swiperProps,
  demoMode = false,
  onSwipe,
}: GistsStackProps) => {
  const { setGistIsSwiping } = useNewsContext()
  const [swiperKey, setSwiperKey] = useState(0)
  const [unratedArticles, setUnratedArticles] = useState<Article[]>(
    articles.filter(isUnratedArticle),
  )
  const [activeCardIndex, setActiveCardIndex] = useState<number>(0)
  const [loadedImageUrls, setLoadedImageUrls] = useState<string[]>([])
  const [loadingCompleted, setLoadingCompleted] = useState(false)
  const [allGistsAreSwiped, setAllGistsAreSwiped] = useState<boolean>(false)

  const isMounted = useRef(false)

  const articleIds = useMemo(() => {
    return articles.map((article) => article.id).join(",")
  }, [articles])

  const fullRatedCount = useMemo(() => {
    return articles.filter((article) => article.isSurveyed).length
  }, [articles])

  useEffect(() => {
    const unratedArticles = articles.filter(isUnratedArticle)
    setUnratedArticles(unratedArticles)
    setActiveCardIndex(0)
  }, [articleIds, fullRatedCount])

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      return
    }

    setSwiperKey((prevKey) => prevKey + 1)
    setLoadedImageUrls([])
    setLoadingCompleted(false)
  }, [unratedArticles])

  useEffect(() => {
    if (
      unratedArticles
        .slice(0, 3)
        .every((article) => loadedImageUrls.includes(article.imageUrl))
    ) {
      setLoadingCompleted(true)
    } else {
      setLoadingCompleted(false)
    }
  }, [loadedImageUrls, unratedArticles])

  const handleImageLoaded = (imageUrl: string) => {
    if (loadedImageUrls.includes(imageUrl)) return
    setLoadedImageUrls((prev) => [...prev, imageUrl])
  }

  const handleSwipe = async ({
    cardIndex,
    rating,
  }: {
    cardIndex: number
    rating: GistSwipeType
  }) => {
    setActiveCardIndex(cardIndex + 1)
    await onSwipe({
      article: unratedArticles[cardIndex],
      rating,
      nextArticle: unratedArticles[cardIndex + 1],
    })
  }

  if (unratedArticles.length === 0 || allGistsAreSwiped) {
    return (
      <Screen style={styles.container}>
        <NoMoreGists />
      </Screen>
    )
  }

  // Platform-specific variables (declared for both platforms)
  const swiperRef = useRef<any>(null)
  const loadAnim = useRef(
    onWeb ? new Animated.Value(1) : new Animated.Value(0),
  ).current

  // Platform-specific animation effect (only runs on mobile)
  useEffect(() => {
    if (!onWeb && loadingCompleted) {
      Animated.timing(loadAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start()
    } else if (!onWeb) {
      loadAnim.setValue(0)
    }
  }, [loadingCompleted, onWeb])

  // Platform-specific timer handler
  const handleTimerCompleted = () => {
    if (!onWeb) {
      swiperRef.current?.swipeTop()
      const article = unratedArticles[activeCardIndex]
      trackEvent(NewsEventType.GistAutoSwiped, {
        data: { article_id: article?.id },
      })
    } else {
      // TODO: Implement web auto-swipe with react-tinder-card
      console.log("Auto-swipe on web - TODO")
    }
  }
  return (
    <View style={styles.container}>
      {!loadingCompleted && (
        <View style={styles.loaderContainer}>
          <Loader errorTag="GistsStack" />
        </View>
      )}
      {!onWeb ? (
        <Animated.View style={[styles.swiperContainer, { opacity: loadAnim }]}>
          <Swiper<Article>
            {...swiperProps}
            ref={swiperRef}
            key={swiperKey}
            cards={unratedArticles}
            stackScale={3}
            showSecondCard={true}
            verticalSwipe={false}
            cardIndex={0}
            stackSize={3}
            stackSeparation={-18}
            disableBottomSwipe
            cardVerticalMargin={0}
            cardHorizontalMargin={0}
            marginTop={6}
            marginBottom={0}
            backgroundColor={BEIGE}
            containerStyle={styles.cardStyle}
            cardStyle={styles.cardStyle}
            keyExtractor={(article: Article) =>
              article ? article.id.toString() : `${Math.random()}`
            }
            thresholdMultiplier={4}
            renderCard={(article: Article, cardIndex: number) => {
              return (
                article && (
                  <Pressable
                    style={{ flex: 0.7 }}
                    key={article.id}
                    onPressIn={() => setGistIsSwiping(true)}
                    onPressOut={() => setGistIsSwiping(false)}
                  >
                    <GistCard
                      isVisible={activeCardIndex === cardIndex}
                      article={article}
                      demoMode={demoMode}
                      onImageLoaded={() => handleImageLoaded(article.imageUrl)}
                      onTimerCompleted={handleTimerCompleted}
                    />
                  </Pressable>
                )
              )
            }}
            renderAcceptComponent={(overlay: any) =>
              !swiperProps?.disableRightSwipe && (
                <SwipeIndicator type="like" overlayStyle={overlay} />
              )
            }
            renderRejectComponent={(overlay: any) =>
              !swiperProps?.disableLeftSwipe && (
                <SwipeIndicator type="dislike" overlayStyle={overlay} />
              )
            }
            onTapCard={() => setGistIsSwiping(false)}
            onSwiping={() => setGistIsSwiping(true)}
            onSwipedAborted={() => setGistIsSwiping(false)}
            onSwiped={() => setGistIsSwiping(false)}
            onSwipedLeft={(cardIndex: number) =>
              handleSwipe({ cardIndex, rating: "dislike" })
            }
            onSwipedRight={(cardIndex: number) =>
              handleSwipe({ cardIndex, rating: "like" })
            }
            onSwipedTop={(cardIndex: number) =>
              handleSwipe({ cardIndex, rating: "skip" })
            }
            onSwipedAll={() => setAllGistsAreSwiped(true)}
          />
        </Animated.View>
      ) : (
        <Swiper<Article>
          {...swiperProps}
          ref={swiperRef}
          key={swiperKey}
          cards={unratedArticles}
          stackScale={3}
          showSecondCard={true}
          verticalSwipe={false}
          cardIndex={0}
          stackSize={3}
          stackSeparation={-18}
          disableBottomSwipe
          cardVerticalMargin={0}
          cardHorizontalMargin={0}
          marginTop={6}
          marginBottom={0}
          backgroundColor={BEIGE}
          containerStyle={styles.cardStyle}
          cardStyle={styles.cardStyle}
          keyExtractor={(article: Article) =>
            article ? article.id.toString() : `${Math.random()}`
          }
          thresholdMultiplier={4}
          renderCard={(article: Article, cardIndex: number) => {
            return (
              article && (
                <div
                  style={{ flex: 0.7 }}
                  key={article.id}
                  onMouseDown={() => setGistIsSwiping(true)}
                  onMouseUp={() => setGistIsSwiping(false)}
                >
                  <GistCard
                    isVisible={activeCardIndex === cardIndex}
                    article={article}
                    demoMode={demoMode}
                    onImageLoaded={() => handleImageLoaded(article.imageUrl)}
                    onTimerCompleted={handleTimerCompleted}
                  />
                </div>
              )
            )
          }}
          renderAcceptComponent={(overlay: any) =>
            !swiperProps?.disableRightSwipe && (
              <SwipeIndicator type="like" overlayStyle={overlay} />
            )
          }
          renderRejectComponent={(overlay: any) =>
            !swiperProps?.disableLeftSwipe && (
              <SwipeIndicator type="dislike" overlayStyle={overlay} />
            )
          }
          onTapCard={() => setGistIsSwiping(false)}
          onSwiping={() => setGistIsSwiping(true)}
          onSwipedAborted={() => setGistIsSwiping(false)}
          onSwiped={() => setGistIsSwiping(false)}
          onSwipedLeft={(cardIndex: number) =>
            handleSwipe({ cardIndex, rating: "dislike" })
          }
          onSwipedRight={(cardIndex: number) =>
            handleSwipe({ cardIndex, rating: "like" })
          }
          onSwipedTop={(cardIndex: number) =>
            handleSwipe({ cardIndex, rating: "skip" })
          }
          onSwipedAll={() => setAllGistsAreSwiped(true)}
        />
      )}
    </View>
  )
}

export default GistsStack

const styles = StyleSheet.create({
  container: {
    backgroundColor: BEIGE,
    paddingHorizontal: 0,
    justifyContent: "center",
    flex: 1,
    zIndex: 1,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  swiperContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: "center",
  },
  cardStyle: {
    margin: 0,
    padding: 0,
    width: "100%",
  },
})
