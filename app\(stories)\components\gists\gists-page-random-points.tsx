import { Button } from "@/components/Button"
import { Screen } from "@/components/Themed"
import { useLevels } from "@/context/LevelContext"
import { GistsPage } from "@/screens/news/GistsPage"
import { SESSION } from "../../notifications/notification-feed"

export default function Story() {
  const { setStats } = useLevels()

  const randomizePoints = () => {
    setStats((prev) => ({
      ...prev!,
      points: Math.floor(Math.random() * 11000),
    }))
  }

  return (
    <Screen>
      <Button text="Randomize points" onPress={randomizePoints} />
      <GistsPage session={SESSION} />
    </Screen>
  )
}
