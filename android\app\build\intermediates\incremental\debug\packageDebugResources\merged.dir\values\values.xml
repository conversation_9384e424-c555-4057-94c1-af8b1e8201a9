<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">InPress</string>
    <string name="expo_runtime_version">1.0.0</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">automatic</string>
    <string name="facebook_app_id">437278972477723</string>
    <string name="facebook_client_token">********************************</string>
    <string name="fb_login_protocol_scheme">fb437278972477723</string>
    <string name="gcm_defaultSenderId" translatable="false">648197698885</string>
    <string name="google_api_key" translatable="false">AIzaSyB3qbQBkr9tsINR2K8HDA9JlNWn_-XBLZE</string>
    <string name="google_app_id" translatable="false">1:648197698885:android:923e3fdd7ff0d914f63a2c</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyB3qbQBkr9tsINR2K8HDA9JlNWn_-XBLZE</string>
    <string name="google_storage_bucket" translatable="false">inpress-d7ae8.firebasestorage.app</string>
    <string name="project_id" translatable="false">inpress-d7ae8</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style>
    <style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>