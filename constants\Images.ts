import { ImageURISource } from "react-native"

export const IMAGES: Record<string, ImageURISource> = {
  MapPin: require("../assets/images/map-pin.png"),
  Location: require("../assets/images/current-location.png"),
  CaretDown: require("../assets/images/caret-down.png"),
  OpenBook: require("../assets/images/book-open.png"),
  Brain: require("../assets/images/brain.png"),
  Eyeglasses: require("../assets/images/eyeglasses.png"),
  Fire: require("../assets/images/fire.png"),
  Leaf: require("../assets/images/leaf.png"),
  Trophy_1: require("../assets/images/trophy_1.png"),
  Trophy_2: require("../assets/images/trophy_2.png"),
  Trophy_3: require("../assets/images/trophy_3.png"),
  Dark_Leaf: require("../assets/images/dark-Leaf.png"),
  Heart: require("../assets/images/Heart.png"),
  ThumbsDown: require("../assets/images/thumbs-down.png"),
  UnachievedNewbieBadge: require("../assets/images/unachieved-newbie-badge.png"),
  HeartHalf: require("../assets/images/heart-half.png"),
  ArrowRight: require("../assets/images/arrow-right.png"),
  ShuffleOn: require("../assets/images/shuffle-on.png"),
  ShuffleOff: require("../assets/images/shuffle-off.png"),
}
