import { BROWNSTONE_CTA } from "@/constants/Colors"
import { StyleSheet, Text, View } from "react-native"

type UnreadCounterProps = {
  count: number
}

export const UnreadCounter = ({ count }: UnreadCounterProps) => {
  if (count === 0) {
    return null
  }

  return (
    <View style={styles.countWrapper}>
      <Text style={styles.countTxt}>{count}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  countWrapper: {
    position: "absolute",
    backgroundColor: BROWNSTONE_CTA,
    top: -4,
    right: -5,
    borderRadius: 10,
    height: 16,
    width: 16,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 2,
  },
  countTxt: {
    fontSize: 10,
    color: "white",
  },
})
