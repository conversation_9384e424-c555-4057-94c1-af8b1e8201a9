import GistsStep, {
  SUBTITLE as GISTS_SUBTITLE,
  TITLE as GISTS_TITLE,
} from "@/components/signInOrUp/GistsStep"
import { RegularStep, Step } from "@/components/signInOrUp/SignUp"
import {
  TITLE as INSCORE_TITLE,
  SUBTITLE as INSCORE_SUBTITLE,
  InScoreStep,
} from "@/components/signInOrUp/InScoreStep"
import {
  TITLE as DEPTH_TITLE,
  SUBTITLE as DEPTH_SUBTITLE,
  DepthStep,
} from "@/components/signInOrUp/DepthStep"
import {
  TITLE as MATCH_TITLE,
  MatchingExplainerStep,
} from "@/components/signInOrUp/MatchingExplainerStep"

type Props = {
  onNext: Required<Step>["onNext"]
}

export const assembleExplainerSteps = ({ onNext }: Props) => {
  const steps: RegularStep[] = [
    {
      title: INSCORE_TITLE,
      subtitle: INSCORE_SUBTITLE,
      bodyComponent: () => <InScoreStep />,
      label: "inscoreExplainer",
    },
    {
      title: GISTS_TITLE,
      subtitle: GISTS_SUBTITLE,
      bodyComponent: () => <GistsStep onComplete={onNext} />,
      submitIsDisabled: true,
      label: "gistsExplainer",
    },
    {
      title: DEPTH_TITLE,
      subtitle: DEPTH_SUBTITLE,
      bodyComponent: () => <DepthStep />,
      label: "fullRatingExplainer",
    },
    {
      title: MATCH_TITLE,
      bodyComponent: () => <MatchingExplainerStep />,
      label: "matchExplainer",
    },
  ]
  return steps
}
