import { Picker } from "@react-native-picker/picker"
import { View } from "react-native"
import { GenderPref } from "./GenderPrefStep"
interface GenderStepProps {
  gender?: string
  includeSelect?: boolean
  onChange: (gender: string) => void
}

export type GenderLabel = "Male" | "Female" | "Non-binary"
export const genderOptions: { value: GenderPref; label: GenderLabel }[] = [
  {
    value: "male",
    label: "Male",
  },
  {
    value: "female",
    label: "Female",
  },
  {
    value: "nonbinary",
    label: "Non-binary",
  },
]

export const GenderStep = ({
  gender,
  includeSelect = true,
  onChange,
}: GenderStepProps) => (
  <View>
    <Picker selectedValue={gender} onValueChange={onChange}>
      {includeSelect && <Picker.Item label="Select" value="select" />}
      {genderOptions.map(({ value, label }) => (
        <Picker.Item key={value} label={label} value={value} />
      ))}
    </Picker>
  </View>
)
