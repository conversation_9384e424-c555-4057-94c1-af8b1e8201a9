import { sendPhoneVerification } from "@/apiQueries/auth"
import { <PERSON><PERSON> } from "@/components/Button"
import PhoneStep from "@/components/signInOrUp/PhoneStep"
import { Screen, Text } from "@/components/Themed"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { BEIGE } from "@/constants/Colors"
import { useRouter } from "expo-router"
import { useState } from "react"
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
} from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"

export default function Route() {
  const router = useRouter()

  const [phoneNumber, setPhoneNumber] = useState("")
  const [error, setError] = useState<string>()

  const onPhoneNumberSubmit = async () => {
    try {
      if (!phoneNumber) throw new Error("No phone number")
      await sendPhoneVerification(phoneNumber)
      router.replace({
        pathname: "/(app)/account/verify-phone",
        params: { phoneNumber },
      })
    } catch (error: any) {
      setError("Couldn't send verification code")
    }
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1, backgroundColor: BEIGE }}
      keyboardVerticalOffset={60 + useSafeAreaInsets().bottom}
    >
      <ScrollView>
        <Screen>
          <ScreenHeader
            title="See what hidden interests you and your friends have!"
            subtitle="Add your phone number and send them a Match invite to find out."
          />
          <PhoneStep
            phoneNumber={phoneNumber}
            onChange={(phoneNumber) => setPhoneNumber(phoneNumber)}
          />
          <Button text="Add phone number" onPress={onPhoneNumberSubmit} />
          {error && (
            <Text style={styles.errorMessage}>
              That didn't work, please try again
            </Text>
          )}
        </Screen>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 28,
    marginBottom: 16,
  },
  errorMessage: { color: "red", marginTop: 12, textAlign: "center" },
})
