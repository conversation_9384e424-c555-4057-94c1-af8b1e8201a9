import React from "react"
import {
  <PERSON><PERSON>View,
  TouchableWithoutFeedback,
  View,
  Text,
  StyleSheet,
} from "react-native"
import { PersonCardTop } from "./PersonCardTop"
import { Scoop } from "./Scoop"
import { sectionTitleStyle } from "./constants"
import { DetailItem } from "./DetailItem"
import { calculateDistanceInMiles } from "@/utils/allowableAreas"
import { Lead } from "@/apiQueries/apiQueries"
import CandleLineIcon from "../icons/CandleLine"
import BriefcaseIcon from "../icons/Briefcase"
import MapPinLineIcon from "../icons/MapPinLine"
import { ConfirmationModal } from "../ConfirmationModal"
import { Image } from "expo-image"
import _ from "lodash"
import SharedInterestsPanel from "./SharedInterestsPanel"
import { User } from "@/types/user"

interface PersonCardProps {
  lead: Lead
  recipientUser: User
  showHideAndReport?: boolean
  onHideAndReport?: (userId: number) => void
}

const PersonCard = ({
  lead,
  recipientUser,
  showHideAndReport = true,
  onHideAndReport,
}: PersonCardProps) => {
  const { user, topics } = lead

  const [modalIsVisible, setModalIsVisible] = React.useState(false)

  const handlePressHideAndReport = () => {
    setModalIsVisible(true)
  }

  const handleHideAndReport = () => {
    setModalIsVisible(false)
    onHideAndReport!(user.id)
  }

  const imageScoopSectionCount = Math.max(
    user.images.length - 1,
    user.scoopResponses.length,
  )

  const distance = calculateDistanceInMiles(
    recipientUser.latitude,
    recipientUser.longitude,
    user.latitude,
    user.longitude,
  )

  let distanceText

  if (distance === 0) {
    distanceText = "Less than 1 mile away"
  } else if (distance === 1) {
    distanceText = "1 mile away"
  } else {
    distanceText = `${distance} miles away`
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <TouchableWithoutFeedback>
        <View>
          <PersonCardTop lead={lead} />
          <View style={styles.personCardBody}>
            <View style={styles.bodyContainer}>
              {topics ? (
                <View style={styles.topicsWrapper}>
                  <SharedInterestsPanel topics={topics} />
                </View>
              ) : null}
              {user.biography.length ? (
                <View>
                  <Text style={sectionTitleStyle}>Bio</Text>
                  <Text style={styles.biography}>{user.biography}</Text>
                </View>
              ) : null}

              <View style={styles.detailsContainer}>
                <DetailItem
                  iconComponent={<CandleLineIcon style={styles.icon} />}
                  text={`${user.age} years old`}
                />
                <View style={styles.spacer}></View>
                <DetailItem
                  iconComponent={<BriefcaseIcon style={styles.icon} />}
                  text={user.occupation}
                />
                <View style={styles.spacer}></View>
                <DetailItem
                  iconComponent={<MapPinLineIcon style={styles.icon} />}
                  text={distanceText}
                />
              </View>

              {_.range(0, imageScoopSectionCount).map((index) => (
                <View style={styles.scoop} key={index}>
                  {user.scoopResponses[index] && (
                    <Scoop
                      scoopResponse={user.scoopResponses[index]}
                      key={`scoop-${index}`}
                    />
                  )}
                  {user.images[index + 1] && (
                    <Image
                      style={styles.profileImage}
                      key={`image-${index + 1}`}
                      source={{
                        uri: user.images[index + 1].url,
                      }}
                    />
                  )}
                </View>
              ))}

              {showHideAndReport && (
                <Text
                  style={styles.hideAndReportText}
                  onPress={handlePressHideAndReport}
                >
                  Hide and Report
                </Text>
              )}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
      <ConfirmationModal
        visible={modalIsVisible}
        title="Hide and report user"
        description="This will block this user from your feed."
        confirmButtonText="Hide and report"
        onConfirm={handleHideAndReport}
        onCancel={() => setModalIsVisible(false)}
      />
      <View style={{ height: 200 }}></View>
    </ScrollView>
  )
}

const sectionGap = 35

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  personCardBody: {
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: 20,
    paddingBottom: 20,
    flex: 1,
    justifyContent: "flex-start",
    alignItems: "center",
    backgroundColor: "white",
    marginTop: -10,
  },
  bodyContainer: {
    width: "90%",
    gap: sectionGap,
  },
  topicsWrapper: {
    marginTop: sectionGap,
  },
  biography: {
    color: "black",
    fontSize: 14,
    textAlign: "left",
  },
  detailsContainer: {
    flexDirection: "column",
    gap: 10,
  },
  spacer: {
    height: 1,
    backgroundColor: "lightgray",
  },
  icon: {
    width: 24,
    height: 24,
    marginHorizontal: 10,
    alignSelf: "center",
  },
  scoop: {
    gap: 20,
  },
  profileImage: {
    width: "100%",
    height: 400,
    borderRadius: 20,
    marginBottom: 15,
  },
  hideAndReportText: {
    textAlign: "center",
    fontSize: 14,
    fontFamily: "InterTight-SemiBold",
    padding: 5,
    textDecorationLine: "underline",
  },
})

export default PersonCard
