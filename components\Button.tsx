import React from "react"
import {
  TouchableOpacity,
  StyleSheet,
  View,
  ActivityIndicator,
  TextProps,
  ViewProps,
  Image,
} from "react-native"
import { Text } from "./Themed"
import { disabledOpacity } from "./constants"

interface ButtonProps {
  text: string
  iconSource?: any
  iconComponent?: React.JSX.Element
  isTextOnLeft?: boolean
  style?: ViewProps["style"]
  textStyle?: TextProps["style"]
  onPress: () => void
  isLoading?: boolean
  disabled?: boolean
  iconTintColor?: string
}

export const Button = ({
  text,
  iconSource,
  iconComponent,
  isTextOnLeft,
  style,
  textStyle,
  onPress,
  isLoading = false,
  disabled = false,
  iconTintColor,
}: ButtonProps) => {
  const textEl = <Text style={[styles.text, textStyle]}>{text}</Text>
  const imageEl =
    iconComponent || iconSource ? (
      <View
        style={{
          marginLeft: isTextOnLeft ? 8 : 0,
          marginRight: isTextOnLeft ? 0 : 8,
        }}
      >
        {iconComponent ? (
          iconComponent
        ) : (
          <Image
            source={iconSource}
            style={styles.image}
            tintColor={iconTintColor ?? "black"}
          />
        )}
      </View>
    ) : null
  return (
    <View style={{ alignItems: "center" }}>
      <TouchableOpacity
        style={[
          {
            ...styles.innerContainer,
            opacity: disabled ? disabledOpacity : 1,
          },
          style,
        ]}
        onPress={onPress}
        disabled={isLoading || disabled}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : isTextOnLeft ? (
          <>
            {textEl}
            {imageEl}
          </>
        ) : (
          <>
            {imageEl}
            {textEl}
          </>
        )}
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  innerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 32,
    paddingVertical: 16,
    paddingHorizontal: 20,
    width: 353,
    height: 56,
    backgroundColor: "black",
  },
  image: {
    width: 24,
    height: 24,
  },
  text: {
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
    letterSpacing: 0.39,
    color: "white",
  },
})
