import { Text } from "react-native"
import Badge from "@/components/profile/Badge"
import { Screen } from "@/components/Themed"
import _ from "lodash"
import { Level } from "@/types/levels"
import { S3_URL } from "@/constants/Links"

export const LEVELS: Level[] = [
  {
    id: 1,
    name: "Newbie",
    place: 1,
    color: "#FFA180",
    pointsRequired: 30,
    description:
      "Gain access to 5 daily Leads! Article ratings and daily streaks are the trick.",
    levelUpTitle: "Welcome, Newbie!",
    levelUpSubtitle:
      "You’ve gained access to 5 daily Leads! Rate 2 more articles to unlock 10 daily Leads!",
    badgeUrl: `${S3_URL}/levels/newbie-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/newbie-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/newbie-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-newbie-on-inpress.png`,
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    place: 2,
    color: "#A8FFC0",
    pointsRequired: 100,
    description:
      "You’re unlocking 20 daily Leads! Keep growing sharper and aim for Explorer!",
    levelUpTitle: "Nice, Learner!",
    levelUpSubtitle: "Nice job, keep it up! Go go go!",
    badgeUrl: `${S3_URL}/levels/learner-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/learner-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/learner-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-learner-on-inpress.png`,
  },
  {
    id: 3,
    name: "Explorer",
    place: 3,
    color: "#E0E6C3",
    pointsRequired: 250,
    description:
      "You’re on the Explorer journey! Keep climbing for deeper Lead insights!",
    levelUpTitle: "You’re an Explorer!",
    levelUpSubtitle: "You’re doing great! Keep it up!",
    badgeUrl: `${S3_URL}/levels/explorer-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/explorer-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/explorer-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-explorer-on-inpress.png`,
  },
  {
    id: 4,
    name: "Thinker",
    place: 4,
    color: "#E18260",
    pointsRequired: 750,
    description:
      "Thinker on the rise! Your Top Shared Interests get smarter—next up, Analyst!",
    levelUpTitle: "Thinker Achieved!",
    levelUpSubtitle:
      "Big brain energy unlocked! At this rate your Top Shared Interests are going to be *chef's kiss*",
    badgeUrl: `${S3_URL}/levels/thinker-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/thinker-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/thinker-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-thinker-on-inpress.png`,
  },
  {
    id: 5,
    name: "Analyst",
    place: 5,
    color: "#A7877D",
    pointsRequired: 1500,
    description:
      "Analyst in action! Your insights are definitely something Leads crave.",
    levelUpTitle: "Analyst in Action!",
    levelUpSubtitle:
      "Numbers, patterns, trends—you see it all. You're almost an InPress Scholar...the final 3 levels!",
    badgeUrl: `${S3_URL}/levels/analyst-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/analyst-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/analyst-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-analyst-on-inpress.png`,
  },
  {
    id: 6,
    name: "Scholar",
    place: 6,
    color: "#E1882F",
    pointsRequired: 3000,
    description:
      "You’re a Scholar! Among the best-read on InPress. Sage wisdom awaits next!",
    levelUpTitle: "We see you, Scholar!",
    levelUpSubtitle:
      "Your wisdom intimidates us, honestly. Share this and tell everyone how smart you are.",
    badgeUrl: `${S3_URL}/levels/scholar-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/scholar-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/scholar-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-scholar-on-inpress.png`,
  },
  {
    id: 7,
    name: "Sage",
    place: 7,
    color: "#C9C9C9",
    pointsRequired: 6000,
    description:
      "Your wisdom is unmatched, Sage. You're almost among the best—GOAT!",
    levelUpTitle: "You’re a Sage!",
    levelUpSubtitle:
      "Your profile is now an actual flex. Wield your knowledge responsibly, oh wise one.",
    badgeUrl: `${S3_URL}/levels/sage-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/sage-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/sage-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-sage-on-inpress.png`,
  },
  {
    id: 8,
    name: "G.O.A.T",
    place: 8,
    color: "#F4D463",
    pointsRequired: 10000,
    description:
      "You're G.O.A.T.'d! InPress royalty, congrats on being the wisest here!",
    levelUpTitle: "God tier: G.O.A.T.",
    levelUpSubtitle:
      "You are not just informed...you are legendary. Books will be written of you. Now, ascend.",
    badgeUrl: `${S3_URL}/levels/goat-badge.png`,
    grayscaleBadgeUrl: `${S3_URL}/levels/goat-badge-grayscale.png`,
    iconUrl: `${S3_URL}/levels/goat-icon.png`,
    shareableUrl: `${S3_URL}/levels/level-up-shareables/i-leveled-up-to-goat-on-inpress.png`,
  },
]

export default function Story() {
  return (
    <Screen style={{ gap: 20 }}>
      <Text>Badge Variant:</Text>
      <Badge points={150} variant="badge" />
      <Text>Icon Variant:</Text>
      <Badge points={150} variant="icon" />
      <Text>Points Variant (no points):</Text>
      <Badge points={0} variant="points" />
      <Text>Points Variant (some points):</Text>
      <Badge points={150} variant="points" />
      <Text>Points Variant (no level):</Text>
      <Badge points={10} variant="points" />
      <Text>Points Variant (many points):</Text>
      <Badge points={6795} variant="points" />
      <Text>Points Variant (final level)</Text>
      <Badge
        points={(_.maxBy(LEVELS, "pointsRequired")?.pointsRequired || 0) + 10}
        variant="points"
      />
    </Screen>
  )
}
