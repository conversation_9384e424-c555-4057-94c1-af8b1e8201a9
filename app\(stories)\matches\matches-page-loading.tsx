import { USER } from "../(account)/account"
import { MatchesPage_ } from "@/components/MatchesPage"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"

export default function Story() {
  return (
    <MatchesPage_
      matches={undefined}
      activeConnectionMode={ConnectionMode.Dates}
      user={USER}
      testMode={true}
      onSelectChannel={async () => {}}
    />
  )
}
