import { LatLng } from "react-native-maps"

export const washingtonDC = {
  latitude: 38.9072,
  longitude: -77.0369,
}

export const usaBoundingBox = {
  northWest: { latitude: 49.3457868, longitude: -124.7844079 },
  northEast: { latitude: 49.3457868, longitude: -66.93457 },
  southWest: { latitude: 24.396308, longitude: -124.7844079 },
  southEast: { latitude: 24.396308, longitude: -66.93457 },
}

export const isInTheUSA = (latLng: LatLng): boolean => {
  const { northWest, southEast } = usaBoundingBox

  const isWithinLatitude =
    latLng.latitude <= northWest.latitude &&
    latLng.latitude >= southEast.latitude

  const isWithinLongitude =
    latLng.longitude >= northWest.longitude &&
    latLng.longitude <= southEast.longitude

  return isWithinLatitude && isWithinLongitude
}

export function calculateDistanceInMiles(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number {
  const toRadians = (degree: number): number => (degree * Math.PI) / 180
  const R = 3958.8

  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2)

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  const distMiles = +(R * c).toFixed(0)

  return distMiles
}
