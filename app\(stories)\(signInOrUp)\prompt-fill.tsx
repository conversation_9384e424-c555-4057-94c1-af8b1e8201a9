import FillPrompt from "@/components/signInOrUp/promptStep/FillPrompt"
import { promptChoices } from "./prompt-select"

export default function Story() {
  return (
    <FillPrompt
      initialResponse={{
        position: 0,
        promptId: promptChoices[0].id,
        prompt: promptChoices[0].text,
        text: "My favorite color is blue",
      }}
      onChangeResponse={(promptResponse) => {
        console.log(promptResponse)
      }}
      onChangePrompt={(promptResponse) => {
        console.log(promptResponse)
      }}
    />
  )
}
