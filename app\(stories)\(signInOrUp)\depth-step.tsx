import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { useHideHeader } from "../story_utils"
import { router } from "expo-router"
import { DepthStep, SUBTITLE, TITLE } from "@/components/signInOrUp/DepthStep"

export default function Story() {
  useHideHeader()

  return (
    <SignUpWrapper
      title={TITLE}
      subtitle={SUBTITLE}
      progress={0.9}
      onBack={() => router.back()}
      onNext={() => {}}
    >
      <DepthStep />
    </SignUpWrapper>
  )
}
