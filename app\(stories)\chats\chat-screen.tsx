import { ChatScreen_ } from "@/components/ChatScreen"
import { View, Text } from "react-native"
import { USER } from "../(account)/account"

export default function Story() {
  return (
    <ChatScreen_
      thisUser={USER}
      match={{
        user: USER,
        score: 0.3,
        topics: ["topic1", "topic2"],
        chatChannelId: "channelId",
      }}
      channelComponent={
        <View>
          <Text>Chat goes here</Text>
        </View>
      }
      onUnmatch={() => console.log("Unmatched")}
      onReportAndBlock={() => console.log("Reported and blocked")}
    />
  )
}
