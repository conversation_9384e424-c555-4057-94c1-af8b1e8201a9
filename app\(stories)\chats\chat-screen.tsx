import { ChatScreen_ } from "@/components/ChatScreen"
import { View, Text } from "react-native"
import { USER } from "../(account)/account"
import { LEADS } from "../leads/leads"

export default function Story() {
  return (
    <ChatScreen_
      thisUser={USER}
      match={{
        user: USER,
        score: 0.3,
        topics: LEADS[0].topics,
        chatChannelId: "channelId",
      }}
      channelComponent={
        <View>
          <Text>Chat goes here</Text>
        </View>
      }
      onUnmatch={() => console.log("Unmatched")}
      onReportAndBlock={() => console.log("Reported and blocked")}
    />
  )
}
