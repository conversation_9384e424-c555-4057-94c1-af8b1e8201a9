import StatsBar from "@/components/news/statsBar/StatsBar"
import { Screen } from "@/components/Themed"
import { LevelsProvider } from "@/context/LevelContext"
import { LEVELS_PROVIDER_PROPS } from "../../constants/levels"
import _ from "lodash"

export default function Story() {
  const props = {
    ...LEVELS_PROVIDER_PROPS,
    initialData: {
      ...LEVELS_PROVIDER_PROPS.initialData,
      stats: { points: 90, streakDays: 14, articlesRead: 0, articlesRated: 0 },
    },
  }
  return (
    <LevelsProvider {...props}>
      <Screen style={{ justifyContent: "center" }}>
        <StatsBar />
      </Screen>
    </LevelsProvider>
  )
}
