import { Screen } from "@/components/Themed"
import { newUpdateAlert } from "@/components/news/alerts"
import { NewsFeedAlert } from "@/components/news/AlertCard"
import WelcomeDrawer from "@/components/news/WelcomeDrawer"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { BEIGE } from "@/constants/Colors"

export const alerts: NewsFeedAlert[] = [newUpdateAlert]

export default function Story() {
  return (
    <Screen style={{ backgroundColor: BEIGE }}>
      <ScreenHeader title={"Welcome to News!"} />
      <WelcomeDrawer onClose={() => {}} />
    </Screen>
  )
}
