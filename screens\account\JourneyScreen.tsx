import Colors, { <PERSON>EI<PERSON>, DARK_GREY, MEDIUM_GREY } from "@/constants/Colors"
import { useLevels } from "@/context/LevelContext"
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  SectionList,
} from "react-native"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { LinearGradient } from "expo-linear-gradient"
import { Avatar } from "@/components/widgets/Avatar"
import {
  isUserWithProfile,
  LeaderboardUser,
  UnknownTypeUser,
} from "@/types/user"
import { pickImage } from "@/utils/images"
import { Image } from "expo-image"
import { TimedImage } from "@/components/TrackedImage"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { router } from "expo-router"
import InScoreSection from "@/components/leads/InScoreSection"
import { Leaderboard } from "@/components/ratings/Leaderboard"
import { StatusBar } from "expo-status-bar"
import { fontStyles } from "@/styles"
import { Level, LevelStats } from "@/types/levels"

type JourneyScreenProps = Omit<LevelStats, "points"> & {
  user: UnknownTypeUser
  leaderboardUsers: LeaderboardUser[]
  onUpdatePicture: (imageUri: string) => Promise<void>
}

export const JourneyScreen = ({
  user,
  articlesRead,
  articlesRated,
  streakDays,
  leaderboardUsers,
  onUpdatePicture,
}: JourneyScreenProps) => {
  const { levels, loading, calculateLevel, calculateNextLevel } = useLevels()
  const { top } = useSafeAreaInsets()

  if (loading || !levels) return null

  const { points } = user
  const currentLevel = calculateLevel(points)
  const nextLevel = calculateNextLevel(points)

  const handleCameraPress = async () => {
    const uri = await pickImage()
    if (uri) await onUpdatePicture(uri)
  }

  const userImageUrl = user.images[0]?.url

  const renderLevelItem = ({ item: level }: { item: Level }) => {
    const isPassed = points >= level.pointsRequired
    return (
      <View style={styles.levelItem}>
        <View style={styles.levelBadgeContainer}>
          {isPassed ? (
            <Image
              source={{ uri: level.badgeUrl }}
              style={styles.bigBadgeStyle}
            />
          ) : (
            <View style={styles.grayImageContainer}>
              <Image
                source={
                  level.place > 6
                    ? require("@/assets/images/higher-badge.png")
                    : require("@/assets/images/lower-badge.png")
                }
                style={styles.bigBadgeStyle}
              />
              <Text style={styles.pointsRequiredText}>
                {level.pointsRequired.toLocaleString()}
              </Text>
            </View>
          )}
          <Text
            style={[
              styles.levelName,
              { color: isPassed ? "white" : MEDIUM_GREY },
            ]}
          >
            {level.name}
          </Text>
        </View>
      </View>
    )
  }

  const renderPictureSection = () => {
    return (
      <>
        <View style={styles.profilePicContainer}>
          <TouchableOpacity
            onPress={() => router.canGoBack() && router.back()}
            style={[styles.backButton, { top: top + 50 }]}
          >
            <Image
              source={require("../../assets/images/leftArrow.png")}
              style={styles.backIcon}
            />
          </TouchableOpacity>
          {userImageUrl ? (
            <View style={styles.profileImgContainer}>
              <LinearGradient
                colors={["#000000CC", "#00000000"]}
                style={[styles.topLinearGradient, { height: top + 54 }]}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
              />
              <TimedImage
                name="avatar-image"
                source={{ uri: userImageUrl }}
                style={styles.profilePic}
              />
              <LinearGradient
                colors={[`${BEIGE}00`, BEIGE]}
                style={styles.linearGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
              >
                <Text numberOfLines={1} style={styles.userName}>
                  {user.firstName}
                </Text>
              </LinearGradient>
            </View>
          ) : (
            <View style={styles.avatarWrapper}>
              <Avatar
                user={user}
                isEditable={!isUserWithProfile(user)}
                handleCameraPress={handleCameraPress}
              />
            </View>
          )}
        </View>

        {!userImageUrl && (
          <LinearGradient
            colors={["#ffffff22", "#00000033", "#00000088"]}
            style={styles.linearGradient}
          />
        )}
      </>
    )
  }

  const renderContent = () => {
    return (
      <View style={styles.contentContainer}>
        {[
          {
            name: "InScore",
            component: (
              <InScoreSection
                points={points}
                currentLevel={currentLevel}
                nextLevel={nextLevel}
                articlesRead={articlesRead}
                articlesRated={articlesRated}
                streakDays={streakDays}
              />
            ),
          },
          {
            name: "Local Leaderboard",
            component: (
              <Leaderboard
                users={leaderboardUsers!}
                currentUserId={user.id}
                showLargeVersion={true}
              />
            ),
          },
          {
            name: "Levels",
            component: (
              <FlatList
                data={levels}
                renderItem={renderLevelItem}
                keyExtractor={(item) => item.id.toString()}
                numColumns={4}
                scrollEnabled={false}
                columnWrapperStyle={{ gap: 12 }}
                style={styles.flatListContent}
              />
            ),
          },
        ].map((item) => (
          <View key={item.name} style={{ flex: 1 }}>
            <Text style={styles.contentSectionTitle}>{item.name}</Text>
            {item.component}
          </View>
        ))}
      </View>
    )
  }

  return (
    <>
      <StatusBar style="light" />
      <SectionList
        sections={[
          { title: "profile", data: ["profile"] },
          { title: "content", data: ["content"] },
        ]}
        keyExtractor={(item) => item}
        renderItem={({ item, section }) => {
          if (section.title === "profile") {
            return renderPictureSection()
          } else {
            return renderContent()
          }
        }}
        renderSectionHeader={() => null}
        contentContainerStyle={{ flexGrow: 1 }}
        bounces={false}
      />
    </>
  )
}

const BADGE_WIDTH = 23

const styles = StyleSheet.create({
  profilePicContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  profileImgContainer: {
    width: "100%",
    height: 393,
  },
  topLinearGradient: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    zIndex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  linearGradient: {
    height: 80,
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    paddingHorizontal: 20,
    justifyContent: "flex-end",
  },
  avatarWrapper: {
    marginVertical: 30,
  },
  userName: {
    fontSize: 48,
    color: DARK_GREY,
    zIndex: 1,
    ...fontStyles.editorial,
  },
  contentContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
    padding: 20,
    gap: 40,
  },
  contentSectionTitle: {
    fontSize: 24,
    textAlign: "left",
    marginBottom: 8,
    ...fontStyles.editorial,
  },
  statsContainer: {
    flex: 1,
    backgroundColor: DARK_GREY,
    borderRadius: 22,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  progressContainer: {
    flexDirection: "row",
    flex: 1,
    marginTop: 5,
    alignSelf: "center",
  },
  progressBadge: {
    width: BADGE_WIDTH,
    height: 27,
    marginTop: 7,
    resizeMode: "contain",
    zIndex: 1,
  },
  progressText: {
    textAlign: "center",
    color: "white",
    fontSize: 11,
  },
  progressBar: {
    width: wp(70),
    height: 10,
    backgroundColor: "white",
  },
  cardsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  flatListContent: {
    backgroundColor: DARK_GREY,
    paddingVertical: 24,
    gap: 12,
    paddingHorizontal: 14,
    borderRadius: 10,
  },
  levelItem: {
    flex: 1,
  },
  levelBadgeContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  bigBadgeStyle: {
    width: wp(18),
    height: 80,
    resizeMode: "contain",
  },
  levelName: {
    fontSize: 12,
    textAlign: "center",
    fontFamily: "InterTight-Regular",
  },
  profilePic: {
    width: "100%",
    height: 393,
    resizeMode: "cover",
  },
  iconStyle: {
    width: 18,
    height: 18,
    resizeMode: "contain",
  },
  grayImageContainer: {
    alignItems: "center",
  },
  pointsRequiredText: {
    position: "absolute",
    top: 27,
    fontSize: 13,
    ...fontStyles.editorial,
  },
  backIconContainer: {
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 100,
    height: 36,
    width: 36,
  },
  backIcon: {
    width: 20,
    height: 20,
  },
  backButton: {
    height: 36,
    width: 36,
    position: "absolute",
    zIndex: 99,
    left: 20,
    right: 0,
  },
})
