import { getMatches, getNewsArticles } from "@/apiQueries/apiQueries"
import { login } from "@/apiQueries/auth"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import dotenv from "dotenv"
dotenv.config({ path: ".env.test.local" })

export async function getSession() {
  const { INPRESS_EMAIL, INPRESS_PASSWORD } = process.env

  if (!INPRESS_EMAIL || !INPRESS_PASSWORD) {
    throw new Error("INPRESS_EMAIL and INPRESS_PASSWORD must be set in .env")
  }
  return await login({
    email: INPRESS_EMAIL,
    password: INPRESS_PASSWORD,
  })
}

describe("API queries", () => {
  it("gets news articles", async () => {
    const session = await getSession()
    if (!session) {
      throw new Error("No session")
    }
    const newsArticles = await getNewsArticles(session.token)
    console.log(`Got ${newsArticles.length} news articles`)
    expect(newsArticles.length).toBeGreaterThan(0)
  })

  it("gets matches", async () => {
    const session = await getSession()
    if (!session) {
      throw new Error("No session")
    }
    const matches = await getMatches({
      token: session.token,
      connectionMode: ConnectionMode.Dates,
    })
    console.log(`Got ${matches.length} matches`)
    expect(matches.length).toBeGreaterThan(0)
  })
})
