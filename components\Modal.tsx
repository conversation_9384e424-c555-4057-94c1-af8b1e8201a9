import React from "react"
import {
  View,
  StyleSheet,
  Modal as RNModal,
  ModalProps as RNModalProps,
  ViewStyle,
  TouchableOpacity,
} from "react-native"

interface ModalProps {
  visible: boolean
  containerStyle?: RNModalProps["style"]
  children: React.ReactNode
  modalProps?: RNModalProps
  overlayStyle?: ViewStyle | ViewStyle[]
  onPressOverlay?: () => void
}

export const Modal = ({
  visible,
  containerStyle,
  children,
  overlayStyle = {},
  modalProps = {},
  onPressOverlay,
}: ModalProps) => {
  return (
    <RNModal
      visible={visible}
      transparent={true}
      animationType="slide"
      {...modalProps}
    >
      <TouchableOpacity
        activeOpacity={1}
        style={[styles.overlay, overlayStyle]}
        onPress={() => onPressOverlay?.()}
      >
        <View style={[styles.container, containerStyle]}>{children}</View>
      </TouchableOpacity>
    </RNModal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  container: {
    width: "80%",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    alignItems: "center",
    zIndex: 1,
  },
})
