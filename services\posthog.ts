import { isImpersonating } from "@/utils/general"
import { Platform } from "react-native"

const posthogApi<PERSON>ey = process.env.EXPO_PUBLIC_POSTHOG_API_KEY

export const shouldTrackWithPosthog = !!posthogApiKey && !isImpersonating

let posthog: any

if (Platform.OS === "web") {
  posthog = {
    capture: () => {},
    identify: () => {},
    reset: () => {},
    flush: () => {},
    shutdown: () => {},
    alias: () => {},
    group: () => {},
    register: () => {},
    unregister: () => {},
    getDistinctId: () => "web-mock-id",
    isFeatureEnabled: () => false,
    getFeatureFlag: () => false,
    reloadFeatureFlags: () => {},
    onFeatureFlags: () => {},
  }
} else {
  const PostHog = require("posthog-react-native").default
  posthog = new PostHog(posthogApiKey ?? "fake_key", {
    host: "https://us.i.posthog.com",
    disabled: !shouldTrackWithPosthog,
  })
}

export { posthog }
