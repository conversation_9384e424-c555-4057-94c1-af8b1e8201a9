import { View, StyleSheet, Text } from "react-native"

import { Icon } from "react-native-paper"
import { AntDesign } from "@expo/vector-icons"

import { Image } from "expo-image"
import { IMAGES } from "@/constants/Images"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { Article } from "@/types/news"

type ReadRatedCover = {
  article: Article
  rightOrientRated?: boolean
}

export const ReadRatedCover = ({
  article,
  rightOrientRated,
}: ReadRatedCover) => {
  const { isOpened, isSurveyed, gistRating } = article

  if (!isOpened && !isSurveyed && !gistRating) return null

  const RenderFooter = ({
    icon,
    text,
  }: {
    icon: React.ReactNode
    text: string
  }) => {
    return (
      <View
        style={[
          styles.ratingIconStyle,
          rightOrientRated && { right: 10, left: undefined },
        ]}
      >
        {icon}
        <Text style={styles.label}>{text}</Text>
      </View>
    )
  }

  return (
    <>
      <View style={styles.emptyContainer} />

      {isOpened && (
        <View style={styles.iconContainer}>
          <AntDesign name="eye" size={24} color="white" />
        </View>
      )}

      {isSurveyed ? (
        <RenderFooter
          icon={<Icon source={"star"} size={18} color="white" />}
          text={"Rated"}
        />
      ) : gistRating ? (
        <RenderFooter
          icon={
            <Image source={IMAGES.HeartHalf} style={styles.halfHeartIcon} />
          }
          text="Gist-Rated"
        />
      ) : null}
    </>
  )
}

const styles = StyleSheet.create({
  halfHeartIcon: {
    width: wp(3.2),
    height: wp(3.2),
    resizeMode: "contain",
  },
  label: {
    color: "white",
    fontFamily: "Inter-Regular",
    fontSize: 10,
  },
  emptyContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "black",
    zIndex: 1,
    opacity: 0.5,
    borderRadius: 8,
  },
  iconContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 2,
    alignItems: "center",
    justifyContent: "center",
  },
  ratingIconStyle: {
    position: "absolute",
    zIndex: 2,
    left: 10,
    bottom: 10,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
})
