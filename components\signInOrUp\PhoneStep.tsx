import { Text, View } from "../Themed"
import PhoneInput from "../PhoneInput"
import { StyleSheet } from "react-native"

export default function PhoneStep({
  phoneNumber,
  onChange,
}: {
  phoneNumber?: string
  onChange: (phoneNumber: string) => void
}) {
  if (phoneNumber?.startsWith("+1")) {
    phoneNumber = phoneNumber.slice(2)
  }

  const handleChange = (value: string) => {
    onChange(`+1${value}`)
  }

  return (
    <View style={{ gap: 12 }}>
      <Text style={{ fontSize: 16 }}>
        InPress will send you a text with a verification code. Message and data
        rates may apply. Only US phone numbers are supported at this time.
      </Text>
      <View>
        <View style={styles.inputWrapper}>
          <Text style={styles.countryCode}>+1</Text>
          <PhoneInput value={phoneNumber} onChange={handleChange} />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 16,
    paddingRight: 30,
  },
  countryCode: {
    fontSize: 18,
    marginBottom: 8,
  },
})
