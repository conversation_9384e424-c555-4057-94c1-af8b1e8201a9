import {
  <PERSON><PERSON>,
  Mo<PERSON>,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native"
import { useEffect, useState } from "react"
import { ActiveConnectionMode } from "@/context/ModeContext"
import {
  SAGE_BRIGHT,
  datesModePalette,
  friendsModePalette,
} from "@/constants/Colors"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"
import { Button } from "../Button"
import { Ionicons } from "@expo/vector-icons"
import SharedInterestsPanel from "./SharedInterestsPanel"
import { Loader } from "../widgets/Loader"
import { getScannedMatch } from "@/apiQueries/scannedMatch"
import { useSession } from "@/ctx"
import { EventType, trackEvent } from "@/utils/tracking"
import { createConnectRequestForUserId } from "@/apiQueries/connectRequests"
import _ from "lodash"
import { isUserWithProfile, UnknownTypeUser } from "@/types/user"
import { Avatar } from "../widgets/Avatar"
import { fontStyles } from "@/styles"
import { Topic } from "@/types/social"

type ScannedMatch = {
  thisUser: UnknownTypeUser
  otherUser: UnknownTypeUser
  score?: number
  topics: Topic[]
  alreadyConnected: boolean
}

type ScannedMatchModalProps = {
  visible: boolean
  otherUserId: number
  onClose: () => void
}

const ScannedMatchModal = ({
  visible,
  otherUserId,
  onClose,
}: ScannedMatchModalProps) => {
  const [scannedMatch, setScannedMatch] = useState<ScannedMatch | null>(null)
  const { session } = useSession()

  const refreshScannedMatch = async () => {
    const match = await getScannedMatch({
      token: session!.token,
      otherUserId: otherUserId,
    })
    setScannedMatch(match)
    trackEvent(EventType.ScannedMatchSeen, {
      data: {
        otherUserId: match.otherUser.id,
        topics: match.topics,
        alreadyConnected: match.alreadyConnected,
      },
    })
  }

  const handleSendRequest = async () => {
    await createConnectRequestForUserId({
      token: session!.token,
      otherUserId,
    })

    Alert.alert("Friend request sent!")
  }

  useEffect(() => {
    refreshScannedMatch()
  }, [])

  if (!scannedMatch) {
    return <Loader />
  }

  return (
    <ScannedMatchModal_
      visible={visible}
      scannedMatch={scannedMatch}
      connectionMode={ConnectionMode.Friends}
      onSendFriendRequest={() => handleSendRequest()}
      onClose={onClose}
    />
  )
}

interface ScannedMatchModalProps_ {
  visible: boolean
  scannedMatch: ScannedMatch
  connectionMode: ActiveConnectionMode
  onSendFriendRequest: () => void
  onClose: () => void
}

const ScannedMatchModal_ = ({
  visible,
  scannedMatch,
  connectionMode,
  onSendFriendRequest,
  onClose,
}: ScannedMatchModalProps_) => {
  const { thisUser, otherUser, topics, alreadyConnected } = scannedMatch

  const backgroundColor =
    connectionMode === ConnectionMode.Dates
      ? datesModePalette.backgroundColor
      : friendsModePalette.backgroundColor

  return (
    <Modal
      visible={visible}
      style={{
        backgroundColor: backgroundColor,
      }}
      statusBarTranslucent
    >
      <View style={[styles.container, { backgroundColor: backgroundColor }]}>
        <TouchableOpacity style={styles.closeIconContainer} onPress={onClose}>
          <Ionicons name="close" size={24} color="black" />
        </TouchableOpacity>
        <View style={styles.matchesUserContainer}>
          <View style={styles.currentUserProfileContainer}>
            <Avatar user={thisUser} size={114} />
          </View>
          <View style={styles.matchesUserProfileContainer}>
            <Avatar user={otherUser} size={114} />
          </View>
          {!_.isNil(scannedMatch.score) && (
            <View style={styles.matchPercentage}>
              <Text style={styles.matchPercentageText}>
                {Math.round(scannedMatch.score * 100)}%
              </Text>
            </View>
          )}
        </View>
        <View style={{ alignItems: "center" }}>
          <Text style={styles.header}>{otherUser.firstName} +</Text>
          <Text style={[styles.header, { includeFontPadding: false }]}>
            {thisUser.firstName}
          </Text>
        </View>
        {topics.length > 0 ? (
          <View style={{ marginTop: 10 }}>
            <SharedInterestsPanel topics={topics} horizontallyCentered={true} />
          </View>
        ) : (
          <Text style={styles.body}>
            We don't have enough ratings to build your shared interests yet.
            Rate a few articles to see them.
          </Text>
        )}

        {isUserWithProfile(thisUser) && isUserWithProfile(otherUser) && (
          <View style={{ alignItems: "center" }}>
            {!thisUser.friendsModeIsActivated ? (
              <>
                <Text style={styles.errorText}>
                  To send this person a friend request we'll activate friends
                  mode for you and copy the details from your Dating profile to
                  your Friends profile.
                </Text>
              </>
            ) : null}

            {!alreadyConnected ? (
              <View style={styles.buttonsContainer}>
                <Button
                  text="Send friend request"
                  isTextOnLeft={true}
                  iconComponent={
                    <Ionicons name="chatbubble" size={24} color="white" />
                  }
                  onPress={onSendFriendRequest}
                />
                <Button
                  text="Back to Profile"
                  style={styles.outlinedButton}
                  textStyle={{ color: "black" }}
                  onPress={onClose}
                />
              </View>
            ) : (
              <Text style={styles.errorText}>You're already friends!</Text>
            )}
          </View>
        )}
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 16,
    gap: 15,
  },
  header: {
    fontSize: 52,
    ...fontStyles.editorial,
  },
  errorText: {
    fontFamily: "InterTight-Regular",
    marginVertical: 5,
    textAlign: "center",
  },
  body: {
    textAlign: "center",
  },
  button: {
    width: 320,
  },
  outlinedButton: {
    marginTop: 8,
    backgroundColor: "transparent",
    borderWidth: 1.5,
    color: "black",
  },
  closeIconContainer: {
    width: "100%",
    height: 50,
    position: "absolute",
    top: 50,
    justifyContent: "center",
    alignItems: "flex-end",
  },
  matchesUserContainer: {
    width: 50,
    flexDirection: "row",
  },
  currentUserProfileContainer: {
    backgroundColor: "white",
    borderRadius: 70,
    borderColor: SAGE_BRIGHT,
    borderWidth: 4,
    height: 120,
    width: 120,
    zIndex: -100,
    marginLeft: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  matchesUserProfileContainer: {
    backgroundColor: "white",
    borderRadius: 70,
    borderColor: SAGE_BRIGHT,
    borderWidth: 4,
    height: 120,
    width: 120,
    position: "absolute",
    right: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  matchPercentage: {
    backgroundColor: SAGE_BRIGHT,
    borderRadius: 70,
    paddingHorizontal: 15,
    position: "absolute",
    left: "50%",
    transform: [{ translateX: -40 }],
    bottom: -10,
  },
  matchPercentageText: {
    fontSize: 28,
    ...fontStyles.editorial,
  },
  buttonsContainer: {
    gap: 10,
    marginTop: 30,
  },
})

export default ScannedMatchModal
export { ScannedMatch, ScannedMatchModal_, ScannedMatchModalProps_ }
