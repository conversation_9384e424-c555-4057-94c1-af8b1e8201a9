import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { Text } from "../Themed"
import { StyleSheet, Vibration, View } from "react-native"
import { ProgressBar } from "react-native-paper"
import _ from "lodash"
import { useLevels } from "@/context/LevelContext"
import { useEffect, useMemo, useState } from "react"
import { LevelUpStep } from "./LevelUpStep"
import { completionStepStyles } from "./styles"
import { CheckProgressButton } from "./CheckProgressButton"
import { progressBarStyle } from "../widgets/styles"
import { StreakStep } from "./StreakStep"
import { ProgressBadge } from "../profile/ProgressBadge"
import { RollingPoints } from "../levels/RollingPoints"
import { SoundAsset } from "@/utils/sounds"
import { useFeatureFlag } from "@/utils/featureFlags"
import { LeaderboardStep } from "./LeaderboardStep"
import { PointEvent } from "@/types/levels"
import { LeaderboardProps } from "./Leaderboard"

type CelebrationStep = "levelUp" | "streak" | "leaderboard"

export type PointsCompletionStepProps = {
  initialPoints: number
  pointEvents: PointEvent[] | undefined
  soundAssets: SoundAsset[]
  leaderboardUsers: LeaderboardProps["users"] | undefined
  soundAndHapticsOn: boolean
}

export const PointsCompletionStep = ({
  initialPoints,
  pointEvents,
  soundAssets,
  leaderboardUsers,
  soundAndHapticsOn,
}: PointsCompletionStepProps) => {
  const soundAndHapticsFlag = useFeatureFlag("rating_sounds_and_haptics")
  const leaderboardFlag = useFeatureFlag("leaderboard")

  const { loading, calculateLevel, calculateNextLevel } = useLevels()
  const [progress, setProgress] = useState<number | undefined>()
  const [eventCount, setEventCount] = useState(0)
  const [celebrationStep, setCelebrationStep] = useState<CelebrationStep>()
  const playSoundsAndHaptics = soundAndHapticsOn && soundAndHapticsFlag

  const points = useMemo(() => {
    return (
      initialPoints +
      (pointEvents ? _.sumBy(pointEvents.slice(0, eventCount), "points") : 0)
    )
  }, [initialPoints, pointEvents, eventCount])

  const initialLevel = calculateLevel(initialPoints)
  const initialNextLevel = calculateNextLevel(initialPoints)

  useEffect(() => {
    const ANIMATION_DELAY = 750
    const min = initialLevel?.pointsRequired || 0
    const currentLevel = calculateLevel(points)

    setProgress(
      initialLevel?.id !== currentLevel?.id || !initialNextLevel
        ? 1
        : (points - min) / (initialNextLevel.pointsRequired - min),
    )

    if (pointEvents) {
      if (eventCount < pointEvents.length) {
        const interval = setInterval(() => {
          if (playSoundsAndHaptics) {
            soundAssets
              .find((a) => a.type === `points-gained-${eventCount + 1}`)!
              .sound.playAsync()
            Vibration.vibrate(50)
          }
          setEventCount(eventCount + 1)
        }, ANIMATION_DELAY)
        return () => clearInterval(interval)
      } else {
        let celebrationStep: CelebrationStep | undefined
        if (initialLevel?.id !== currentLevel?.id) {
          celebrationStep = "levelUp"
        } else if (_.some(pointEvents, { type: "streak_bonus" })) {
          celebrationStep = "streak"
        } else if (leaderboardUsers) {
          celebrationStep = "leaderboard"
        }

        if (celebrationStep) {
          const animationTimeout = setTimeout(() => {
            if (celebrationStep === "levelUp") {
              if (playSoundsAndHaptics) {
                soundAssets
                  .find((a) => a.type === "level-up")!
                  .sound.playAsync()
                Vibration.vibrate(500)
              }
            }
            setCelebrationStep(celebrationStep)
          }, ANIMATION_DELAY)
          return () => {
            clearInterval(animationTimeout)
          }
        }
      }
    }
  }, [pointEvents, eventCount])

  if (loading) return null

  if (celebrationStep === "levelUp") {
    return <LevelUpStep points={points} />
  } else if (celebrationStep === "streak") {
    const streak = pointEvents!.find((e) => e.type === "streak_bonus")!
    return <StreakStep streakType={streak.subtype!} />
  } else if (leaderboardFlag && celebrationStep === "leaderboard") {
    return <LeaderboardStep users={leaderboardUsers!} />
  }

  return (
    <View style={completionStepStyles.container}>
      <Text style={completionStepStyles.title}>
        {initialLevel
          ? `Level ${initialLevel.place}: ${initialLevel.name}`
          : "Almost a Newbie!"}
      </Text>
      <Text style={styles.subtitle}>
        {_.isNil(pointEvents)
          ? ""
          : pointEvents.length
          ? `You just upped your InScore!`
          : "You already rated this article"}
      </Text>
      <View style={styles.progressContainer}>
        <View>
          <ProgressBar
            progress={progress}
            fillStyle={{
              backgroundColor: initialNextLevel?.color || initialLevel?.color,
              ...progressBarStyle,
            }}
            style={styles.progressBar}
          />
          <RollingPoints pointEvents={pointEvents} eventCount={eventCount} />
        </View>
        <View style={{ marginLeft: -8 }}>
          <ProgressBadge
            initialLevel={initialLevel}
            initialNextLevel={initialNextLevel}
            points={points}
          />
        </View>
      </View>
      <CheckProgressButton />
    </View>
  )
}

export const styles = StyleSheet.create({
  subtitle: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 20,
    marginBottom: 30,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 30,
  },
  progressBar: {
    flex: 1,
    height: 16,
    maxHeight: 16,
    width: wp(55),
    backgroundColor: "black",
    borderRadius: 9,
  },
})
