import {
  StyleSheet,
  View,
  Text,
  StyleProp,
  ViewStyle,
  TextStyle,
} from "react-native"
import { Image } from "expo-image"
import { Article } from "@/types/news"

export default function ArticleSource({
  article,
  containerStyle,
  textStyle,
}: {
  article: Article
  containerStyle?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
}) {
  return (
    <View style={[styles.outletContainer, containerStyle]}>
      {article.faviconUrl && (
        <Image
          style={styles.outletFavicon}
          source={{
            uri: article.faviconUrl,
          }}
        />
      )}
      <Text style={[styles.outletName, textStyle]} numberOfLines={1}>
        {article.source}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  outletContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
    height: 16,
    marginBottom: 14,
  },
  outletName: {
    fontFamily: "InterTight-Regular",
    fontSize: 12,
    marginRight: 20,
  },
  outletFavicon: {
    width: 16,
    height: 16,
    borderRadius: 2.46,
    borderColor: "#E6E6E6",
    borderWidth: 0.5,
  },
})
