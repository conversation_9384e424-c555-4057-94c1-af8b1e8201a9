import AnimatedNumbers from "@/components/AnimatedNumbers"
import { NormalText, SmallText } from "@/components/StyledText"
import { BRIGHT_GREEN } from "@/constants/Colors"
import _ from "lodash"
import { useEffect, useRef, useState } from "react"
import { Animated, StyleSheet, View, Image } from "react-native"

type StatItemProps = {
  icon: any
  value: number
  marker?: string
  diffMarker?: string
}

const StatItem = ({ icon, value, marker, diffMarker }: StatItemProps) => {
  const [displayValue, setDisplayValue] = useState(value)
  const [diff, setDiff] = useState(0)
  const prevValueRef = useRef(value)

  const isMounted = useRef(false)
  const opacity = useRef(new Animated.Value(0)).current
  const translateY = useRef(new Animated.Value(0)).current
  const fadeOutTimeout = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!isMounted.current) {
      setTimeout(() => {
        isMounted.current = true
        prevValueRef.current = value
      }, 100)
      return
    }

    const prev = prevValueRef.current
    const change = _.round(value - prev, 0)

    if (change !== 0) {
      setDiff(change)
      setDisplayValue(value)
      prevValueRef.current = value

      opacity.setValue(0)
      translateY.setValue(0)

      if (fadeOutTimeout.current) {
        clearTimeout(fadeOutTimeout.current)
        fadeOutTimeout.current = null
      }

      if (change > 0) {
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: -10,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          fadeOutTimeout.current = setTimeout(() => {
            Animated.timing(opacity, {
              toValue: 0,
              duration: 300,
              useNativeDriver: true,
            }).start()
          }, 2000)
        })
      }
    }
  }, [value])

  return (
    <View style={styles.container}>
      {typeof icon === "function" ? (
        icon()
      ) : (
        <Image
          source={typeof icon === "string" ? { uri: icon } : icon}
          style={styles.icon}
        />
      )}
      <View style={styles.statRow}>
        <AnimatedNumbers
          animateToNumber={value}
          fontStyle={styles.valueText}
          includeComma
          animationDuration={isMounted.current ? 1000 : 0}
        />
        {marker ? (
          <NormalText style={styles.valueText}>{marker}</NormalText>
        ) : null}
        {diff > 0 && (
          <Animated.View
            style={[
              styles.diffContainer,
              {
                opacity,
                transform: [{ translateY }],
              },
            ]}
          >
            <SmallText style={styles.diffText}>
              {`+${diff}`}
              {diffMarker}
            </SmallText>
          </Animated.View>
        )}
      </View>
    </View>
  )
}

export default StatItem

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
  },
  icon: {
    width: 20,
    height: 20,
  },
  statRow: {
    flexDirection: "row",
  },
  valueText: {
    color: "white",
  },
  diffContainer: {
    position: "absolute",
    top: -3,
    right: -16,
  },
  diffText: {
    color: BRIGHT_GREEN,
    marginLeft: 4,
  },
  value: {
    fontSize: 16,
    fontWeight: "600",
  },
})
