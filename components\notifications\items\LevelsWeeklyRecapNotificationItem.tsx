import { GenericNotificationItem } from "./GenericNotificationItem"
import { LevelsWeeklyRecapNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function LevelsWeeklyRecapNotificationItem({
  item,
}: {
  item: LevelsWeeklyRecapNotification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          This week, you rated {item.ratingsCount} articles, earned{" "}
          {item.pointsEarned} points
          {item.nextLevel ? (
            <NormalText>
              , and grew closer to <BoldText>{item.nextLevel.name}.</BoldText>
            </NormalText>
          ) : (
            `!`
          )}
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
