import { UserSettings } from "@/apiQueries/userSettings"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { AccountScreen_ } from "@/screens/account/AccountScreen"
import { UserWithPrivateData } from "@/types/user"
import _ from "lodash"

export const SETTINGS: UserSettings = {
  autoUpdateLocation: true,
  matchMoodsOptOut: false,
  ratingSoundsAndHaptics: true,
  nonessentialNotificationsDisabled: false,
}

export const USER: UserWithPrivateData = {
  id: 1,
  firstName: "Andy",
  lastName: "<PERSON>",
  phoneNumber: "+**********",
  age: 25,
  latitude: 38.919147,
  longitude: -77.036301,
  biography: "I love to travel and meet new people!",
  connectionMode: ConnectionMode.Dates,
  datesModeIsActivated: true,
  friendsModeIsActivated: false,
  images: _.range(6).map((index) => ({
    id: index,
    path: "storage/images/profile3.jpg",
    url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male3.jpg",
  })),
  preferences: {
    genders: ["female", "nonbinary"],
    minAge: 21,
    maxAge: 30,
    minSimilarity: 50,
    maxSimilarity: 100,
  },
  settings: SETTINGS,
  scoopResponses: [
    {
      position: 0,
      promptId: 0,
      prompt: "What's your favorite day of the week?",
      text: "Sunday when it's holiday (Every Sunday !!)",
    },
    {
      position: 1,
      promptId: 1,
      prompt: "My favorite national park is…",
      text: "Zoo",
    },
    {
      position: 2,
      promptId: 2,
      prompt: "If I were an animal, I'd be…",
      text: "Fox, Obviously because I like Naruto Anime. Anime is my thing basically, we can chat more about Anime, I guess 24x7 I can talk on Anime :)",
    },
  ],
  occupation: "Dev",
  gender: "male",
  birthdate: "1996-01-01",
  level: undefined,
  points: 20,
  isNewsOnly: false,
  isGenerated: false,
  isArchived: false,
  createdAt: "2021-09-01T00:00:00Z",
}

export default function AccountStory() {
  return <AccountScreen_ user={USER} showBadge={true} />
}
