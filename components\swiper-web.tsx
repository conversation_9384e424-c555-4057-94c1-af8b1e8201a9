import React, { forwardRef, useImperativeHandle, useRef, useState } from "react"
import { StyleSheet } from "react-native"
// Web-specific swiper component that mimics the mobile Swiper API
// Uses react-tinder-card internally but exposes the same interface

interface WebSwiperProps<T> {
  cards: T[]
  renderCard: (item: T, cardIndex: number) => React.ReactNode
  keyExtractor?: (item: T) => string

  // Swipe callbacks (matching mobile Swiper API)
  onSwipedLeft?: (cardIndex: number) => void
  onSwipedRight?: (cardIndex: number) => void
  onSwipedTop?: (cardIndex: number) => void
  onSwipedAll?: () => void
  onSwiping?: (x?: number, y?: number) => void
  onSwipedAborted?: () => void
  onSwiped?: (cardIndex: number) => void
  onTapCard?: () => void

  // Overlay components (matching mobile Swiper API)
  renderAcceptComponent?: (overlay: any) => React.ReactNode
  renderRejectComponent?: (overlay: any) => React.ReactNode

  // Style props (matching mobile Swiper API)
  backgroundColor?: string
  containerStyle?: any
  cardStyle?: any

  // Swiper configuration (matching mobile Swiper API)
  stackScale?: number
  showSecondCard?: boolean
  verticalSwipe?: boolean
  cardIndex?: number
  stackSize?: number
  stackSeparation?: number
  disableBottomSwipe?: boolean
  cardVerticalMargin?: number
  cardHorizontalMargin?: number
  marginTop?: number
  marginBottom?: number
  thresholdMultiplier?: number

  // Additional props that might be passed through swiperProps
  disableLeftSwipe?: boolean
  disableRightSwipe?: boolean
  [key: string]: any
}

export interface WebSwiperRef {
  swipeLeft: () => void
  swipeRight: () => void
  swipeTop: () => void
}

function WebSwiperInner(
  props: WebSwiperProps<any>,
  ref: React.Ref<WebSwiperRef>,
) {
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [removedCards, setRemovedCards] = useState<number[]>([])
  const [isSwipingLeft, setIsSwipingLeft] = useState(false)
  const [isSwipingRight, setIsSwipingRight] = useState(false)

  // Import TinderCard dynamically (same pattern as GistsStack)
  const TinderCard = require("react-tinder-card")

  const {
    cards,
    renderCard,
    keyExtractor,
    onSwipedLeft,
    onSwipedRight,
    onSwipedTop,
    onSwipedAll,
    onSwiping,
    onSwipedAborted,
    onSwiped,
    onTapCard,
    renderAcceptComponent,
    renderRejectComponent,
    backgroundColor,
    containerStyle,
    cardStyle,
    disableLeftSwipe,
    disableRightSwipe,
    ...otherProps
  } = props

  // Expose imperative methods (matching mobile Swiper API)
  useImperativeHandle(ref, () => ({
    swipeLeft: () => handleSwipe("left"),
    swipeRight: () => handleSwipe("right"),
    swipeTop: () => handleSwipe("up"),
  }))

  const handleSwipe = (direction: string, cardIndex?: number) => {
    const index = cardIndex ?? currentCardIndex

    // Show overlay during swipe
    if (direction === "left") {
      setIsSwipingLeft(true)
      setTimeout(() => setIsSwipingLeft(false), 200) // Hide after animation
    } else if (direction === "right") {
      setIsSwipingRight(true)
      setTimeout(() => setIsSwipingRight(false), 200) // Hide after animation
    }

    // Call appropriate callback based on direction
    if (direction === "left" && onSwipedLeft) {
      onSwipedLeft(index)
    } else if (direction === "right" && onSwipedRight) {
      onSwipedRight(index)
    } else if (direction === "up" && onSwipedTop) {
      onSwipedTop(index)
    }

    // Update state
    setRemovedCards((prev) => [...prev, index])
    setCurrentCardIndex((prev) => prev + 1)

    // Call general onSwiped callback
    if (onSwiped) {
      onSwiped(index)
    }

    // Check if all cards are swiped
    if (index >= cards.length - 1 && onSwipedAll) {
      onSwipedAll()
    }
  }

  const handleCardLeftScreen = (cardIndex: number) => {
    // Card has finished leaving the screen
    console.log("Card left screen:", cardIndex)
  }

  // Filter out removed cards and get visible cards
  const visibleCards = cards.filter((_, index) => !removedCards.includes(index))

  // Create overlay styles that mimic mobile behavior
  const acceptOverlayStyle = {
    transform: `scale(${isSwipingRight ? 1 : 0})`,
    transition: "transform 0.1s ease-out",
    position: "absolute" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    pointerEvents: "none" as const,
  }

  const rejectOverlayStyle = {
    transform: `scale(${isSwipingLeft ? 1 : 0})`,
    transition: "transform 0.1s ease-out",
    position: "absolute" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    pointerEvents: "none" as const,
  }

  return (
    <div
      style={{
        ...styles.container,
        backgroundColor,
        ...containerStyle,
      }}
    >
      {/* Render overlay components if provided - positioned absolutely */}
      {renderAcceptComponent && (
        <div style={acceptOverlayStyle}>{renderAcceptComponent({})}</div>
      )}
      {renderRejectComponent && (
        <div style={rejectOverlayStyle}>{renderRejectComponent({})}</div>
      )}

      {/* Render cards in reverse order (last card on bottom) */}
      {visibleCards.map((card, index) => {
        const cardIndex = cards.indexOf(card)
        const key = keyExtractor ? keyExtractor(card) : `card-${cardIndex}`

        return (
          <TinderCard
            key={key}
            onSwipe={(direction: string) => handleSwipe(direction, cardIndex)}
            onCardLeftScreen={() => handleCardLeftScreen(cardIndex)}
            preventSwipe={[
              ...(disableLeftSwipe ? ["left"] : []),
              ...(disableRightSwipe ? ["right"] : []),
            ]}
            swipeRequirementType="position"
            swipeThreshold={100}
          >
            <div
              style={{
                ...styles.card,
                ...cardStyle,
              }}
            >
              {renderCard(card, cardIndex)}
            </div>
          </TinderCard>
        )
      })}
    </div>
  )
}

const styles = {
  container: {
    flex: 1,
    position: "relative" as const,
    maxHeight: 500, // Max height for web
  },
  card: {
    position: "absolute" as const,
    width: "100%",
    height: "100%",
  },
}

const WebSwiper = forwardRef<WebSwiperRef, WebSwiperProps<any>>(WebSwiperInner)
export default WebSwiper
