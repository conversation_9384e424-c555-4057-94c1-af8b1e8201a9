import { Prompt } from "@/components/signInOrUp/SignUp"
import { get, post, put } from "@/network"
import { ImpersonateProps } from "@/ctx"
import { INPRESS_API_URL } from "./constants"
import { LocalOrRemoteImage } from "@/components/MultiImagePicker"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { Deactivation } from "@/components/settings/DeactivateAccountModal"
import * as Sentry from "@sentry/react-native"
import {
  convertRawSession,
  convertRawUser,
  convertUnknownTypeUserWithPrivateData,
  convertUserUpdateToRaw,
  RawSession,
  RawUnknownTypeUserWithPrivateData,
  RawUser,
  Session,
  UnknownTypeUserWithPrivateData,
  User,
  UserUpdate,
} from "@/types/user"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { resizeImage } from "@/utils/images"
import { isImpersonating } from "@/utils/general"

export const sendPhoneVerification = async (phoneNumber: string) =>
  post<{ phone_number: string }, void>(`${INPRESS_API_URL}/verification/send`, {
    phone_number: phoneNumber,
  })

export const verifyPhoneNumber = async (phoneNumber: string, code: string) =>
  post<{ phone_number: string; code: string }, { success: boolean }>(
    `${INPRESS_API_URL}/verification/verify`,
    {
      phone_number: phoneNumber,
      code,
    },
  )

interface LoginApiProps {
  email: string
  password: string
}

export const login = async ({
  email,
  password,
}: {
  email: string
  password: string
}): Promise<Session> => {
  const rawSession = await post<LoginApiProps, RawSession>(
    `${INPRESS_API_URL}/login`,
    {
      email,
      password,
    },
  )
  return convertRawSession(rawSession)
}

interface UpdateProfileProps {
  token: string
  userUpdate: UserUpdate
  connectionMode?: ConnectionMode
}

interface UpdateProfileApiParams
  extends Partial<Omit<RawUser, "scoop_responses">> {
  scoop_responses?: {
    position: number
    prompt_id: number
    text: string
  }[]
}

export const updateProfile = async ({
  token,
  userUpdate,
  connectionMode,
}: UpdateProfileProps): Promise<Session> => {
  let suffix
  if (connectionMode === "friends") {
    suffix = "-friends"
  } else if (!connectionMode) {
    suffix = "-without-mode"
  } else {
    suffix = ""
  }

  const rawSession = await put<UpdateProfileApiParams, RawSession>(
    `${INPRESS_API_URL}/update-profile${suffix}`,
    convertUserUpdateToRaw(userUpdate),
    token,
  )
  return convertRawSession(rawSession)
}

const convertImagesToFormData = (
  images: LocalOrRemoteImage[],
  connectionMode?: ConnectionMode,
) => {
  const formData = new FormData()
  images.forEach((image, index) => {
    formData.append("statuses[]", image.uri ? "new" : "old")
    formData.append("paths[]", image.path || "")

    if (image.uri) {
      formData.append("images[]", {
        name: `image-${index}.png`,
        type: `image/png`,
        uri: image.uri,
      } as any)
    }
  })

  if (connectionMode) formData.append("connection_mode", connectionMode)

  return formData
}

type UpdateProfileImagesProps = FormData

interface UpdateProfileImagesApiResponse {
  images: {
    id: number
    path: string
    position: number
  }[]
}

const resizeImages = async (images: LocalOrRemoteImage[]) =>
  await Promise.all(
    images.map(async (image) => {
      if (image.uri) {
        const uri = await resizeImage(image.uri)
        return { uri } as LocalOrRemoteImage
      }
      return image
    }),
  )

export const uploadNewProfileImages = async ({
  images,
  connectionMode,
}: {
  images: LocalOrRemoteImage[]
  connectionMode: ConnectionMode
}): Promise<number[]> => {
  const token = process.env.EXPO_PUBLIC_PUBLIC_API_TOKEN
  if (!token) {
    throw new Error("Missing token")
  }

  const resizedImages = await resizeImages(images)

  const formData = convertImagesToFormData(resizedImages, connectionMode)

  const { images: createdImages } = await post<
    UpdateProfileImagesProps,
    UpdateProfileImagesApiResponse
  >(`${INPRESS_API_URL}/upload-new-account-images`, formData, token, {
    "Content-Type": "multipart/form-data",
  })

  return createdImages.map((image) => image.id)
}

export const updateProfileImages = async ({
  token,
  pics,
  connectionMode,
}: {
  token: string
  pics: LocalOrRemoteImage[]
  connectionMode?: ConnectionMode
}) => {
  const resizedImages = await resizeImages(pics)

  const formData = convertImagesToFormData(resizedImages, connectionMode)

  return await post(`${INPRESS_API_URL}/upload-images`, formData, token, {
    "Content-Type": "multipart/form-data",
  })
}

export const getSession = async (token: string): Promise<Session | null> => {
  const MAX_RETRIES = 3
  const RETRY_DELAY_MS = 1000

  const delay = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms))

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      const rawSession = await get<null, RawSession>(
        `${INPRESS_API_URL}/session`,
        token,
      )
      return convertRawSession(rawSession)
    } catch (e) {
      console.error(`Attempt ${attempt} failed:`, e)

      if (attempt === MAX_RETRIES) {
        Sentry.captureException(
          new Error("Failed to refresh session after max retries"),
          { extra: { error: e } },
        )
        console.error("Max retries reached. Returning null.")
        return null
      }

      console.log(`Retrying in ${RETRY_DELAY_MS}ms...`)
      await delay(RETRY_DELAY_MS)
    }
  }

  return null
}

export const updatePushToken = async ({
  token,
  pushToken,
}: {
  token: string
  pushToken: string
}) => {
  if (isImpersonating) return

  const response = await put(
    `${INPRESS_API_URL}/update-push-token`,
    {
      push_token: pushToken,
    },
    token,
  )

  return response
}

export const impersonate = async ({
  email,
}: ImpersonateProps): Promise<Session> => {
  const adminToken = process.env.EXPO_PUBLIC_ADMIN_TOKEN
  if (!adminToken) {
    throw new Error("No admin token found")
  }

  const adminPassword = process.env.EXPO_PUBLIC_ADMIN_PASSWORD

  const response: RawSession = await post(
    `${INPRESS_API_URL}/impersonate`,
    {
      admin_password: adminPassword,
      email,
    },
    adminToken,
  )

  return convertRawSession(response)
}

interface GetUserApiParams {
  user_id: number
  connection_mode: ConnectionMode
}

export const getUser = async ({
  token,
  userId,
  connectionMode,
}: {
  token: string
  userId: number
  connectionMode: ActiveConnectionMode
}): Promise<User> => {
  const { user } = await get<GetUserApiParams, { user: RawUser }>(
    `${INPRESS_API_URL}/user`,
    token,
    { user_id: userId, connection_mode: connectionMode },
  )
  return convertRawUser(user)
}

type GetCurrentUserApiParams = { connection_mode?: ConnectionMode }

export const getCurrentUser = async ({
  token,
  connectionMode,
}: {
  token: string
  connectionMode?: ConnectionMode
}): Promise<UnknownTypeUserWithPrivateData> => {
  const params = connectionMode ? { connection_mode: connectionMode } : null

  const { user } = await get<
    GetCurrentUserApiParams | null,
    { user: RawUnknownTypeUserWithPrivateData }
  >(`${INPRESS_API_URL}/current-user`, token, params)
  return convertUnknownTypeUserWithPrivateData(user)
}

export const resetPassword = async ({
  token,
  password,
  password_confirmation,
}: {
  token: string
  password: string
  password_confirmation: string
}) => {
  const response = await post(
    `${INPRESS_API_URL}/reset-password`,
    {
      password,
      password_confirmation,
    },
    token,
  )

  return response
}

type FetchPromptsParams = { connection_mode: ConnectionMode }

export const fetchPrompts = async (connectionMode: ConnectionMode) => {
  try {
    const response = await get<FetchPromptsParams, Prompt[]>(
      `${INPRESS_API_URL}/onboarding/scoop_prompts`,
      undefined,
      { connection_mode: connectionMode },
    )

    return response
  } catch (error) {
    throw new Error("Failed to fetch scoop prompts")
  }
}

export const archiveAccount = async (
  token: string,
  deactivation: Deactivation,
) =>
  post<Deactivation, null>(
    `${INPRESS_API_URL}/archive-account`,
    deactivation,
    token,
  )
