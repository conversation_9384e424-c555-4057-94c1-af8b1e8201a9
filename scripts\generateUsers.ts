import fs from "fs"
import _ from "lodash"
import moment from "moment"
import { faker } from "@faker-js/faker"

import dotenv from "dotenv"
import { birthdateFormat } from "@/types/user"
dotenv.config()

interface User {
  name: string
  email: string
  birthdate: string
  gender: string
}

async function generateRawUsers(
  gender: "male" | "female",
  count: number,
): Promise<User[]> {
  try {
    const users: User[] = _.range(count).map((index: number) => {
      const name = `${faker.person.firstName(
        gender,
      )} ${faker.person.lastName()}`
      return {
        name,
        email: name.replace(/\s+/g, ".").toLowerCase() + "@example.com",
        birthdate: moment()
          .subtract(_.random(21, 32), "years")
          .format(birthdateFormat),
        gender,
      }
    })
    return users
  } catch (error) {
    console.error("Error fetching users:", error)
    return []
  }
}

async function generateAndSaveUsers() {
  const totalUserCount = 100
  const maleUsers = await generateRawUsers("male", totalUserCount / 2)
  const femaleUsers = await generateRawUsers("female", totalUserCount / 2)

  const allUsers = [...maleUsers, ...femaleUsers]

  const downloadDir = "./generatedData"
  if (!fs.existsSync(downloadDir)) {
    fs.mkdirSync(downloadDir)
  }

  const usersReadyForInsertion = allUsers.map((user, index) => ({
    ...user,
    is_generated: true,
  }))

  const usersJsonStr = JSON.stringify(usersReadyForInsertion, null, 2)
  fs.writeFileSync(`${downloadDir}/generatedUsers.json`, usersJsonStr)

  console.log(usersReadyForInsertion)
}

generateAndSaveUsers()
