import { useState } from "react"
import { NewFriendRequestNotification } from "@/apiQueries/notificationTypes"
import { Image } from "expo-image"
import { GenericNotificationItem } from "./GenericNotificationItem"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import {
  ConnectRequestStatus,
  respondToConnectRequest,
} from "@/apiQueries/connectRequests"
import { HandleNavigateParams } from "../NotificationFeed"
import { NowFriendsNotificationItem } from "./NowFriendsNotificationItem"
import SetUpFriendsProfileModal from "../SetUpFriendsProfileModal"
import { styles } from "../notificationStyles"
import { BoldText, NormalText } from "@/components/StyledText"
import { SessionWithProfile } from "@/types/user"

export function NewFriendRequestNotificationItem({
  item,
  session,
  onNavigate,
  onRefresh,
}: {
  item: NewFriendRequestNotification
  session: SessionWithProfile | null | undefined
  onNavigate: (params: HandleNavigateParams) => void
  onRefresh: () => void
}) {
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false)

  const handleAccept = async () => {
    if (!session?.user.friendsModeIsActivated) {
      setIsBottomSheetOpen(true)
      return
    }

    await respondToConnectRequest({
      connectRequestId: item.connectRequest.id,
      response: "accept",
      token: session!.token,
    })
    onRefresh()
  }

  const onDecline = async () => {
    if (!session?.token) return
    await respondToConnectRequest({
      connectRequestId: item.connectRequest.id,
      response: "decline",
      token: session.token,
    })
    onRefresh()
  }

  const handleOpenChat = () => {
    const streamId = item.chatChannel!.streamId
    onNavigate({
      href: {
        pathname: "/(app)/matches/[channelId]",
        params: { channelId: streamId },
      },
      connectionMode: ConnectionMode.Friends,
    })
  }

  const ImageComponent = (
    <Image
      source={{ uri: item.user.image.url }}
      placeholder="LKO2?U%2Tw=w]~RBVZRi}RPxYJt6"
      contentFit="cover"
      contentPosition="center"
      style={styles.image}
    />
  )

  if (item.connectRequest.status === ConnectRequestStatus.ACCEPTED)
    return (
      <NowFriendsNotificationItem item={item} onOpenChat={handleOpenChat} />
    )

  const userIsActive = !item.user.isArchived

  return (
    <>
      <GenericNotificationItem
        timestamp={item.createdAt}
        LogoComponent={ImageComponent}
        TextComponent={
          <NormalText>
            <BoldText>
              {item.user.firstName} {item.user.lastName}
            </BoldText>{" "}
            is requesting to be friends with you
          </NormalText>
        }
        primaryButton={{
          text: "Accept",
          onPress: handleAccept,
        }}
        secondaryButton={{
          text: "Decline",
          onPress: onDecline,
        }}
        disabled={!userIsActive}
      />
      <SetUpFriendsProfileModal
        visible={isBottomSheetOpen}
        onClose={() => setIsBottomSheetOpen(false)}
      />
    </>
  )
}
