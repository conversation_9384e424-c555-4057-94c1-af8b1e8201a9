import OpenAI from "openai"
import path from "path"
import axios from "axios"
import fs from "fs"

import dotenv from "dotenv"
import _ from "lodash"
dotenv.config()

const openai = new OpenAI({
  apiKey: process.env["OPENAI_API_KEY"],
})

async function downloadImage({
  gender,
  filePath,
}: {
  gender: string
  filePath: string
}) {
  const response = await openai.images.generate({
    model: "dall-e-3",
    prompt: `A casual selfie of an ugly overweight ${gender} in their 20s with an asymmetric face in a casual setting like a park or a cafe.`,
    n: 1,
    size: "1024x1024",
    response_format: "url",
  })

  const imageUrl = response.data![0].url
  if (!imageUrl) {
    console.error("Error generating image")
    return
  }
  const imageResponse = await axios.get(imageUrl, { responseType: "stream" })
  const imageStream = fs.createWriteStream(filePath)
  imageResponse.data.pipe(imageStream)
  return new Promise<void>((resolve, reject) => {
    imageStream.on("finish", resolve)
    imageStream.on("error", reject)
  })
}

async function generateProfilePics() {
  const numberToGenerate = 100
  const profilePicsDir = path.resolve(__dirname, "../generatedProfilePics")
  if (!fs.existsSync(profilePicsDir)) {
    fs.mkdirSync(profilePicsDir)
  }

  console.log("Generating profile pics...")

  for await (const index of _.range(numberToGenerate)) {
    await downloadImage({
      gender: index % 2 === 0 ? "male" : "female",
      filePath: path.resolve(
        profilePicsDir,
        `generatedProfilePic-${index}.jpg`,
      ),
    })
    console.log("Generated profile pic", index + 1, "of", numberToGenerate)
    await new Promise((resolve) => setTimeout(resolve, 12000))
  }
}

generateProfilePics()
