import { Lead } from "@/apiQueries/apiQueries"
import { LeadsPage_ } from "@/components/LeadsPage"
import { USER } from "../(account)/account"
import { DEFAULT_PROPS } from "./leads"

const leads: Lead[] = [
  {
    id: 1,
    score: 0.5,
    topics: [],
    user: {
      ...USER,
      images: USER.images.slice(0, 1),
    },
  },
]

export default function LeadsStory() {
  return <LeadsPage_ {...DEFAULT_PROPS} leads={leads} />
}
