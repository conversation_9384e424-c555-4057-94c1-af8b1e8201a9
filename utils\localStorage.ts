import AsyncStorage from "@react-native-async-storage/async-storage"

export const ANON_USER_ID_KEY = "anonUserId"
export const ONBOARDING_DATA_KEY = "onboardingData"
export const IS_NEWS_ONLY_KEY = "isNewsOnly"
export const HAS_SEEN_WELCOME_POPUP_KEY = "hasSeenWelcomePopup"
export const LAST_VISITED_PATH_KEY = "lastVisitedPath"
export const LAST_LOC_UPDATE_TIME_KEY = "lastLocationUpdateTime"

export enum StorageKeys {
  Levels = "levels",
  Stats = "stats",
}

export async function saveToStorage<T>({
  key,
  value,
}: {
  key: StorageKeys | string
  value: T
}): Promise<void> {
  return AsyncStorage.setItem(key, JSON.stringify(value))
}

export async function loadfromStorage<T>(
  key: StorageKeys | string,
): Promise<T | null> {
  return AsyncStorage.getItem(key).then((value) => {
    if (!value) return null
    return JSON.parse(value) as T
  })
}
