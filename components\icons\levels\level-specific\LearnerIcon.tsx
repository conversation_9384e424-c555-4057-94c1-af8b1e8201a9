import Svg, { Path, SvgProps } from "react-native-svg"
export const LearnerIcon = (props: SvgProps) => (
  <Svg width={20} height={20} viewBox="0 0 27 30" fill="none">
    <Path
      d="M19.199 0.463677L19.7178 2.21861C19.8788 2.75258 20.1513 3.2377 20.5135 3.63519C20.8756 4.03268 21.3173 4.33149 21.8033 4.50773L23.3999 5.07797L23.4312 5.0878C23.5544 5.13544 23.661 5.22393 23.7365 5.34104C23.8119 5.45814 23.8524 5.5981 23.8524 5.7416C23.8524 5.88509 23.8119 6.02505 23.7365 6.14216C23.661 6.25927 23.5544 6.34775 23.4312 6.3954L21.8347 6.96563C21.3486 7.14187 20.9069 7.44068 20.5448 7.83817C20.1826 8.23566 19.9101 8.72079 19.7491 9.25475L19.2303 11.008C19.187 11.1434 19.1065 11.2606 19 11.3436C18.8934 11.4265 18.7661 11.471 18.6355 11.471C18.505 11.471 18.3777 11.4265 18.2711 11.3436C18.1646 11.2606 18.0841 11.1434 18.0407 11.008L17.5205 9.25475C17.3607 8.7196 17.0891 8.23304 16.7274 7.83409C16.3655 7.43436 15.9233 7.13354 15.4364 6.9558L13.8398 6.38557C13.7167 6.33792 13.61 6.24944 13.5346 6.13233C13.4592 6.01522 13.4186 5.87526 13.4186 5.73177C13.4186 5.58827 13.4592 5.44831 13.5346 5.3312C13.61 5.2141 13.7167 5.12561 13.8398 5.07797L15.4364 4.50773C15.9164 4.32673 16.3515 4.02578 16.7077 3.62857C17.0639 3.23135 17.3314 2.74871 17.4892 2.21861L18.0079 0.465316C18.051 0.329392 18.1314 0.211563 18.2381 0.128181C18.3449 0.0447995 18.4725 0 18.6035 0C18.7344 0 18.8621 0.0447995 18.9688 0.128181C19.0755 0.211563 19.156 0.327753 19.199 0.463677ZM26.5111 13.461L25.3692 13.0547C25.0227 12.9273 24.7079 12.7131 24.4497 12.429C24.1915 12.1449 23.9969 11.7987 23.8814 11.4177L23.5087 10.1658C23.4779 10.069 23.4204 9.98516 23.3443 9.92584C23.2682 9.86651 23.1772 9.83464 23.0839 9.83464C22.9906 9.83464 22.8996 9.86651 22.8235 9.92584C22.7474 9.98516 22.6899 10.069 22.659 10.1658L22.2893 11.4177C22.1759 11.7962 21.9846 12.1409 21.7301 12.4248C21.4757 12.7088 21.1651 12.9243 20.8224 13.0547L19.682 13.461C19.5946 13.4957 19.5191 13.5591 19.4657 13.6427C19.4123 13.7263 19.3837 13.8259 19.3837 13.928C19.3837 14.0301 19.4123 14.1298 19.4657 14.2133C19.5191 14.2969 19.5946 14.3604 19.682 14.395L20.8224 14.803C21.17 14.9304 21.4857 15.1452 21.7445 15.4302C22.0032 15.7152 22.198 16.0627 22.3132 16.4449L22.6829 17.6968C22.7137 17.7936 22.7712 17.8774 22.8473 17.9368C22.9234 17.9961 23.0144 18.028 23.1077 18.028C23.201 18.028 23.292 17.9961 23.3682 17.9368C23.4443 17.8774 23.5017 17.7936 23.5326 17.6968L23.9038 16.4449C24.0194 16.0637 24.2142 15.7174 24.4727 15.4333C24.7312 15.1492 25.0463 14.9351 25.393 14.808L26.5334 14.4016C26.6208 14.3669 26.6964 14.3035 26.7497 14.2199C26.8031 14.1363 26.8318 14.0367 26.8318 13.9346C26.8318 13.8325 26.8031 13.7328 26.7497 13.6493C26.6964 13.5657 26.6208 13.5023 26.5334 13.4676L26.5111 13.461ZM23.1107 19.6648C22.7141 19.6642 22.3267 19.5329 21.9986 19.2879C21.6707 19.0257 21.4322 18.68 21.2831 18.2703L20.8955 16.9562C20.8562 16.8178 20.7841 16.6934 20.6868 16.5957C20.5941 16.4937 20.4821 16.4154 20.3588 16.3663L19.2229 15.9566C18.8204 15.7928 18.4924 15.5289 18.2539 15.1684C18.0154 14.808 17.8961 14.3803 17.8961 13.9362C17.8961 13.6249 17.9558 13.3136 18.075 13.035C17.8365 12.9694 17.6129 12.8547 17.4042 12.6909C17.0495 12.4051 16.7793 12.0107 16.6275 11.557L16.0908 9.76763C15.9567 9.39075 15.8225 9.17774 15.6585 8.99749C15.4584 8.78497 15.2189 8.62234 14.9564 8.52066L13.3747 7.94715C13.117 7.85292 12.8785 7.70431 12.6726 7.50964V28.2707C13.2558 28.8393 13.9651 29.2288 14.729 29.3999C15.4929 29.5709 16.2844 29.5175 17.0235 29.2449C17.7626 28.9723 18.4232 28.4903 18.9386 27.8474C19.4541 27.2045 19.8062 26.4236 19.9593 25.5834L20.0145 25.2769C21.1807 25.005 22.2148 24.27 22.9189 23.2125C23.623 22.155 23.9477 20.8493 23.8307 19.5451C23.5983 19.628 23.3551 19.6684 23.1107 19.6648ZM11.1819 4.03582C10.6918 3.64454 10.12 3.39503 9.51919 3.31023C8.91838 3.22544 8.30783 3.30809 7.74369 3.55058C7.17956 3.79307 6.67996 4.18761 6.29086 4.6979C5.90177 5.20819 5.63567 5.81784 5.51706 6.47077L5.33072 7.49817L4.72548 7.6309C3.81387 7.83189 2.99368 8.37359 2.40426 9.16396C1.81483 9.95434 1.49251 10.9446 1.49208 11.9666V12.2911C1.49208 13.4807 1.95421 14.5523 2.69212 15.3012C1.7908 15.7921 1.05096 16.5794 0.57537 17.5537C0.0997794 18.5279 -0.0879455 19.6409 0.0382738 20.7378C0.164493 21.8347 0.598389 22.8612 1.27969 23.6747C1.96098 24.4881 2.85585 25.0482 3.83998 25.2769L3.89514 25.5801C4.04808 26.4204 4.4 27.2016 4.91537 27.8447C5.43074 28.4877 6.09134 28.97 6.83047 29.2427C7.5696 29.5155 8.36111 29.5691 9.12511 29.3981C9.88911 29.2272 10.5986 28.8377 11.1819 28.269V4.03582Z"
      fill={props.fill || "black"}
    />
  </Svg>
)
