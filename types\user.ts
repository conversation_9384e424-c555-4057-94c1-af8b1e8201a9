import {
  convertRawUserSettings,
  RawUserSettings,
  UserSettings,
} from "@/apiQueries/userSettings"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { GenderPrefs } from "@/components/signInOrUp/GenderPrefStep"
import { Level, RawLevel, convertRawLevel } from "./levels"

export interface ScoopResponse {
  position: number
  promptId: number
  prompt: string
  text: string
}

export interface RawScoopResponse {
  id: number
  user_id: number
  prompt_id: number
  position: number
  text: string
  created_at: string
  updated_at: string
  prompt: {
    id: number
    text: string
    created_at: string
    updated_at: string
  }
}

export const birthdateFormat = "YYYY-MM-DD"

export interface Preferences {
  genders: GenderPrefs
  minAge: number
  maxAge: number
  minSimilarity: number
  maxSimilarity: number
}

export interface RawPreferences {
  genders: GenderPrefs
  min_age: number
  max_age: number
  min_similarity: number
  max_similarity: number
}

export const convertRawPreferences = (
  rawPreferences: RawPreferences,
): Preferences => {
  return {
    genders: rawPreferences.genders,
    minAge: rawPreferences.min_age,
    maxAge: rawPreferences.max_age,
    minSimilarity: rawPreferences.min_similarity,
    maxSimilarity: rawPreferences.max_similarity,
  }
}

export interface UserImage {
  id: number
  url: string
}

export interface Account {
  id: number
  firstName: string
  lastName: string
  latitude: number
  longitude: number
  settings: UserSettings | undefined
  isNewsOnly: boolean
  isGenerated: boolean
  isArchived: boolean
  points: number
  level?: Level
  createdAt: string
}

export interface RawAccount {
  id: Account["id"]
  first_name: Account["firstName"]
  last_name: Account["lastName"]
  latitude: Account["latitude"]
  longitude: Account["longitude"]
  settings: RawUserSettings | undefined
  is_news_only: Account["isNewsOnly"]
  is_generated: Account["isGenerated"]
  is_archived: Account["isArchived"]
  points: Account["points"]
  level?: RawLevel
  created_at: Account["createdAt"]
}

export interface Profile {
  age: number
  occupation: string
  gender: string
  connectionMode: ConnectionMode
  datesModeIsActivated: boolean
  friendsModeIsActivated: boolean
  preferences: Preferences
  images: UserImage[]
  biography: string
  scoopResponses: ScoopResponse[]
}

interface RawProfile {
  age: Profile["age"]
  occupation: Profile["occupation"]
  gender: Profile["gender"]
  connection_mode: Profile["connectionMode"]
  dates_mode_is_activated: Profile["datesModeIsActivated"]
  friends_mode_is_activated: Profile["friendsModeIsActivated"]
  preferences: RawPreferences
  images: Profile["images"]
  biography: Profile["biography"]
  scoop_responses: RawScoopResponse[]
}

interface NewsOnlyUser extends Account {
  images: Profile["images"]
}

interface RawNewsOnlyUser extends RawAccount {
  images: RawProfile["images"]
}

export const convertRawNewsOnlyUser = (
  rawNewsOnlyUser: RawNewsOnlyUser,
): NewsOnlyUser => {
  return {
    id: rawNewsOnlyUser.id,
    firstName: rawNewsOnlyUser.first_name,
    lastName: rawNewsOnlyUser.last_name,
    latitude: rawNewsOnlyUser.latitude,
    longitude: rawNewsOnlyUser.longitude,
    images: rawNewsOnlyUser.images,
    settings:
      rawNewsOnlyUser.settings &&
      convertRawUserSettings(rawNewsOnlyUser.settings),
    isNewsOnly: rawNewsOnlyUser.is_news_only,
    isGenerated: rawNewsOnlyUser.is_generated,
    isArchived: rawNewsOnlyUser.is_archived,
    points: rawNewsOnlyUser.points,
    level: rawNewsOnlyUser.level && convertRawLevel(rawNewsOnlyUser.level),
    createdAt: rawNewsOnlyUser.created_at,
  }
}

export interface User extends Account, Profile {}
export interface RawUser extends RawAccount, RawProfile {}

export const convertRawUser = (rawUser: RawUser): User => {
  return {
    ...convertRawNewsOnlyUser(rawUser),
    age: rawUser.age,
    gender: rawUser.gender,
    biography: rawUser.biography,
    scoopResponses: rawUser.scoop_responses.map(
      ({ position, prompt, text }) => ({
        position,
        promptId: prompt.id,
        prompt: prompt.text,
        text,
      }),
    ),
    preferences: convertRawPreferences(rawUser.preferences),
    occupation: rawUser.occupation,
    connectionMode: rawUser.connection_mode,
    datesModeIsActivated: !!rawUser.dates_mode_is_activated,
    friendsModeIsActivated: !!rawUser.friends_mode_is_activated,
  }
}

export type UnknownTypeUser = User | NewsOnlyUser
export type RawUnknownTypeUser = RawUser | RawNewsOnlyUser

export const isUserWithProfile = (user: UnknownTypeUser): user is User => {
  return !user.isNewsOnly
}

const isRawNewsOnlyUser = (
  rawUser: RawUnknownTypeUser,
): rawUser is RawNewsOnlyUser => {
  return rawUser.is_news_only
}

export const convertRawUnknownTypeUser = (
  rawUser: RawUnknownTypeUser,
): UnknownTypeUser => {
  return isRawNewsOnlyUser(rawUser)
    ? convertRawNewsOnlyUser(rawUser)
    : convertRawUser(rawUser)
}

export interface Session {
  user: UnknownTypeUser
  token: string
  version: string
}

export interface RawSession {
  user: RawUnknownTypeUser
  token: Session["token"]
  version: Session["version"]
}

export const convertRawSession = (rawSession: RawSession): Session => {
  return {
    user: convertRawUnknownTypeUser(rawSession.user),
    token: rawSession.token,
    version: rawSession.version,
  }
}

export interface SessionWithProfile extends Session {
  user: User
}

export const isSessionWithProfile = (
  session: Session,
): session is SessionWithProfile => {
  return isUserWithProfile(session.user)
}

type PrivateFields = {
  lastName: string
  phoneNumber: string
  birthdate: string
}

type RawPrivateFields = {
  last_name: string
  phone_number: string
  birthdate: string
}

export type NewsOnlyUserWithPrivateData = NewsOnlyUser & PrivateFields
export type UserWithPrivateData = User & PrivateFields

export type RawNewsOnlyUserWithPrivateData = RawNewsOnlyUser & RawPrivateFields
export type RawUserWithPrivateData = RawUser & RawPrivateFields

export type UnknownTypeUserWithPrivateData =
  | UserWithPrivateData
  | NewsOnlyUserWithPrivateData

export type RawUnknownTypeUserWithPrivateData =
  | RawUserWithPrivateData
  | RawNewsOnlyUserWithPrivateData

export const isUserWithPrivateData = (
  user: UnknownTypeUserWithPrivateData,
): user is UserWithPrivateData => {
  return !user.isNewsOnly
}

export const isRawUserWithPrivateData = (
  rawUser: RawUnknownTypeUserWithPrivateData,
): rawUser is RawUserWithPrivateData => {
  return !isRawNewsOnlyUser(rawUser)
}

export const convertUnknownTypeUserWithPrivateData = (
  user: RawUnknownTypeUserWithPrivateData,
): UnknownTypeUserWithPrivateData => {
  const privateFields = {
    lastName: user.last_name,
    phoneNumber: user.phone_number,
    birthdate: user.birthdate,
  }

  return isRawUserWithPrivateData(user)
    ? {
        ...convertRawUser(user),
        ...privateFields,
      }
    : {
        ...convertRawNewsOnlyUser(user),
        ...privateFields,
      }
}

export interface UserUpdate
  extends Partial<Omit<UserWithPrivateData, "settings" | "images" | "level">> {
  imageIds?: number[]
}

interface RawUserUpdate
  extends Partial<Omit<RawUserWithPrivateData, "scoop_responses">> {
  scoop_responses?: {
    position: number
    prompt_id: number
    text: string
  }[]
  image_ids?: number[]
}

export const convertUserUpdateToRaw = (
  userUpdate: Partial<UserUpdate>,
): RawUserUpdate => {
  return {
    ...userUpdate,
    first_name: userUpdate.firstName,
    last_name: userUpdate.lastName,
    phone_number: userUpdate.phoneNumber,
    preferences: userUpdate.preferences && {
      genders: userUpdate.preferences.genders,
      min_age: userUpdate.preferences.minAge,
      max_age: userUpdate.preferences.maxAge,
      min_similarity: userUpdate.preferences.minSimilarity,
      max_similarity: userUpdate.preferences.maxSimilarity,
    },
    image_ids: userUpdate.imageIds,
    dates_mode_is_activated: userUpdate.datesModeIsActivated,
    friends_mode_is_activated: userUpdate.friendsModeIsActivated,
    scoop_responses: userUpdate.scoopResponses?.map(
      ({ position, promptId, text }) => ({
        position,
        prompt_id: promptId,
        text,
      }),
    ),
  }
}

export type UserPreview = {
  id: User["id"]
  firstName: User["firstName"]
  image: UserImage | undefined
}

export type RawUserPreview = {
  id: UserPreview["id"]
  first_name: UserPreview["firstName"]
  image: UserPreview["image"]
}

export const convertRawUserPreview = (rawUser: RawUserPreview): UserPreview => {
  return {
    id: rawUser.id,
    firstName: rawUser.first_name,
    image: rawUser.image,
  }
}

export type LeaderboardUser = UserPreview & {
  rank: number
  points: number
}

export type RawLeaderboardUser = RawUserPreview & {
  rank: LeaderboardUser["rank"]
  points: LeaderboardUser["points"]
}

export const convertRawLeaderboardUser = (
  rawUser: RawLeaderboardUser,
): LeaderboardUser => {
  return {
    ...convertRawUserPreview(rawUser),
    rank: rawUser.rank,
    points: rawUser.points,
  }
}
