import { SvgProps } from "react-native-svg"
import { NewbieIcon } from "../icons/levels/level-specific/NewbieIcon"
import { Level } from "@/types/levels"
import { cloneElement } from "react"
import { LearnerIcon } from "../icons/levels/level-specific/LearnerIcon"
import { ExplorerIcon } from "../icons/levels/level-specific/ExplorerIcon"
import { ThinkerIcon } from "../icons/levels/level-specific/ThinkerIcon"
import { AnalystIcon } from "../icons/levels/level-specific/AnalystIcon"
import { ScholarIcon } from "../icons/levels/level-specific/ScholarIcon"
import { SageIcon } from "../icons/levels/level-specific/SageIcon"
import { GoatIcon } from "../icons/levels/level-specific/GoatIcon"

type Props = SvgProps & {
  level: Level
}

export const LevelIcon = ({ level, ...props }: Props): React.JSX.Element => {
  const map: Record<number, React.JSX.Element> = {
    1: <NewbieIcon />,
    2: <LearnerIcon />,
    3: <ExplorerIcon />,
    4: <ThinkerIcon />,
    5: <AnalystIcon />,
    6: <ScholarIcon />,
    7: <SageIcon />,
    8: <GoatIcon />,
  }

  const icon = map[level.place]!

  return cloneElement(icon, props)
}
