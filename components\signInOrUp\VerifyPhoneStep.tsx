import { Keyboard, StyleSheet, View } from "react-native"
import { OtpInput } from "react-native-otp-entry"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"

interface VerifyPhoneStepProps {
  onCodeChange: (code: string) => void
}

export const CODE_LENGTH = 5

const VerifyPhoneStep = ({ onCodeChange }: VerifyPhoneStepProps) => {
  return (
    <View style={styles.container}>
      <OtpInput
        numberOfDigits={5}
        onTextChange={onCodeChange}
        theme={{
          pinCodeContainerStyle: styles.inputContainer,
        }}
        textInputProps={{
          returnKeyType: "done",
          onSubmitEditing: () => {
            Keyboard.dismiss()
          },
        }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    flexDirection: "row",
    justifyContent: "center",
    gap: 16,
  },
  inputContainer: {
    width: wp(75) / 5,
    backgroundColor: "white",
  },
})

export default VerifyPhoneStep
