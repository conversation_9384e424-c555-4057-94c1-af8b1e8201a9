import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native"
import React from "react"
import { Ionicons } from "@expo/vector-icons"
import { Prompt } from "../SignUp"
import { NewResponse } from "./PromptStep_"

interface SelectPromptProps {
  prompts: Prompt[]
  selectedPrompts: NewResponse[]
  onPressPrompt: (item: Prompt) => void
}

const SelectPrompt = ({
  prompts,
  selectedPrompts,
  onPressPrompt,
}: SelectPromptProps) => {
  const renderPrompt = ({ item }: { item: Prompt }) => {
    return (
      <TouchableOpacity
        style={styles.promptContainer}
        onPress={() => onPressPrompt(item)}
      >
        <Text style={styles.promptText} numberOfLines={1}>
          {item?.text}
        </Text>
        <Ionicons name="chevron-forward" size={20} />
      </TouchableOpacity>
    )
  }

  const renderDivider = () => {
    return <View style={styles.divider} />
  }

  return (
    <View>
      <FlatList
        data={prompts.filter((prompt) =>
          selectedPrompts.every((p) => p.promptId !== prompt.id),
        )}
        renderItem={renderPrompt}
        keyExtractor={(item, index) => index.toString()}
        style={styles.listStyle}
        ItemSeparatorComponent={renderDivider}
        contentContainerStyle={styles.contentContainerStyle}
        showsVerticalScrollIndicator={false}
      />
    </View>
  )
}

export default SelectPrompt

const styles = StyleSheet.create({
  listStyle: {
    overflow: "scroll",
    marginTop: 24,
    marginHorizontal: 8,
  },
  promptContainer: {
    backgroundColor: "#FFFFFF",
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    height: 56,
  },
  promptText: {
    flex: 1,
    fontWeight: "500",
    fontSize: 14,
  },
  divider: {
    width: "100%",
    height: 1,
    backgroundColor: "#DDDDDD",
  },
  contentContainerStyle: {
    borderWidth: 1,
    borderRadius: 15,
    overflow: "hidden",
    borderColor: "#DDDDDD",
  },
})
