import * as AppTrackingTransparency from "expo-tracking-transparency"
import uuid from "react-native-uuid"
import { Mixpanel } from "mixpanel-react-native"
import Constants from "expo-constants"
import * as Sentry from "@sentry/react-native"
import * as Device from "expo-device"
import {
  ConfigurableEventProps,
  postTrackingEvent,
} from "@/apiQueries/apiQueries"
import { getSession } from "@/context/session"
import { posthog } from "@/services/posthog"
import * as Localization from "expo-localization"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { ANON_USER_ID_KEY } from "./localStorage"
import { isImpersonating } from "./general"
import AppsFlyer from "react-native-appsflyer"
import { NewsEventType } from "@/types/news"
import { Platform } from "react-native"
import appsFlyer from "react-native-appsflyer"
import { ProfileEventType } from "@/types/profile"
import { OtherEventTypes } from "@/types/other"
import { SocialEventType } from "@/types/social"

export const enableTracking = process.env.EXPO_PUBLIC_ENABLE_TRACKING === "true"

export let mixpanel: Mixpanel | undefined

let hasEnabledAppsFlyer = false

export const initTracking = () => {
  if (enableTracking) {
    initMixpanel()
    initSentry()
    initConsoleError()
  }
}

export const initAppsFlyer = async () => {
  console.log("Initializing AppsFlyer...")
  if (hasEnabledAppsFlyer) {
    console.log("AppsFlyer already initialized")
    return
  }

  if (Platform.OS === "ios") {
    try {
      const { status } =
        await AppTrackingTransparency.requestTrackingPermissionsAsync()

      trackEvent(EventType.RespondedToTrackingPermission, { data: { status } })
    } catch (e) {
      console.error("Failed to request tracking permission", e)
    }
  }

  const appsFlyerDevKey = process.env.EXPO_PUBLIC_APPS_FLYER_DEV_KEY
  if (!appsFlyerDevKey) {
    console.error("Missing AppsFlyer dev key")
    return
  }

  AppsFlyer.initSdk(
    {
      devKey: appsFlyerDevKey,
      isDebug: process.env.NODE_ENV !== "production",
      appId: Constants.expoConfig?.extra?.appsflyer.appId,
      onInstallConversionDataListener: true,
    },
    (result) => {
      console.log("AppsFlyer initialized", result)
    },
    (error) => {
      console.error("AppsFlyer initialization error", error)
    },
  )

  console.log("AppsFlyer initialized")

  hasEnabledAppsFlyer = true
}

export const initMixpanel = () => {
  const MIXPANEL_TOKEN = process.env.EXPO_PUBLIC_MIXPANEL_TOKEN
  if (!MIXPANEL_TOKEN) {
    throw new Error("Missing Mixpanel token")
  }
  const trackAutomaticEvents = true
  const useNative = true
  mixpanel = new Mixpanel(MIXPANEL_TOKEN, trackAutomaticEvents, useNative)
  mixpanel.init()
}

const getAppVersion = () => {
  try {
    const appVersionString = Constants.expoConfig?.version
    const appVersion = appVersionString
      ? parseInt(appVersionString.split(".")[0])
      : 0

    return appVersion
  } catch (e) {
    console.error("Failed to get app version", e)
    return -1
  }
}

export enum EventType {
  LoadedApp = "loaded_app",
  ViewedScreen = "viewed_screen",

  RespondedToTrackingPermission = "responded_to_tracking_permission",

  FailedRegisteringWithoutCode = "failed_registering_without_code",
  FailedVerifyingInvite = "failed_verifying_invite",
  HitOutOfUsaStep = "hit_out_of_usa_step",
  ViewedOnboardingScreen = "viewed_onboarding_screen",
  TappedShareInOnboarding = "tapped_share_in_onboarding",
  CompletedOnboarding = "completed_onboarding",
  SignUpFailed = "sign_up_failed",
  WelcomeSeen = "welcome_seen",
  UpdateRequiredSeen = "update_required_seen",
  LocationAutoUpdated = "location_auto_updated",

  ModeActivated = "mode_activated",
  ActivateModeFailed = "activate_mode_failed",
  AnnouncementClicked = "announcement_clicked",
  AnnouncementClosed = "announcement_closed",
  OpenedArticle = "opened_article",
  ClosedArticle = "closed_article",
  SwipedLead = "swiped_lead",
  LeadsFetched = "leads_fetched",
  ScannedMatchSeen = "scanned_match_seen",
  MatchWithoutLeadSeen = "match_without_lead_seen",
  Unmatched = "unmatched",
  NotificationReceived = "notification_received",
  NotificationRespondedTo = "notification_responded_to",
  LevelUpSharePressed = "level_up_share_pressed",
  StreakSharePressed = "streak_share_pressed",
  ReceivedAccess = "received_access",
  LoaderTimeout = "loader_timeout",
}

export const trackEvent = async (
  eventType:
    | EventType
    | NewsEventType
    | SocialEventType
    | ProfileEventType
    | OtherEventTypes,
  properties?: ConfigurableEventProps,
) => {
  const storyMode = process.env.EXPO_PUBLIC_RENDER_STORIES === "true"

  if (storyMode || isImpersonating) {
    console.log(
      "Skipping tracking in story mode or impersonation:",
      eventType,
      properties,
    )
    return
  }

  const session = getSession()
  const token = process.env.EXPO_PUBLIC_TRACKING_TOKEN
  if (!token) {
    throw new Error("Missing tracking token")
  }

  const anonId = await getAnonymousUserId()

  postTrackingEvent(
    {
      user_id: session?.user.id,
      anonymous_user_id: anonId,
      type: eventType,
      data: properties?.data,
      route: properties?.route,
      route_params: properties?.route_params,
      device_id: Device.modelId,
      device_brand: Device.brand,
      device_model: Device.modelName,
      os_name: Device.osName,
      os_version: Device.osVersion,
      locale: Localization.getLocales()[0].languageTag,
      opened_article_id: properties?.opened_article_id,
      onboarding_step: properties?.onboarding_step,
      environment: process.env.NODE_ENV,
      app_version: getAppVersion(),
    },
    token,
  )

  posthog.capture(eventType, properties?.data)
  mixpanel?.track(eventType, properties)
}

export const trackAttributionEvent = async (
  eventType: string,
  options: Record<string, any> = {},
) => {
  console.log("Attempting to track attribution event:", eventType)
  if (!enableTracking) {
    console.error("Tracking is disabled, cannot track attribution event")
    return
  }
  if (!hasEnabledAppsFlyer) {
    await initAppsFlyer()
  }

  appsFlyer.logEvent(
    eventType,
    options,
    (res) => console.log(`Tracked attribution event ${eventType}`, res),
    (err) => console.error("Failed to track attribution event:", err),
  )
}

export const initConsoleError = () => {
  const originalConsoleError = console.error
  console.error = (...args: any[]) => {
    originalConsoleError(...args)
    Sentry.captureException(new Error(args.map(String).join(" ")))
  }
}

export const initSentry = () => {
  Sentry.init({
    dsn: "https://<EMAIL>/4507215810920448",
    environment: process.env.NODE_ENV,
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    integrations: [Sentry.mobileReplayIntegration()],
    attachScreenshot: true,
    tracesSampleRate: 0.2,
  })
}

export const getAnonymousUserId = async (): Promise<string> => {
  const anonId = await AsyncStorage.getItem(ANON_USER_ID_KEY)
  if (!anonId) {
    const newAnonId = uuid.v4()
    await AsyncStorage.setItem(ANON_USER_ID_KEY, newAnonId)
    return newAnonId
  }
  return anonId
}

export { getAppVersion }
