import { post } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { trackEvent, EventType } from "@/utils/tracking"
import { FinishedNewAccount } from "@/components/signInOrUp/SignUp"

export type RegisterRequest = {
  email: string
  invitationCode?: string
  referralCode?: string
}

type RawRegisterRequest = {
  email: string
  invitation_code?: string
  referral_code?: string
}

const registerWithReferralSystem = async ({
  email,
  invitationCode,
  referralCode,
}: RegisterRequest) => {
  const token = process.env.EXPO_PUBLIC_PUBLIC_API_TOKEN
  return post<RawRegisterRequest, void>(
    `${INPRESS_API_URL}/register-with-referral-system`,
    { email, invitation_code: invitationCode, referral_code: referralCode },
    token,
  )
}

export const attemptReferralRegistration = (profile: FinishedNewAccount) => {
  let request: RegisterRequest = {
    email: profile.email,
  }

  if (profile.accessCodeType === "shared") {
    request.referralCode = profile.accessCode
  } else if (profile.accessCodeType === "custom") {
    request.invitationCode = profile.accessCode
  }

  registerWithReferralSystem(request).catch((error) => {
    trackEvent(EventType.FailedRegisteringWithoutCode, {
      data: request,
    })
    console.error("Failed to register with referral system", error)
  })
}

export { registerWithReferralSystem }
