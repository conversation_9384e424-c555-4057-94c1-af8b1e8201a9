import { ActiveConnectionMode } from "@/context/ModeContext"
import {
  NotificationConnectRequest,
  RawNotificationConnectRequest,
} from "./connectRequests"
import { RawUserPreview, UserImage, UserPreview } from "@/types/user"
import { Level, RawLevel, StreakType } from "@/types/levels"
import { ExternalPathString } from "expo-router"
import { Destination } from "@/components/news/constants"

export enum NotificationType {
  Announcement = "announcement",
  Broadcast = "broadcast",
  MoreSurveysNeeded = "more_surveys_needed",
  NewLeads = "new_leads",
  NewFriendRequest = "connect_request_received",
  NewMatch = "new_match",
  NewLike = "new_like",
  FriendRequestAccepted = "friend_request_accepted",
  NewMessage = "new_message",
  LevelUp = "level_up",
  AlmostLevelUp = "almost_level_up",
  LevelsFeatureLive = "levels_feature_live",
  AlmostLosingStreak = "almost_losing_streak",
  AlmostHitStreak = "almost_hit_streak",
  AlmostHitDensityBonus1 = "almost_hit_density_bonus_1",
  AlmostHitDensityBonus2 = "almost_hit_density_bonus_2",
  HitDensityBonus1 = "hit_density_bonus_1",
  HitDensityBonus2 = "hit_density_bonus_2",
  HitDayOrWeekStreak = "hit_day_or_week_streak",
  HitMonthStreak = "hit_month_streak",
  HitLongTermStreak = "hit_long_term_streak",
  LevelsWeeklyRecap = "levels_weekly_recap",
}

interface RawChatChannel {
  stream_id: string
}

interface ChatChannel {
  streamId: string
}

interface CommonNotificationData {
  id: string
  type: NotificationType
  isRead: boolean
  createdAt: string
}

interface NotificationUser extends UserPreview {
  image: UserImage
  lastName: string
  isArchived: boolean
}

interface RawNotificationUser extends RawUserPreview {
  image: NotificationUser["image"]
  last_name: NotificationUser["lastName"]
  is_archived: NotificationUser["isArchived"]
}

export interface AnnouncementNotification extends CommonNotificationData {
  type: NotificationType.Announcement
  title: string
  body: string
  url?: ExternalPathString
}

export interface BroadcastNotification extends CommonNotificationData {
  type: NotificationType.Broadcast
  articleId?: number
  articleUrl?: string
  inAppDestination?: Destination
  externalUrl?: string
}

export interface NewFriendRequestNotification extends CommonNotificationData {
  type: NotificationType.NewFriendRequest
  user: NotificationUser
  connectRequest: NotificationConnectRequest
  chatChannel?: ChatChannel
}

export interface NewMatchNotification extends CommonNotificationData {
  type: NotificationType.NewMatch
  user: NotificationUser
  connectionMode: ActiveConnectionMode
  chatChannel: ChatChannel
}

export interface NewLikeNotification extends CommonNotificationData {
  type: NotificationType.NewLike
  user?: NotificationUser // Optional for backwards compatibility (BC-7)
  connectionMode: ActiveConnectionMode
}

export interface FriendRequestAcceptedNotification
  extends CommonNotificationData {
  type: NotificationType.FriendRequestAccepted
  user: NotificationUser
  chatChannel: ChatChannel
}

export interface NewMessageNotification extends CommonNotificationData {
  type: NotificationType.NewMessage
  connectionMode: ActiveConnectionMode
  chatChannel?: ChatChannel
}

export interface LevelUpNotification extends CommonNotificationData {
  type: NotificationType.LevelUp
  level: Level
  nextLevel?: Level
}

export interface AlmostLevelUpNotification extends CommonNotificationData {
  type: NotificationType.AlmostLevelUp
  pointsNeeded: number
  nextLevel: Level
}

export interface LevelsFeatureLiveNotification extends CommonNotificationData {
  type: NotificationType.LevelsFeatureLive
}

export interface AlmostLosingStreakNotification extends CommonNotificationData {
  type: NotificationType.AlmostLosingStreak
}

export interface AlmostHitStreakNotification extends CommonNotificationData {
  type: NotificationType.AlmostHitStreak
  streakTypeToHit: StreakType
  rewardPoints: number
}

export interface AlmostHitDensityBonus1Notification
  extends CommonNotificationData {
  type: NotificationType.AlmostHitDensityBonus1
}

export interface AlmostHitDensityBonus2Notification
  extends CommonNotificationData {
  type: NotificationType.AlmostHitDensityBonus2
}

export interface HitDensityBonus1Notification extends CommonNotificationData {
  type: NotificationType.HitDensityBonus1
}

export interface HitDensityBonus2Notification extends CommonNotificationData {
  type: NotificationType.HitDensityBonus2
}

export interface HitDayOrWeekStreakNotification extends CommonNotificationData {
  type: NotificationType.HitDayOrWeekStreak
  streakType: StreakType
  pointsEarned: number
}

export interface HitMonthStreakNotification extends CommonNotificationData {
  type: NotificationType.HitMonthStreak
  streakType: StreakType
}

export interface HitLongTermStreakNotification extends CommonNotificationData {
  type: NotificationType.HitLongTermStreak
  streakType: StreakType
  pointsEarned: number
}

export interface LevelsWeeklyRecapNotification extends CommonNotificationData {
  type: NotificationType.LevelsWeeklyRecap
  ratingsCount: number
  pointsEarned: number
  nextLevel?: Level
}

export interface RawNotification
  extends Omit<CommonNotificationData, "isRead" | "createdAt"> {
  is_read: boolean
  user?: RawNotificationUser
  article_id?: number
  article_url?: string
  in_app_destination?: Destination
  external_url?: string
  connection_mode?: ActiveConnectionMode
  connect_request?: RawNotificationConnectRequest
  chat_channel?: RawChatChannel
  level?: RawLevel
  next_level?: RawLevel
  points_needed?: number
  streak_type_to_hit?: StreakType
  reward_points?: number
  streak_type?: StreakType
  points_earned?: number
  ratings_count?: number
  created_at: string
}

export type Notification =
  | AnnouncementNotification
  | BroadcastNotification
  | NewFriendRequestNotification
  | FriendRequestAcceptedNotification
  | NewMatchNotification
  | NewLikeNotification
  | NewMessageNotification
  | LevelUpNotification
  | AlmostLevelUpNotification
  | LevelsFeatureLiveNotification
  | AlmostLosingStreakNotification
  | AlmostHitStreakNotification
  | AlmostHitDensityBonus1Notification
  | AlmostHitDensityBonus2Notification
  | HitDensityBonus1Notification
  | HitDensityBonus2Notification
  | HitDayOrWeekStreakNotification
  | HitMonthStreakNotification
  | HitLongTermStreakNotification
  | LevelsWeeklyRecapNotification
