import { View, Text } from "@/components/Themed"
import { Link, RelativePathString, router, usePathname } from "expo-router"
import { useEffect } from "react"
import { LEADS } from "./leads/leads"
import { Image } from "expo-image"
import { ScrollView } from "react-native"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { LAST_VISITED_PATH_KEY } from "@/utils/localStorage"
import { BoldText } from "@/components/StyledText"

export default function StoryMenu() {
  const pathname = usePathname()

  useEffect(() => {
    async function prefetchLeadImages() {
      console.log("Prefetching lead images")
      console.log(LEADS)
      for await (const lead of LEADS) {
        console.log(`Prefetching ${lead.user.firstName}`)
        const imageUrl = lead.user.images[0].url
        console.log(`Prefetching ${imageUrl}`)
        await Image.prefetch(imageUrl)
        console.log(`Prefetched ${lead.user.firstName}`)
      }
    }
    prefetchLeadImages()
  }, [])

  const reloadPath = async () => {
    const lastVisitedPath = await AsyncStorage.getItem(LAST_VISITED_PATH_KEY)
    if (lastVisitedPath && lastVisitedPath !== pathname) {
      router.navigate(lastVisitedPath as RelativePathString)
    }
  }

  useEffect(() => {
    reloadPath()
  }, [])

  useEffect(() => {
    AsyncStorage.setItem(LAST_VISITED_PATH_KEY, pathname)
  }, [pathname])

  const spacer = <View style={{ height: 20 }} />

  return (
    <ScrollView style={{ flex: 1, marginBottom: 30 }}>
      <Text>Story Menu</Text>
      <Link href="/(stories)/api-queries">API Queries</Link>
      <Link href="/(stories)/utils">Utils</Link>
      {spacer}
      <Link href="/(stories)/invite-friends/contacts-list">Invite Friends</Link>
      <Link href="/(stories)/invite-friends/teaser">
        Match With Friends - Teaser
      </Link>
      {spacer}
      <Link href="/(stories)/sounds-and-haptics">Sounds and Haptics</Link>
      {spacer}
      <Link href="/(stories)/profile-debugging">Profile Debugger</Link>
      <Link href="/(stories)/(signInOrUp)/secretly-tappable-logo">
        Secretly Tappable Logo
      </Link>
      <Link href="/(stories)/(signInOrUp)/initial-view">Initial View</Link>
      <Link href="/(stories)/(signInOrUp)/initial-view-update-available">
        Initial View (Update Available)
      </Link>
      <Link href="/(stories)/(signInOrUp)/initial-view-update-required">
        Initial View (Update Required)
      </Link>
      <Link href="/(stories)/(signInOrUp)/login">Login</Link>
      <Link href="/(stories)/(signInOrUp)/login-with-error">
        Login with error
      </Link>
      <Link href="/(stories)/(signInOrUp)/code-verification-live">
        Code Verification
      </Link>
      <Link href="/(stories)/(signInOrUp)/sign-up-wrapper">
        SignUpScreenWrapper
      </Link>
      <Link href="/(stories)/(signInOrUp)/sign-up/normal">SignUp</Link>
      <Link href="/(stories)/(signInOrUp)/sign-up/loading">SignUp Loading</Link>
      <Link href="/(stories)/(signInOrUp)/sign-up/failed">SignUp failed</Link>
      <Link href="/(stories)/(signInOrUp)/sign-up/saved-progress">
        SignUp Saved Progress
      </Link>
      <Link href="/(stories)/(signInOrUp)/sign-up/activate-mode">
        SignUp - Activate Mode
      </Link>
      <Link href="/(stories)/(signInOrUp)/sign-up/access/unlaunched-area">
        SignUp - Unlaunched Area
      </Link>
      <Link href="/(stories)/(signInOrUp)/date-picker">Date Picker</Link>
      <Link href="/(stories)/(signInOrUp)/phone-verification-step">
        Phone Verification Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/gender-step">Gender Step</Link>
      <Link href="/(stories)/(signInOrUp)/gender-pref-step">
        Gender Pref Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/job-step">Job Step</Link>
      <Link href="/(stories)/(signInOrUp)/password-step">Password Step</Link>
      <Link href="/(stories)/(signInOrUp)/biography-step">Biography Step</Link>
      <Link href="/(stories)/(signInOrUp)/eula-step">Eula Step</Link>
      <Link href="/(stories)/(signInOrUp)/permission-step">
        Permission Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/permission-step-bypass-code">
        Permission Step Bypass Code
      </Link>
      <Link href="/(stories)/(signInOrUp)/membership-step">
        Membership Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/membership-step-loading-plans">
        Membership Step Loading Plans
      </Link>
      <Link href="/(stories)/(signInOrUp)/membership-step-live">
        Membership Step Live
      </Link>
      <Link href="/(stories)/(signInOrUp)/prompt-step">Prompt Step</Link>
      <Link href="/(stories)/(signInOrUp)/prompt-select">Select Prompt</Link>
      <Link href="/(stories)/(signInOrUp)/prompt-fill">Fill Prompt</Link>
      <Link href="/(stories)/(signInOrUp)/prompt-step-edit-context">
        Prompt Step Edit Context
      </Link>
      <Link href="/(stories)/(signInOrUp)/register-new-account">
        Register New Account
      </Link>
      <Link href="/(stories)/(signInOrUp)/explainer-steps">
        ⭐️ Explainer Steps
      </Link>
      <Link href="/(stories)/(signInOrUp)/gists-step">⭐️ Gists Step</Link>
      <Link href="/(stories)/(signInOrUp)/inscore-step">⭐️ InScore Step</Link>
      <Link href="/(stories)/(signInOrUp)/depth-step">⭐️ Depth Step</Link>
      <Link href="/(stories)/(signInOrUp)/matching-explainer-step">
        ⭐️ Matching Explainer Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/notification-step">
        Notification Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/just-be-yourself-step">
        Just Be Yourself Step
      </Link>
      <Link href="/(stories)/(signInOrUp)/low-user-area">Low User Area</Link>
      {spacer}
      <Link href="/(stories)/components/button">Button</Link>
      <Link href="/(stories)/components/text-input">Text Input</Link>
      <Link href="/(stories)/components/checkbox-row">Checkbox Row</Link>
      <Link href="/(stories)/components/loader">Loader</Link>
      <Link href="/(stories)/components/similarity-slider">
        Similarity Slider
      </Link>
      <Link href="/(stories)/components/confirmation-modal">
        Confirmation Modal
      </Link>
      <Link href="/(stories)/components/deactivate-account-modal">
        Deactivate Account Modal
      </Link>
      <Link href="/(stories)/components/multi-image-picker">
        Multi Image Picker
      </Link>
      <Link href="/(stories)/components/multi-image-picker-online">
        Multi Image Picker (online)
      </Link>
      <Link href="/(stories)/components/map">Map</Link>
      <Link href="/(stories)/components/map-lite">Map Lite</Link>
      <Link href="/(stories)/components/connection-mode-not-ready">
        Connection Mode Not Ready
      </Link>
      <Link href="/(stories)/components/connection-mode-not-ready-dates">
        Connection Mode Not Ready (Dates)
      </Link>
      <Link href="/(stories)/components/avatar">Avatar</Link>
      <Link href="/(stories)/components/avatar-no-image">
        Avatar - no image
      </Link>
      <Link href="/(stories)/components/avatar-preview">Avatar Preview</Link>
      {spacer}
      <Link href="/(stories)/components/badge">Badge</Link>
      <Link href="/(stories)/components/levels/journey-step">Journey Step</Link>
      <Link href="/(stories)/components/levels/journey-step-corner-cases">
        Journey Step Corner Cases
      </Link>
      <Link href="/(stories)/components/levels/cache-image-404">
        Cache Image 404
      </Link>
      <Link href="/(stories)/components/levels/stats-card">Stats Card</Link>
      {spacer}
      <Link href="/(stories)/(news)/page/without-gists">
        News Page (Without Gists)
      </Link>
      <Link href="/(stories)/(news)/page/with-gists">
        News Page (With Gists)
      </Link>
      {spacer}
      <Link href="/(stories)/(news)/article-layouts/mini-article">
        ⭐️ Mini Article Card (Different States)
      </Link>
      <Link href="/(stories)/(news)/article-layouts/mini-article-list">
        ⭐️ Mini Article List
      </Link>
      <Link href="/(stories)/(news)/article-layouts/scrollable-list-view">
        Article Scrollable List View
      </Link>
      <Link href="/(stories)/(news)/article-card">Article Card</Link>
      <Link href="/(stories)/(news)/article-card-html">Article Card HTML</Link>
      <Link href="/(stories)/(news)/article-grid-layout">
        Article Grid Layout
      </Link>
      <Link href="/(stories)/(news)/article-layouts/full-image">
        Article Full Image
      </Link>
      <Link href="/(stories)/(news)/article-spotlight-layout">
        Article Spotlight Layout
      </Link>
      <Link href="/(stories)/(news)/article-horizontal-layout">
        Article Horizontal Layout
      </Link>
      <Link href="/(stories)/(news)/feed/news">News feed</Link>
      <Link href="/(stories)/(news)/feed/finish-reading">
        ⭐️ News feed (With "Finish Reading")
      </Link>
      <Link href="/(stories)/(news)/feed/finish-reading-full-rated">
        Empty "Finish Reading" because all full rated
      </Link>
      <Link href="/(stories)/(news)/feed/news-hide-match-moods">
        News feed (hide match moods)
      </Link>
      <Link href="/(stories)/(news)/feed/news-update-alert">
        News feed (update alert)
      </Link>
      <Link href="/(stories)/(news)/feed/news-two-alerts">
        News feed (two alerts)
      </Link>
      <Link href="/(stories)/(news)/article-layouts/read-rated-cover">
        Read and rated cover component
      </Link>
      <Link href="/(stories)/(news)/feed/news-all-read-and-rated">
        News feed (all articles read and rated)
      </Link>
      <Link href="/(stories)/(news)/feed/news-feed-no-articles-yet">
        News feed (no articles yet)
      </Link>
      <Link href="/(stories)/(news)/feed/news-feed-no-badge-overlap">
        News feed (a few sections to test badge overlap)
      </Link>
      <Link href="/(stories)/(news)/feed/news-announcements">
        News feed (Announcements)
      </Link>
      <Link href="/(stories)/(news)/feed/news-announcements-in-app-destinations">
        News feed (Announcements with in app destination)
      </Link>
      <Link href="/(stories)/(news)/feed/news-welcome">
        News feed (Welcome)
      </Link>
      <Link href="/(stories)/(news)/feed/footer-social">
        News feed footer (social)
      </Link>
      <Link href="/(stories)/(news)/feed/footer-news-only">
        News feed footer (news only)
      </Link>
      <Link href="/(stories)/(news)/share-reminder">Share Reminder</Link>
      <Link href="/(stories)/(news)/article-page">Article page</Link>
      {spacer}
      <Link href="/(stories)/components/stats-bar/initial-data">
        Stats Bar (With initial data, to test no animation)
      </Link>
      <Link href="/(stories)/components/stats-bar/first-level">
        Stats Bar (First Level)
      </Link>
      <Link href="/(stories)/components/stats-bar/zero-points">
        Stats Bar (Zero Points)
      </Link>
      <Link href="/(stories)/components/stats-bar/points-but-no-level">
        Stats Bar (Points but no level)
      </Link>
      <Link href="/(stories)/components/stats-bar/last-level">
        Stats Bar (Last Level)
      </Link>
      <Link href="/(stories)/components/stats-bar/many-points">
        Stats Bar (Many Points)
      </Link>
      <Link href="/(stories)/components/stats-bar/animated-stats-bar">
        Animated Stats Bar
      </Link>
      <Link href="/(stories)/components/stats-bar/slow-loading">
        Stats Bar (Slow loading)
      </Link>
      {spacer}
      <BoldText>Gists</BoldText>
      <Link href="/(stories)/components/gists/section-selector">
        Section Selector
      </Link>
      <Link href="/(stories)/components/gists/gist-card">Gist Card</Link>
      <Link href="/(stories)/components/gists/gist-card-all-colors">
        Gist Card (All Colors)
      </Link>
      <Link href="/(stories)/components/gists/gist-card-no-summary">
        Gist Card (No Summary)
      </Link>
      <Link href="/(stories)/components/gists/gist-card-invalid-image">
        Gist Card (Invalid Image)
      </Link>
      <Link href="/(stories)/components/gists/gist-card-long-summary">
        Gist Card (Long Summary)
      </Link>
      <Link href="/(stories)/components/gists/report-ai-summary">
        Report AI Summary Modal
      </Link>
      <Link href="/(stories)/components/gists/swipe-indicator">
        Swipe Indicator
      </Link>
      <Link href="/(stories)/components/gists/gists-stack">Gists Stack</Link>
      <Link href="/(stories)/components/gists/gists-stack-empty">
        Gists Stack (Empty)
      </Link>
      <Link href="/(stories)/components/gists/stack-with-full-ratings">
        Gists Stack (With full ratings)
      </Link>
      <Link href="/(stories)/components/gists/stack-trigger-full-rating">
        Gists Stack (Triggering full rating)
      </Link>
      <Link href="/(stories)/components/gists/gist-card-swipe-hint">
        Gist Stack (Swipe hint)
      </Link>
      <Link href="/(stories)/components/gists/gists-page-random-points">
        Gists Page (Random points)
      </Link>
      <Link href="/(stories)/components/gists/gists-page">Gists Page</Link>
      {spacer}
      <Link href="/(stories)/(news)/article-survey">Article Survey</Link>
      <Link href="/(stories)/(news)/article-survey-level-up">
        Article Survey (Level Up)
      </Link>
      <Link href="/(stories)/(news)/article-survey-fake-site">
        Article Survey (With Fake Site)
      </Link>
      <Link href="/(stories)/(news)/survey-start-button">
        Survey Start Button
      </Link>
      <Link href="/(stories)/(news)/survey-completion/first-level">
        Survey Completion Step
      </Link>
      <Link href="/(stories)/(news)/survey-completion/getting-first-level">
        Survey Completion Step (Getting First Level)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/level-2">
        Survey Completion (Level 2)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/level-3">
        Survey Completion (Level 3)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/completing-with-streak">
        Survey Completion Step (Completing with Streak)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/many-points">
        Survey Completion Step (Many Points)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/points-when-leveling-up">
        Survey Completion Leveling Up
      </Link>
      <Link href="/(stories)/(news)/survey-completion/points-when-leveling-up-exactly">
        Survey Completion Leveling Up Exactly
      </Link>
      <Link href="/(stories)/(news)/survey-completion/past-last-level">
        Survey Completion (Past Last Level)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/repeat-survey">
        Repeat Survey
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard">
        Leaderboard
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard-no-image">
        Leaderboard (No Image)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard-mid-ranks">
        Leaderboard (Mid Ranks)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard-big-rank-numbers">
        Leaderboard (Big Rank Numbers)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard-big-rank-and-points">
        Leaderboard (Big Rank and Points)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard-large-versions">
        Leaderboard (Large Versions)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/leaderboard-long-name">
        Leaderboard (Long Name)
      </Link>
      <Link href="/(stories)/(news)/survey-completion/completing-with-leaderboard">
        Completing with Leaderboard
      </Link>
      <Link href="/(stories)/(news)/survey-completion/level-up">Level Up</Link>
      <Link href="/(stories)/(news)/survey-completion/streak">Streak</Link>
      <Link href="/(stories)/(news)/rating-drawer">Rating Drawer</Link>
      {spacer}
      <Link href="/(stories)/chats/chat-preview">Chat Preview</Link>
      <Link href="/(stories)/chats/chat-screen">Chat Screen</Link>
      <Link href="/(stories)/chats/chat-live">Chat Live</Link>
      <Link href="/(stories)/leads/leads">Leads</Link>
      <Link href="/(stories)/leads/close-user">Leads (Close User)</Link>
      <Link href="/(stories)/leads/leads-friends">Leads Friends</Link>
      <Link href="/(stories)/leads/shared-interests-panel">
        Shared Interests Panel
      </Link>
      <Link href="/(stories)/leads/lead-card">Lead Card</Link>
      <Link href="/(stories)/leads/lead-card-all-topic-colors">
        Lead Card (All Topic Colors)
      </Link>
      <Link href="/(stories)/leads/leads-more-scoops-than-images">
        Leads More Scoops Than Images
      </Link>
      <Link href="/(stories)/leads/leads-more-images-than-scoops">
        Leads More Images Than Scoops
      </Link>
      <Link href="/(stories)/leads/lead-card-no-bio">Leads No Bio</Link>
      <Link href="/(stories)/leads/lead-card-top">Lead Card Top</Link>
      <Link href="/(stories)/leads/lead-card-top-generated">
        Lead Card Top (Generated user)
      </Link>
      <Link href="/(stories)/leads/lead-card-no-topics">
        Lead Card (No topics)
      </Link>
      <Link href="/(stories)/leads/leads-empty">Leads Empty</Link>
      <Link href="/(stories)/leads/leads-missing-image">
        Leads Missing Image
      </Link>
      <Link href="/(stories)/leads/leads-mode-not-activated">
        Leads Mode Not Activated
      </Link>
      <Link href="/(stories)/leads/leads-max-reached">Leads Max Reached</Link>
      <Link href="/(stories)/leads/match-celebration">Match Celebration</Link>
      <Link href="/(stories)/leads/teaser">Leads - Teaser</Link>
      {spacer}
      <Link href="/(stories)/scan-matching/scanned-match-modal">
        Scanned Match Modal
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-modal-no-topics">
        Scanned Match Modal (No topics)
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-modal-already-connected">
        Scanned Match Modal (Already connected)
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-modal-no-friends-mode">
        Scanned Match Modal (No friends mode)
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-zero-score">
        Scanned Match Zero Score
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-live-no-friends-mode">
        Scanned Match Live (No friends mode)
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-news-only">
        Scanned Match News Only
      </Link>
      <Link href="/(stories)/scan-matching/scanned-match-no-image">
        Scanned Match No Image
      </Link>
      {spacer}
      <Link href="/(stories)/matches/matches-page-empty-state">
        Matches Page Empty State
      </Link>
      <Link href="/(stories)/matches/matches-page-loading">
        Matches Page Loading
      </Link>
      <Link href="/(stories)/matches/matches-page-client-disconnected">
        Matches Page Client Disconnected
      </Link>
      <Link href="/(stories)/matches/matches-page-with-matches-and-requests">
        Matches Page with matches and requests
      </Link>
      <Link href="/(stories)/matches/matches-page-without-matches-with-requests">
        Matches Page without matches with requests
      </Link>
      <Link href="/(stories)/matches/matches-page-user-undefined">
        Matches Page with user undefined
      </Link>
      <Link href="/(stories)/matches/matches-page-mode-not-activated">
        Matches Page - mode not activated
      </Link>
      <Link href="/(stories)/matches/teaser">Matches - Teaser</Link>
      {spacer}
      <Link href="/(stories)/(account)/progress/inscore-no-points">
        InScore Progress (No points)
      </Link>
      <Link href="/(stories)/(account)/progress/inscore-no-level">
        InScore Progress (No level)
      </Link>
      <Link href="/(stories)/(account)/progress/inscore-newbie">
        InScore Progress (Newbie)
      </Link>
      <Link href="/(stories)/(account)/progress/inscore-learner">
        InScore Progress (Learner)
      </Link>
      <Link href="/(stories)/(account)/progress/inscore-goat">
        InScore Progress (GOAT)
      </Link>
      {spacer}
      <Link href="/(stories)/notifications/notification-feed">
        Notification Feed
      </Link>
      <Link href="/(stories)/notifications/announcement-notification">
        Announcement Notification
      </Link>
      <Link href="/(stories)/notifications/new-friend-request-item">
        New Friend Request Item
      </Link>
      <Link href="/(stories)/notifications/new-friend-request-item-no-friends-profile">
        New Friend Request Item (No friends profile created)
      </Link>
      <Link href="/(stories)/notifications/new-like-notification">
        New Like Notification
      </Link>
      <Link href="/(stories)/notifications/new-like-notification-no-user">
        New Like Notification (No user)
      </Link>
      <Link href="/(stories)/notifications/levels-notifications">
        Levels Notifications
      </Link>
      {spacer}
      <Link href="/(stories)/(account)/account">Account</Link>
      <Link href="/(stories)/(account)/account-news-only">
        Account (News Only)
      </Link>
      <Link href="/(stories)/(account)/account-with-level">
        Account (With Level)
      </Link>
      <Link href="/(stories)/(account)/account-no-image">
        Account (No profile pic)
      </Link>
      <Link href="/(stories)/(account)/account-uploading">
        Account (Uploading)
      </Link>
      <Link href="/(stories)/(account)/account-no-friends-mode">
        Account (No friends mode)
      </Link>
      <Link href="/(stories)/(account)/account-with-friends-mode">
        Account (With friends mode)
      </Link>
      <Link href="/(stories)/(account)/profile">Profile</Link>
      <Link href="/(app)/account/qr-code-screen">Qr Code</Link>
      <Link href="/(stories)/(account)/edit-profile">Edit Profile</Link>
      <Link href="/(stories)/(account)/edit-profile-one-pic">
        Edit Profile (One pic)
      </Link>
      <Link href="/(stories)/(account)/edit-profile-auto-update-loc">
        Edit Profile (Auto update location)
      </Link>
      <Link href="/(stories)/(account)/edit-news-profile">
        Edit News Profile
      </Link>
      <Link href="/(stories)/(account)/edit-news-profile-auto-update-loc">
        Edit News Profile (Auto update location)
      </Link>
      <Link href="/(stories)/(account)/match-preferences">
        Match Preferences
      </Link>
      <Link href="/(stories)/(account)/match-preferences-both-activated">
        Match Preferences (both activated)
      </Link>
      <Link href="/(stories)/(account)/gender-preference-section">
        Gender Preference Section
      </Link>
      <Link href="/(stories)/(account)/age-preference-section">
        Age Preference Section
      </Link>
      <Link href="/(stories)/(account)/similarity-preference-section">
        Similarity Preference Section
      </Link>
      <Link href="/(stories)/(account)/set-location-section">
        Set Location Section
      </Link>
      <Link href="/(stories)/(account)/change-password">Change Password</Link>
      <Link href="/(stories)/(account)/journey-screen">Journey Screen</Link>
      <Link href="/(stories)/(account)/journey-screen-no-level">
        Journey Screen (No Level)
      </Link>
      <Link href="/(stories)/(account)/journey-screen-no-image">
        Journey Screen (No Image)
      </Link>
      <Link href="/(stories)/(account)/journey-screen-old">
        Journey Screen (Old)
      </Link>
      <Link href="/(stories)/(account)/general-settings">General Settings</Link>
      <Link href="/(stories)/screen-header">Screen Header</Link>
      <Link href="/(stories)/empty-state">Empty State</Link>
      <Link href="/(stories)/update-required">Update Required</Link>
      <Link href="/(stories)/gist-card-video">Gist Card Video</Link>
    </ScrollView>
  )
}
