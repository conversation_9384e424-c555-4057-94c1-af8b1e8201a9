import { LeadsPage_ } from "@/components/LeadsPage"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { DEFAULT_PROPS } from "./leads"
import _ from "lodash"

const props = {
  ..._.set(DEFAULT_PROPS, "user.friendsModeIsActivated", true),
  connectionMode: ConnectionMode.Friends,
}
export default function LeadsStory() {
  return <LeadsPage_ {...props} connectionMode={ConnectionMode.Friends} />
}
