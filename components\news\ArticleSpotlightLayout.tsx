import { useState } from "react"
import { ScrollView, StyleSheet, View } from "react-native"
import { Card, Title } from "react-native-paper"
import {
  SPOTLIGHT_ARTICLE_HEIGHT_FN as getSpotlightArticleCardHeight,
  SpotlightArticleCard,
} from "./SpotlightArticleCard"
import { newsfeedMarginHorizontal, newsStyles } from "./constants"
import ArticleSource from "./ArticleSource"
import Footer, { FOOTER_HEIGHT } from "./Footer"
import { ArticleMatchPreview } from "@/apiQueries/newsFeed"
import { MATCH_MOODS_HEIGHT } from "./MatchMoodsWidget"
import { ReadRatedCover } from "./ReadRatedCover"
import { isFeatureEnabled, useFeatureFlag } from "@/utils/featureFlags"
import ReportArticleModal from "./ReportArticleModal"
import { openArticle } from "../ArticlePage"
import { Article } from "@/types/news"

interface ArticleSpotlightLayoutProps {
  articles: Article[]
  matchPreviews: ArticleMatchPreview[]
  hideMatchMoods: boolean
}

export const ArticleSpotlightLayout = ({
  articles,
  matchPreviews,
  hideMatchMoods,
}: ArticleSpotlightLayoutProps) => {
  const newReadRatedFlag = useFeatureFlag("new_read_rated_design")

  const [isReporting, setIsReporting] = useState(false)

  const handlePress = async (article: Article) => {
    openArticle(article)
  }

  const matchPreview = matchPreviews.find(
    (mood) => mood.articleId === articles[0].id,
  )

  return (
    <View
      style={[
        {
          height:
            HEIGHT_FN(isFeatureEnabled("new_read_rated_design")) +
            (hideMatchMoods ? 0 : MATCH_MOODS_HEIGHT),
        },
      ]}
    >
      <View
        style={[
          styles.spotlightArticle,
          { marginBottom: SPOTLIGHT_MARGIN_BOTTOM_FN(newReadRatedFlag) },
        ]}
      >
        <SpotlightArticleCard
          article={articles[0]}
          imageHeight={216}
          matchPreview={matchPreview}
          hideMatchMoods={hideMatchMoods}
          onPress={() => handlePress(articles[0])}
        />
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={
          SECONDARY_ARTICLE_WIDTH + SECONDARY_ARTICLE_MARGIN_RIGHT
        }
        snapToAlignment="start"
        decelerationRate="fast"
        style={styles.secondaryArticles}
      >
        {articles.slice(1).map((article) => (
          <Card
            key={article.id}
            style={[
              styles.secondaryArticle,
              { height: SECONDARY_ARTICLE_HEIGHT_FN(newReadRatedFlag) },
            ]}
            onPress={() => handlePress(article)}
            onLongPress={() => setIsReporting(true)}
          >
            <Card.Content style={{ height: "100%" }}>
              <ArticleSource article={article} />
              <Title style={styles.secondaryArticleTitle} numberOfLines={3}>
                {article.title}
              </Title>
              {newReadRatedFlag ? (
                <ReadRatedCover article={article} />
              ) : (
                <Footer
                  isRead={article.isOpened}
                  isRated={article.isSurveyed}
                  articleId={article.id}
                />
              )}
              <ReportArticleModal
                articleId={article.id}
                modalVisible={isReporting}
                onClose={() => setIsReporting(false)}
              />
            </Card.Content>
          </Card>
        ))}
      </ScrollView>
    </View>
  )
}

const SPOTLIGHT_MARGIN_BOTTOM_FN = (newReadRatedFlag: boolean) =>
  newReadRatedFlag ? 50 : 60

const SECONDARY_ARTICLE_HEIGHT_FN = (newReadRatedFlag: boolean) =>
  newReadRatedFlag ? 135 : 162

export const HEIGHT_FN = (newReadRatedFlag: boolean) =>
  getSpotlightArticleCardHeight(newReadRatedFlag) +
  SPOTLIGHT_MARGIN_BOTTOM_FN(newReadRatedFlag) +
  SECONDARY_ARTICLE_HEIGHT_FN(newReadRatedFlag) +
  FOOTER_HEIGHT

const SECONDARY_ARTICLE_WIDTH = 276
const SECONDARY_ARTICLE_MARGIN_RIGHT = 20

const styles = StyleSheet.create({
  spotlightArticle: {
    marginHorizontal: newsfeedMarginHorizontal,
  },
  secondaryArticles: {
    paddingLeft: newsfeedMarginHorizontal,
    paddingVertical: 1,
    gap: 16,
  },
  secondaryArticle: {
    width: SECONDARY_ARTICLE_WIDTH,
    marginRight: SECONDARY_ARTICLE_MARGIN_RIGHT,
  },
  secondaryArticleTitle: {
    ...newsStyles.smallArticleTitle,
    marginBottom: 20,
  },
})
