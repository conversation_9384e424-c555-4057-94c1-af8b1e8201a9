import { View, Text } from "react-native"
import { <PERSON><PERSON>ist, ChannelPreview<PERSON>essenger, <PERSON><PERSON> } from "stream-chat-expo"
import { TextInput } from "@/components/TextInput"
import { useEffect, useState } from "react"
import { getMatches, Match } from "@/apiQueries/apiQueries"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { Button } from "@/components/Button"
import { ChatProvider, initializeChat } from "@/chatContext"
import { StreamChat, DefaultGenerics } from "stream-chat"
import { Screen } from "@/components/Themed"
import { filterChannels } from "@/components/MatchesPage"
import _ from "lodash"
import { Session } from "@/types/user"
import { getSession } from "@/apiQueries/auth"

const TOKEN = ""

export default function Story() {
  const [token, setToken] = useState<string>(TOKEN)
  const [session, setSession] = useState<Session>()
  const [matches, setMatches] = useState<Match[]>([])
  const [connectionMode, setConnectionMode] = useState<ActiveConnectionMode>(
    ConnectionMode.Dates,
  )
  const [client, setClient] = useState<StreamChat<DefaultGenerics> | null>(null)

  useEffect(() => {
    ;(async () => {
      if (!token) return

      const session = await getSession(token)
      if (!session) return
      setSession(session)

      const matches = await getMatches({ token, connectionMode })
      setMatches(matches)
    })()
  }, [token])

  useEffect(() => {
    if (!session || client) return

    initializeChat({ session, handleEvent: _.noop }).then(setClient)
  }, [session])

  useEffect(() => {
    console.log("Getting matches")
    getMatches({ token, connectionMode }).then(setMatches)
  }, [connectionMode])

  if (!session || !client) return null

  const filters = {
    type: "messaging",
    members: {
      $in: [session.user.id.toString()],
    },
  }

  return (
    <ChatProvider>
      <Chat client={client}>
        <Screen style={{ paddingHorizontal: 0 }}>
          <TextInput label="Token" value={token} onChangeText={setToken} />
          <Button
            text="Toggle mode"
            onPress={() =>
              setConnectionMode(
                connectionMode === ConnectionMode.Dates
                  ? ConnectionMode.Friends
                  : ConnectionMode.Dates,
              )
            }
          />
          <Button
            text="Reinitialize chat"
            onPress={() => {
              if (session)
                initializeChat({ session, handleEvent: _.noop }).then(setClient)
            }}
          />
          <Text>Session connected, user ID: {session?.user.id.toString()}</Text>
          <Text>{connectionMode}</Text>
          <Text>Match count: {matches.length}</Text>
          <Text>Chat client user ID: {client.userID}</Text>
          <Text>{JSON.stringify(filters)}</Text>
          <Text>Channels</Text>
          <ChannelList
            Preview={(props) => (
              <View>
                <ChannelPreviewMessenger {...props} />
              </View>
            )}
            filters={filters}
            channelRenderFilterFn={(channels) =>
              filterChannels({ matches, channels })
            }
          />
          <Text>End</Text>
        </Screen>
      </Chat>
    </ChatProvider>
  )
}
