import { requestLocationPermission } from "@/utils/location"
import SettingsSwitch from "./SettingsSwitch"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { LAST_LOC_UPDATE_TIME_KEY } from "@/utils/localStorage"

export const LocationSwitch = ({
  value,
  onChange,
}: {
  value: boolean
  onChange: (value: boolean) => void
}) => {
  const handleChange = async (autoUpdateLocation: boolean) => {
    let allowChange = false
    if (autoUpdateLocation) {
      const permissionStatus = await requestLocationPermission()
      if (permissionStatus === "granted") {
        await AsyncStorage.removeItem(LAST_LOC_UPDATE_TIME_KEY)
        allowChange = true
      }
    } else {
      allowChange = true
    }

    if (allowChange) {
      onChange(autoUpdateLocation)
    }

    return allowChange
  }

  return (
    <SettingsSwitch
      title="Automatically update location"
      subtitle="For local news and leads"
      initialValue={value}
      onChange={handleChange}
    />
  )
}
