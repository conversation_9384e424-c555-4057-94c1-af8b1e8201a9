import { NEWSFEED_ARTICLES } from "../(news)/feed/news"
import GistsStep from "@/components/signInOrUp/GistsStep"
import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { router } from "expo-router"
import { useHideHeader } from "../story_utils"

export default function Story() {
  useHideHeader()

  return (
    <SignUpWrapper
      title="Get the Gist"
      subtitle="Gists help you get straight to the point while earning points to track how much you learn."
      progress={0.75}
    >
      <GistsStep
        articles={NEWSFEED_ARTICLES.slice(0, 2)}
        onComplete={() => router.back()}
      />
    </SignUpWrapper>
  )
}
