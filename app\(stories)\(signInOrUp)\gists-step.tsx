import GistsStep, { SUB<PERSON>TLE, TITLE } from "@/components/signInOrUp/GistsStep"
import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { router } from "expo-router"
import { useHideHeader } from "../story_utils"

export default function Story() {
  useHideHeader()

  return (
    <SignUpWrapper
      title={TITLE}
      subtitle={SUBTITLE}
      progress={0.75}
      onBack={() => router.back()}
      onNext={() => {}}
    >
      <GistsStep onComplete={() => router.back()} />
    </SignUpWrapper>
  )
}
