import { Button } from "@/components/Button"
import { Screen } from "@/components/Themed"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { openAppStore } from "@/utils/general"
import { EventType, trackEvent } from "@/utils/tracking"
import { useEffect } from "react"

export const UpdateRequired = () => {
  useEffect(() => {
    trackEvent(EventType.UpdateRequiredSeen)
  }, [])

  return (
    <Screen style={{ paddingTop: 100 }}>
      <ScreenHeader
        title="Update Required"
        subtitle={"Please update InPress to continue using it."}
      />
      <Button text="Update" onPress={() => openAppStore()} />
    </Screen>
  )
}
