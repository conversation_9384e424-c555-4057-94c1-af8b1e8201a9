import NotificationsIconLarge from "@/components/icons/onboarding/NotificationsIconLarge"
import NotificationStep, {
  SUBTITLE,
  TITLE,
} from "@/components/signInOrUp/NotificationStep"
import { SignUpWrapper } from "@/components/signInOrUp/SignUpWrapper"
import { View } from "@/components/Themed"
import { useState } from "react"

export default function Story() {
  const [token, setToken] = useState<string | undefined>()
  const handleNext = async (token: string | undefined) => {
    console.log("Token:", token)
    setToken(token)
  }

  if (!token) {
    return (
      <SignUpWrapper
        title={TITLE}
        subtitle={SUBTITLE}
        iconComponent={NotificationsIconLarge}
        onBack={() => {}}
      >
        <NotificationStep onNext={handleNext} />
      </SignUpWrapper>
    )
  } else {
    return (
      <SignUpWrapper
        title="Next step after notifications"
        subtitle={`Push token: ${token}`}
      >
        <View></View>
      </SignUpWrapper>
    )
  }
}
