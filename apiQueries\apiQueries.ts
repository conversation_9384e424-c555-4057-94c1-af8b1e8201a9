import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { FinishedSurvey } from "../components/ratings/ArticleSurvey"
import { get, post } from "../network"
import { INPRESS_API_URL } from "./constants"
import {
  convertRawPreferences,
  convertRawUser,
  RawPreferences,
  RawUser,
  User,
} from "@/types/user"
import { PointEvent } from "@/types/levels"
import { Article, ArticleReport, RawArticle } from "@/types/news"
import { convertRawArticle } from "@/utils/rawConversion"
import {
  combineTopicFields,
  convertRawLead,
  Lead,
  RawLead,
  RawTopic,
  Topic,
} from "@/types/social"

export const getNewsArticles = async (token: string): Promise<Article[]> => {
  const articles = await get<null, RawArticle[]>(
    `${INPRESS_API_URL}/articles/today`,
    token,
  )
  return articles.map(convertRawArticle)
}

interface GetLeadsApiParams {
  connection_mode: ConnectionMode
  include_max_leads_reached: boolean
}

interface GetLeadsResponse {
  leads: RawLead[]
  max_leads_reached: boolean
}

export const getLeads = async ({
  token,
  connectionMode,
}: {
  token: string
  connectionMode: ConnectionMode
}): Promise<{
  leads: Lead[]
  maxLeadsReached: boolean
}> => {
  const { leads: rawLeads, max_leads_reached: maxLeadsReached } = await get<
    GetLeadsApiParams,
    GetLeadsResponse
  >(`${INPRESS_API_URL}/leads`, token, {
    connection_mode: connectionMode,
    include_max_leads_reached: true,
  })
  const leads = rawLeads.map(convertRawLead)
  return { leads, maxLeadsReached }
}

interface SubmitSurveyApiParams {
  article_id: number
  feel: string
  care: number
  interest: number
}

type RawSurveyResponse = {
  point_events: PointEvent[]
  leveled_up: boolean
}

export type SurveyResponse = {
  pointEvents: PointEvent[]
}

export type SubmitSurveyProps = {
  token: string
  articleId: number
  survey: FinishedSurvey
}

export const submitSurvey = async ({
  token,
  articleId,
  survey,
}: SubmitSurveyProps): Promise<SurveyResponse> => {
  const response = await post<SubmitSurveyApiParams, RawSurveyResponse>(
    `${INPRESS_API_URL}/surveys`,
    {
      article_id: articleId,
      feel: JSON.stringify(survey.feelings),
      care: survey.importanceRating,
      interest: survey.interestRating,
    },
    token,
  )

  return { pointEvents: response.point_events }
}

export const getReferralCode = async (token: string): Promise<string> => {
  const { referral_code } = await get<null, { referral_code: string }>(
    `${INPRESS_API_URL}/referral-code`,
    token,
  )
  return referral_code
}

export type SwipeType = "like" | "pass"

export interface Swipe {
  swipedUserId: number
  type: SwipeType
}

type RawSwipe = {
  swiped_user_id: number
  type: SwipeType
  connection_mode: ConnectionMode
}

export type SwipeResponse = {
  isMutual: boolean
}

export const swipeLead = async ({
  token,
  swipe,
  connectionMode,
}: {
  token: string
  swipe: Swipe
  connectionMode: ConnectionMode
}): Promise<SwipeResponse> => {
  const { is_mutual } = await post<RawSwipe, { is_mutual: boolean }>(
    `${INPRESS_API_URL}/add-swipe`,
    {
      swiped_user_id: swipe.swipedUserId,
      type: swipe.type,
      connection_mode: connectionMode,
    },
    token,
  )

  return { isMutual: is_mutual }
}
export interface Match {
  user: User
  score: number
  topics: Topic[]
  chatChannelId: string
}

type GetMatchesResponse = {
  user: RawUser
  score: number
  topics: string[]
  topics_with_roots: RawTopic[] | null
  chat_channel_id: string
}

export const getMatches = async ({
  token,
  connectionMode,
}: {
  token: string
  connectionMode: ConnectionMode
}): Promise<Match[]> => {
  const response = await get<
    { connection_mode: ConnectionMode; with_leads: boolean },
    GetMatchesResponse[]
  >(`${INPRESS_API_URL}/matches`, token, {
    connection_mode: connectionMode,
    with_leads: true,
  })

  const matchesWithoutChannelId = response.filter(
    ({ chat_channel_id }) => !chat_channel_id,
  )

  if (matchesWithoutChannelId.length > 0) {
    console.error(
      `Match users without chat channel id: ${matchesWithoutChannelId.map(
        (m) => m.user.id,
      )}`,
    )
  }

  return response.map(
    ({ user, chat_channel_id, topics, topics_with_roots, ...rest }) => ({
      user: convertRawUser(user),
      chatChannelId: chat_channel_id,
      topics: combineTopicFields(topics, topics_with_roots),
      ...rest,
    }),
  )
}

export type ConfigurableEventProps = {
  data?: object
  route?: string
  route_params?: object
  opened_article_id?: number
  onboarding_step?: string
}

export interface Event extends ConfigurableEventProps {
  user_id?: number
  anonymous_user_id: string
  type: string
  device_id: string
  device_brand: string | null
  device_model: string | null
  os_name: string | null
  os_version: string | null
  locale: string
  environment?: string
  app_version: number
}

interface TrackEventParams {
  event: Omit<Event, "data" | "route_params"> & {
    data: string
    route_params: string
  }
}

export const postTrackingEvent = async (event: Event, token: string) => {
  try {
    await post<TrackEventParams, void>(
      `${INPRESS_API_URL}/track-event`,
      {
        event: {
          ...event,
          data: JSON.stringify(event.data),
          route_params: JSON.stringify(event.route_params),
        },
      },
      token,
      null,
      false,
    )
  } catch (e) {
    console.error(`Failed to track user event: ${event.type}`, e)
  }
}

type ReportMatchParams = {
  reported_channel_id: string
  reported_user_id: number
}

export const reportMatch = async ({
  token,
  channelId,
  reportedUserId,
}: {
  token: string
  channelId: string
  reportedUserId: number
}) => {
  await post<ReportMatchParams, void>(
    `${INPRESS_API_URL}/report-match`,
    { reported_channel_id: channelId, reported_user_id: reportedUserId },
    token,
  )
}

type ReportLeadParams = {
  reported_user_id: number
}
export const reportLeadUser = async (token: string, userId: number) => {
  await post<ReportLeadParams, void>(
    `${INPRESS_API_URL}/report-lead-user`,
    { reported_user_id: userId },
    token,
  )
}

export type ReportArticleParams = {
  token: string
} & ArticleReport

export const reportArticle = async ({
  token,
  reason,
  note,
  articleId,
}: ReportArticleParams) => {
  await post(
    `${INPRESS_API_URL}/report-article`,
    {
      type: reason,
      note,
      article_id: articleId,
    },
    token,
  )
}

export const getPreferencesOfBothModes = async ({
  token,
  userId,
}: {
  token: string
  userId: number
}) => {
  const { dates_preferences, friends_preferences } = await get<
    { user_id: number },
    {
      dates_preferences: RawPreferences | null
      friends_preferences: RawPreferences | null
    }
  >(`${INPRESS_API_URL}/user-preferences-both-modes`, token, {
    user_id: userId,
  })

  return {
    datesPreferences: dates_preferences
      ? convertRawPreferences(dates_preferences)
      : undefined,
    friendsPreferences: friends_preferences
      ? convertRawPreferences(friends_preferences)
      : undefined,
  }
}

export const getStreamChatToken = async (token: string) => {
  const { token: chatToken } = await get<null, { token: string }>(
    `${INPRESS_API_URL}/stream-chat-token`,
    token,
  )
  return chatToken
}
