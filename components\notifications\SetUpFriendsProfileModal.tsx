import { useEffect, useState } from "react"
import {
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  Animated,
} from "react-native"
import { Button } from "../Button"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import NewspaperIcon from "../icons/NewspaperIcon"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { pushWithParams } from "@/utils/localParams"
import { activateModePath } from "@/utils/deepLinks"
import { ActivateParams } from "@/app/(signInOrUp)/activate-mode"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"
import { fontStyles } from "@/styles"

type SetUpFriendsProfileModalProps = {
  visible: boolean
  onClose: () => void
}

export default function SetUpFriendsProfileModal({
  visible,
  onClose,
}: SetUpFriendsProfileModalProps) {
  const [slideInAnim] = useState(new Animated.Value(700))

  useEffect(() => {
    if (visible) {
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }).start()
    } else {
      Animated.timing(slideInAnim, {
        toValue: 700,
        duration: 400,
        useNativeDriver: true,
      }).start()
    }
  }, [visible])

  const handleButtonPress = () => {
    pushWithParams<ActivateParams>({
      pathname: activateModePath,
      params: { presetConnectionMode: ConnectionMode.Friends },
    })
  }

  return (
    <Modal transparent={true} visible={visible} onRequestClose={onClose}>
      <TouchableOpacity
        style={styles.background}
        activeOpacity={1}
        onPressOut={(e) => {
          if (e.target === e.currentTarget) {
            onClose()
          }
        }}
      >
        <Animated.View
          style={[
            styles.bottomSheet,
            { height: hp(45), transform: [{ translateY: slideInAnim }] },
          ]}
        >
          <Text style={styles.title}>You're accepting a friend request</Text>
          <Text style={styles.subtitle}>
            Set up your Friends profile to accept friend requests and chat.
          </Text>
          <Button
            text="Create Friends Profile"
            isTextOnLeft
            iconComponent={<NewspaperIcon />}
            onPress={handleButtonPress}
          />
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  )
}

const styles = StyleSheet.create({
  background: {
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    position: "absolute",
    left: 0,
    right: 0,
    justifyContent: "flex-end",
    top: 0,
    bottom: 0,
    height: "auto",
  },
  bottomSheet: {
    justifyContent: "center",
    gap: 30,
    alignItems: "center",
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 41,
    textAlign: "center",
    ...fontStyles.editorial,
  },
  subtitle: {
    maxWidth: wp(80),
    fontSize: 15,
    textAlign: "center",
  },
})
