import { SecretlyTappableLogo } from "@/components/signInOrUp/SecretlyTappableLogo"
import { Screen } from "@/components/Themed"
import {
  Feature,
  TestingProvider,
  useTestingContext,
} from "@/context/TestingContext"
import { Text } from "react-native"

const TestApp = () => {
  const { featureIsOn } = useTestingContext()
  return (
    <Screen
      style={{
        backgroundColor: "#000",
        justifyContent: "center",
        alignItems: "center",
        gap: 20,
      }}
    >
      <Text style={{ color: "white" }}>
        Gists on: {featureIsOn(Feature.Gists) ? "Yes" : "No"}
      </Text>
      <SecretlyTappableLogo />
    </Screen>
  )
}

export default function Story() {
  return (
    <TestingProvider>
      <TestApp />
    </TestingProvider>
  )
}
