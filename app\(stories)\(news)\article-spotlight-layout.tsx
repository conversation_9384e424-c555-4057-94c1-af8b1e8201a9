import { Screen } from "@/components/Themed"
import { MATCH_DATA_LIST, articles } from "./feed/news"
import { ArticleSpotlightLayout } from "@/components/news/ArticleSpotlightLayout"
import { ScrollView } from "react-native"

export default function Story() {
  return (
    <Screen style={{ paddingHorizontal: 10 }}>
      <ScrollView>
        <ArticleSpotlightLayout
          articles={articles}
          matchPreviews={MATCH_DATA_LIST}
          hideMatchMoods={true}
        />
        <ArticleSpotlightLayout
          articles={articles.map((a) => ({
            ...a,
            isOpened: true,
            isSurveyed: true,
          }))}
          matchPreviews={MATCH_DATA_LIST}
          hideMatchMoods={true}
        />
      </ScrollView>
    </Screen>
  )
}
