import { DARK_RED, <PERSON>NT_GRE<PERSON>, <PERSON><PERSON><PERSON>, SAGE_GREEN } from "@/constants/Colors"
import { GistSwipeType } from "@/types/news"
import { ViewStyle, View, StyleSheet, Text } from "react-native"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import Animated from "react-native-reanimated"
import { useLevels } from "@/context/LevelContext"
import { fontStyles } from "@/styles"

type SwipeIndicatorProps = {
  type: GistSwipeType
  overlayStyle: ViewStyle
}

export const SwipeIndicator = ({ type, overlayStyle }: SwipeIndicatorProps) => {
  const { loading, stats, calculateLevel, calculateNextLevel } = useLevels()

  if (loading || !stats) return null

  const currentLevel = calculateLevel(stats.points)
  const nextLevel = calculateNextLevel(stats.points)
  const levelToUse = currentLevel || nextLevel

  if (!levelToUse) return null

  const backgroundColor = type === "like" ? MINT_GREEN : PINK
  const textColor = type === "like" ? SAGE_GREEN : DARK_RED
  const text = type === "like" ? "Interesting" : "Not for me"

  return (
    <Animated.View style={[styles.overlay, overlayStyle]}>
      <View style={[styles.container, { backgroundColor }]}>
        <Text style={[styles.text, { color: textColor }]}>{text}</Text>
      </View>
    </Animated.View>
  )
}

const styles = StyleSheet.create({
  overlay: {
    width: 112,
    height: 116,
    position: "absolute",
    top: hp(13),
    alignItems: "center",
    justifyContent: "space-between",
    opacity: 1,
    backgroundColor: "transparent",
  },
  container: {
    height: 60,
    width: 170,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
  },
  text: {
    fontSize: 24,
    ...fontStyles.editorialUltraboldItalic,
  },
})
