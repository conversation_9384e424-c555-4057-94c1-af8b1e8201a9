import { BROWNSTONE_CTA, <PERSON><PERSON><PERSON>, LIGHT_GREY } from "@/constants/Colors"
import { GistSwipeType } from "@/types/news"
import { ViewStyle, ImageSourcePropType, View, StyleSheet } from "react-native"
import { IMAGES } from "@/constants/Images"
import { NormalText } from "../StyledText"
import { Image } from "expo-image"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"
import Animated from "react-native-reanimated"
import { useLevels } from "@/context/LevelContext"
import { Level } from "@/types/levels"
import { LevelIcon } from "../levels/LevelIcon"

const PointsIndicator = ({ level }: { level: Level }) => (
  <View style={styles.pointsContainer}>
    <LevelIcon level={level} />
    <NormalText>InScore +2</NormalText>
  </View>
)

type SwipeIndicatorProps = {
  type: GistSwipeType
  overlayStyle: ViewStyle
}

export const SwipeIndicator = ({ type, overlayStyle }: SwipeIndicatorProps) => {
  const { loading, stats, calculateLevel, calculateNextLevel } = useLevels()

  if (loading || !stats) return null

  const currentLevel = calculateLevel(stats.points)
  const nextLevel = calculateNextLevel(stats.points)
  const levelToUse = currentLevel || nextLevel

  if (!levelToUse) return null

  const renderIndicator = ({ source }: { source: ImageSourcePropType }) => {
    let backgroundColor
    switch (type) {
      case "like":
        backgroundColor = GREEN
        break
      case "dislike":
        backgroundColor = BROWNSTONE_CTA
        break
      default:
        backgroundColor = "transparent"
    }

    return (
      <Animated.View style={[styles.overlay, overlayStyle]}>
        <View style={[styles.iconContainer, { backgroundColor }]}>
          <Image source={source} style={styles.icon} />
        </View>
        <PointsIndicator level={levelToUse} />
      </Animated.View>
    )
  }

  if (type === "like") {
    return renderIndicator({
      source: IMAGES.Heart,
    })
  } else if (type === "dislike") {
    return renderIndicator({
      source: IMAGES.ThumbsDown,
    })
  }
  return null
}

const styles = StyleSheet.create({
  overlay: {
    width: 112,
    height: 116,
    position: "absolute",
    top: hp(25),
    alignItems: "center",
    justifyContent: "space-between",
    opacity: 1,
    backgroundColor: "transparent",
  },
  iconContainer: {
    height: wp(19.5),
    width: wp(19.5),
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 999,
  },
  leafIcon: {
    height: 14,
    width: 17.5,
  },
  icon: {
    height: 45,
    width: 45,
  },
  pointsContainer: {
    height: 27,
    width: 110,
    backgroundColor: "white",
    borderRadius: 999,
    borderWidth: 1,
    borderColor: LIGHT_GREY,
    gap: 8,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
  },
})
