import { Text, View } from "@/components/Themed"
import { Alert, Platform, StyleSheet } from "react-native"
import { ReactNode, useEffect, useState } from "react"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import { TouchableOpacity } from "react-native-gesture-handler"
import { PlanCard } from "./PlanCard"
import { Button } from "../Button"
import CheckboxCircleIcon from "../icons/CheckboxCircle"
import Purchases, { PurchasesPackage } from "react-native-purchases"
import { ScrollView } from "react-native"

type MembershipStepProps = {
  email: string
  onNext: () => void
}

export const MembershipStep = ({ email, onNext }: MembershipStepProps) => {
  const [selectedPlan, setSelectedPlan] = useState<PurchasesPackage>()
  const [availablePlans, setAvailablePlans] = useState<PurchasesPackage[]>()

  useEffect(() => {
    const setup = async () => {
      const REVENUE_CAT_IOS_KEY = process.env.EXPO_PUBLIC_REVENUE_CAT_IOS_KEY
      const REVENUE_CAT_ANDROID_KEY =
        process.env.EXPO_PUBLIC_REVENUE_CAT_ANDROID_KEY

      if (!REVENUE_CAT_IOS_KEY || !REVENUE_CAT_ANDROID_KEY) {
        throw new Error("Missing required environment variables")
      }

      const userId = `TemporaryUserId-${email}-${new Date().getTime()}`

      if (Platform.OS == "ios") {
        Purchases.configure({
          apiKey: REVENUE_CAT_IOS_KEY,
          appUserID: userId,
        })
      } else {
        Purchases.configure({
          apiKey: REVENUE_CAT_ANDROID_KEY,
          appUserID: userId,
        })
      }

      const offerings = await Purchases.getOfferings()
      setAvailablePlans(offerings.current?.availablePackages)
    }

    setup().catch(console.error)
  }, [])

  const handleConfirmPlan = async () => {
    if (!selectedPlan) {
      return
    }

    try {
      const { customerInfo } = await Purchases.purchasePackage(selectedPlan)

      if (typeof customerInfo.entitlements.active["premium"] !== "undefined") {
        Alert.alert("Purchase successful!", "You're in!")
        onNext()
      } else {
        Alert.alert(
          "Unable to confirm purchase",
          "Please try again later. If the issue persists, please contact InPress support.",
        )
        console.error("Unable to confirm purchase", customerInfo)
      }
    } catch (e: any) {
      console.error("Failed to purchase", e)
      if (e.code !== Purchases.PURCHASES_ERROR_CODE.PURCHASE_CANCELLED_ERROR) {
        Alert.alert("Purchase not completed", e.message)
      }
    }
  }

  return (
    <MembershipStep_
      plans={availablePlans}
      selectedPlan={selectedPlan}
      onSelectPlan={setSelectedPlan}
      onConfirmPlan={handleConfirmPlan}
      onSkip={() => onNext()}
    />
  )
}

interface MembershipStepProps_ {
  plans: undefined | PurchasesPackage[]
  selectedPlan?: PurchasesPackage
  onSelectPlan: (purchasePackage: PurchasesPackage) => void
  onConfirmPlan: () => void
  onSkip: () => void
}

export const MembershipStep_ = ({
  plans,
  selectedPlan,
  onSelectPlan,
  onConfirmPlan,
  onSkip,
}: MembershipStepProps_) => {
  const premiumFeatures = [
    {
      title: "Unlimited Leads",
    },
    {
      title: "Peek",
      description:
        "allows you to see which matches have already swiped right on your profile",
    },
    {
      title: "Headline",
      description:
        "bumps your profile to the front of the local matches feed for one hour a day",
    },
    {
      title: "Tip",
      description:
        "gives you the ability to inform 3 matches-a-day that you are confidently interested in them",
    },
  ]

  return (
    <View style={styles.container}>
      <View style={styles.itemsList}>
        {premiumFeatures.map((feature) => (
          <ListItem
            key={feature.title}
            text={
              <>
                <Text style={styles.bold}>{feature.title}</Text>
                {feature.description && <Text> - {feature.description}</Text>}
              </>
            }
          />
        ))}
      </View>
      <View>
        <ScrollView contentContainerStyle={styles.cardsScroller} horizontal>
          <View style={styles.cardsContainer}>
            {plans ? (
              plans.map((plan) => (
                <PlanCard
                  key={plan.identifier}
                  onSelectPlan={onSelectPlan}
                  isSelected={selectedPlan?.identifier == plan.identifier}
                  plan={plan}
                  monthlyPlanAmount={
                    plans.find((p) => p.product.subscriptionPeriod === "month")
                      ?.product.price
                  }
                />
              ))
            ) : (
              <Text>Loading...</Text>
            )}
          </View>
        </ScrollView>
      </View>
      <View style={styles.ctaContainer}>
        <Button
          style={styles.primaryButton}
          text={
            selectedPlan
              ? `Get ${
                  selectedPlan?.product.subscriptionPeriod === "P1Y"
                    ? "yearly"
                    : "monthly"
                } for $${selectedPlan?.product.price.toFixed(2)}`
              : "Get started with premium"
          }
          onPress={onConfirmPlan}
          disabled={!selectedPlan}
        />
        <TouchableOpacity
          onPress={() => {
            onSkip()
          }}
        >
          <View style={styles.secondaryButtonContainer}>
            <Text style={styles.secondaryButtonText}>Maybe later</Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  )
}

function ListItem({ text }: { text: ReactNode }) {
  return (
    <View style={styles.itemContainer}>
      {<CheckboxCircleIcon style={{ marginTop: 2 }} />}
      <Text style={styles.itemText}>{text}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: -50,
    marginTop: 5,
    display: "flex",
    justifyContent: "flex-start",
    width: "100%",
  },
  itemsList: {
    display: "flex",
    flexDirection: "column",
    rowGap: 10,
    marginBottom: hp("6%"),
  },
  itemContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "flex-start",
    columnGap: 8,
  },
  itemText: {
    width: "90%",
    lineHeight: 24,
  },
  bold: {
    fontFamily: "InterTight-SemiBold",
  },
  cardsScroller: {
    paddingTop: 15,
    paddingHorizontal: 10,
    justifyContent: "center",
  },
  cardsContainer: {
    width: "100%",
    justifyContent: "center",
    display: "flex",
    flexDirection: "row",
    columnGap: 13,
    marginBottom: 8,
  },
  ctaContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  primaryButton: {
    marginTop: 20,
    marginBottom: 18,
  },
  primaryButtonText: {
    color: "white",
    fontSize: 18,
    fontFamily: "InterTight-SemiBold",
  },
  secondaryButtonContainer: {
    borderBottomWidth: 1,
  },
  secondaryButtonText: {
    lineHeight: 16,
    fontFamily: "InterTight-SemiBold",
    fontSize: 14,
  },
})
