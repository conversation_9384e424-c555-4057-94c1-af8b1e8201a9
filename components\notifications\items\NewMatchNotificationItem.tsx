import { NewMatchNotification } from "@/apiQueries/notificationTypes"
import { Image } from "expo-image"
import { GenericNotificationItem } from "./GenericNotificationItem"
import { HandleNavigateParams } from "../NotificationFeed"
import { styles } from "../notificationStyles"
import { BoldText, NormalText } from "@/components/StyledText"

export function NewMatchNotificationItem({
  item,
  onNavigate,
}: {
  item: NewMatchNotification
  onNavigate: (params: HandleNavigateParams) => void
}) {
  const handleOpenMatches = () => {
    const streamId = item.chatChannel.streamId
    onNavigate({
      href: {
        pathname: "/(app)/matches/[channelId]",
        params: { channelId: streamId },
      },
      connectionMode: item.connectionMode,
    })
  }

  const userIsActive = !item.user.isArchived

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={
        <Image
          source={{ uri: item.user.image.url }}
          placeholder="LKO2?U%2Tw=w]~RBVZRi}RPxYJt6"
          contentFit="cover"
          contentPosition="center"
          style={styles.image}
        />
      }
      TextComponent={
        <NormalText>
          You matched with <BoldText>{item.user.firstName}</BoldText>!
        </NormalText>
      }
      primaryButton={{
        text: "Open chat",
        onPress: handleOpenMatches,
      }}
      disabled={!userIsActive}
    />
  )
}
