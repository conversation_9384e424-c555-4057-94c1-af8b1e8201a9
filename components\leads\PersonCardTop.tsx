import { StyleSheet, View } from "react-native"
import { Text } from "../Themed"
import { AnimatedCircularProgress } from "react-native-circular-progress"
import { LinearGradient } from "expo-linear-gradient"
import _ from "lodash"
import { TimedImage } from "../TrackedImage"
import Badge from "../profile/Badge"
import { useFeatureFlag } from "posthog-react-native"
import { GREY } from "@/constants/Colors"
import { Lead } from "@/types/social"

export interface PersonCardTopProps {
  lead: Lead
}

export const PersonCardTop = ({
  lead: {
    user: { firstName, isGenerated, images, age, points },
    score,
  },
}: PersonCardTopProps) => {
  const showBadge = useFeatureFlag("levels") && points >= 30

  return (
    <View style={styles.container}>
      {isGenerated ? (
        <Text style={styles.generatedLabel}>
          This user is AI-generated for demo purposes.
        </Text>
      ) : null}
      <TimedImage
        name="leads main profile pic"
        source={images[0].url}
        style={styles.image}
        contentFit="cover"
      />
      <LinearGradient
        colors={["rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0.8)"]}
        style={styles.linearGradient}
      />
      <View style={styles.textContainer}>
        {showBadge ? <Badge variant="icon" points={points} /> : null}
        <Text style={styles.name}>
          {firstName}, {age}
        </Text>
        {score ? (
          <View style={styles.scoreContainer}>
            <AnimatedCircularProgress
              size={19}
              fill={score * 100}
              width={3}
              tintColor="#A8FFC0"
              backgroundColor={GREY}
              rotation={0}
              lineCap="round"
            />
            <Text style={styles.score}>{(score * 100).toFixed(0)}% Match</Text>
          </View>
        ) : null}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 4,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: "#E8E8E8",
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
    overflow: "hidden",
    height: 500,
  },
  generatedLabel: {
    position: "absolute",
    top: 50,
    left: "auto",
    zIndex: 10,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    color: "white",
    padding: 5,
    borderRadius: 5,
  },
  image: {
    width: "100%",
    height: "100%",
    flex: 1,
  },
  textContainer: {
    position: "absolute",
    bottom: 30,
    left: 20,
    gap: 5,
    alignItems: "flex-start",
  },
  name: {
    fontSize: 32,
    fontWeight: "500",
    color: "white",
    fontFamily: "InterTight-SemiBold",
  },
  scoreContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  score: {
    marginLeft: 8,
    fontSize: 14,
    color: "#A8FFC0",
    fontFamily: "InterTight-SemiBold",
  },
  linearGradient: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "25%",
  },
})
