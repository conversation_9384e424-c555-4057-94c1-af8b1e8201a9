import { StyleSheet, View, Text } from "react-native"
import { S3_URL } from "@/constants/Links"
import { ArticleCard } from "../news/ArticleCard"
import { SmallText } from "../StyledText"
import { heightPercentageToDP } from "react-native-responsive-screen"

export const TITLE = "Depth matters. Earn more points with full articles."
export const SUBTITLE =
  "Our newsfeed articles are fact-based and handpicked to ensure that you stay informed."

export const DepthStep = () => {
  const article = {
    id: 1,
    title: "13 Small Living Room Ideas That Will Maximize Your Space",
    source: "Forbes",
    publishedAt: "",
    url: "",
    imageUrl: `${S3_URL}/static_assets/living_room_article.png`,
    frontpageSection: "",
    faviconUrl: `${S3_URL}/static_assets/forbes_favicon.png`,
    summaryPoints: null,
    isOpened: false,
    isSurveyed: false,
    gistRating: null,
    position: null,
  }

  return (
    <View>
      <ArticleCard article={article} disablePress />
      <Text style={styles.container}>
        <SmallText>
          Gist swipes are worth 2 points, full article ratings are 10 points!
        </SmallText>
        <SmallText>
          {"\n"}
          {"\n"}
        </SmallText>
        <SmallText>
          Only full article ratings count towards your InScore.
        </SmallText>
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: heightPercentageToDP("2%"),
    textAlign: "center",
  },
})
