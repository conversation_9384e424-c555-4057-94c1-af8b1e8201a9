import * as Location from "expo-location"
import { LatLng } from "react-native-maps"

export class MissingLocationPermissionError extends Error {
  constructor() {
    super("Location permission not granted")
    this.name = "MissingLocationPermissionError"
  }
}

export const getUserPosition = async (): Promise<LatLng> => {
  const { status } = await Location.requestForegroundPermissionsAsync()
  if (status !== "granted") {
    throw new MissingLocationPermissionError()
  }

  const location = await Location.getCurrentPositionAsync({
    accuracy: Location.Accuracy.High,
  })

  const { latitude, longitude } = location.coords
  return { latitude, longitude }
}
