  Application android.app  Build android.app.Activity  BuildConfig android.app.Activity  Bundle android.app.Activity  DefaultReactActivityDelegate android.app.Activity  ReactActivityDelegate android.app.Activity  ReactActivityDelegateWrapper android.app.Activity  SplashScreenManager android.app.Activity  String android.app.Activity  
fabricEnabled android.app.Activity  invokeDefaultOnBackPressed android.app.Activity  moveTaskToBack android.app.Activity  onCreate android.app.Activity  ApplicationLifecycleDispatcher android.app.Application  Boolean android.app.Application  BuildConfig android.app.Application  
Configuration android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  	ReactHost android.app.Application  ReactNativeHost android.app.Application  ReactNativeHostWrapper android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  invoke android.app.Application  load android.app.Application  onConfigurationChanged android.app.Application  onCreate android.app.Application  Context android.content  ApplicationLifecycleDispatcher android.content.Context  Boolean android.content.Context  Build android.content.Context  BuildConfig android.content.Context  Bundle android.content.Context  
Configuration android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  OpenSourceMergedSoMapping android.content.Context  PackageList android.content.Context  ReactActivityDelegate android.content.Context  ReactActivityDelegateWrapper android.content.Context  	ReactHost android.content.Context  ReactNativeHost android.content.Context  ReactNativeHostWrapper android.content.Context  ReactPackage android.content.Context  SoLoader android.content.Context  SplashScreenManager android.content.Context  String android.content.Context  
fabricEnabled android.content.Context  invoke android.content.Context  invokeDefaultOnBackPressed android.content.Context  load android.content.Context  moveTaskToBack android.content.Context  onConfigurationChanged android.content.Context  onCreate android.content.Context  ApplicationLifecycleDispatcher android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  Bundle android.content.ContextWrapper  
Configuration android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  ReactActivityDelegate android.content.ContextWrapper  ReactActivityDelegateWrapper android.content.ContextWrapper  	ReactHost android.content.ContextWrapper  ReactNativeHost android.content.ContextWrapper  ReactNativeHostWrapper android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  SoLoader android.content.ContextWrapper  SplashScreenManager android.content.ContextWrapper  String android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  invoke android.content.ContextWrapper  invokeDefaultOnBackPressed android.content.ContextWrapper  load android.content.ContextWrapper  moveTaskToBack android.content.ContextWrapper  onConfigurationChanged android.content.ContextWrapper  onCreate android.content.ContextWrapper  
Configuration android.content.res  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  Build  android.view.ContextThemeWrapper  BuildConfig  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  ReactActivityDelegate  android.view.ContextThemeWrapper  ReactActivityDelegateWrapper  android.view.ContextThemeWrapper  SplashScreenManager  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  invokeDefaultOnBackPressed  android.view.ContextThemeWrapper  moveTaskToBack  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  Build #androidx.activity.ComponentActivity  BuildConfig #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  ReactActivityDelegate #androidx.activity.ComponentActivity  ReactActivityDelegateWrapper #androidx.activity.ComponentActivity  SplashScreenManager #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  invokeDefaultOnBackPressed #androidx.activity.ComponentActivity  moveTaskToBack #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  Build (androidx.appcompat.app.AppCompatActivity  BuildConfig (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  ReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  ReactActivityDelegateWrapper (androidx.appcompat.app.AppCompatActivity  SplashScreenManager (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  invokeDefaultOnBackPressed (androidx.appcompat.app.AppCompatActivity  moveTaskToBack (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  Build #androidx.core.app.ComponentActivity  BuildConfig #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegateWrapper #androidx.core.app.ComponentActivity  SplashScreenManager #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  invokeDefaultOnBackPressed #androidx.core.app.ComponentActivity  moveTaskToBack #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  Build &androidx.fragment.app.FragmentActivity  BuildConfig &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  ReactActivityDelegate &androidx.fragment.app.FragmentActivity  ReactActivityDelegateWrapper &androidx.fragment.app.FragmentActivity  SplashScreenManager &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  invokeDefaultOnBackPressed &androidx.fragment.app.FragmentActivity  moveTaskToBack &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  getPACKAGES com.facebook.react.PackageList  getPackages com.facebook.react.PackageList  packages com.facebook.react.PackageList  setPackages com.facebook.react.PackageList  Build  com.facebook.react.ReactActivity  BuildConfig  com.facebook.react.ReactActivity  Bundle  com.facebook.react.ReactActivity  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  ReactActivityDelegate  com.facebook.react.ReactActivity  ReactActivityDelegateWrapper  com.facebook.react.ReactActivity  SplashScreenManager  com.facebook.react.ReactActivity  String  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  invokeDefaultOnBackPressed  com.facebook.react.ReactActivity  moveTaskToBack  com.facebook.react.ReactActivity  onCreate  com.facebook.react.ReactActivity  
fabricEnabled (com.facebook.react.ReactActivityDelegate  mainComponentName (com.facebook.react.ReactActivityDelegate  Boolean "com.facebook.react.ReactNativeHost  BuildConfig "com.facebook.react.ReactNativeHost  List "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  ReactPackage "com.facebook.react.ReactNativeHost  String "com.facebook.react.ReactNativeHost   DefaultNewArchitectureEntryPoint com.facebook.react.defaults  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  
fabricEnabled 8com.facebook.react.defaults.DefaultReactActivityDelegate  mainComponentName 8com.facebook.react.defaults.DefaultReactActivityDelegate  Boolean 2com.facebook.react.defaults.DefaultReactNativeHost  BuildConfig 2com.facebook.react.defaults.DefaultReactNativeHost  List 2com.facebook.react.defaults.DefaultReactNativeHost  PackageList 2com.facebook.react.defaults.DefaultReactNativeHost  ReactPackage 2com.facebook.react.defaults.DefaultReactNativeHost  String 2com.facebook.react.defaults.DefaultReactNativeHost  OpenSourceMergedSoMapping com.facebook.react.soloader  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  ApplicationLifecycleDispatcher com.scoopt.inpress  Boolean com.scoopt.inpress  Build com.scoopt.inpress  BuildConfig com.scoopt.inpress  List com.scoopt.inpress  MainActivity com.scoopt.inpress  MainApplication com.scoopt.inpress  OpenSourceMergedSoMapping com.scoopt.inpress  PackageList com.scoopt.inpress  ReactActivityDelegateWrapper com.scoopt.inpress  ReactNativeHostWrapper com.scoopt.inpress  SoLoader com.scoopt.inpress  SplashScreenManager com.scoopt.inpress  String com.scoopt.inpress  
fabricEnabled com.scoopt.inpress  load com.scoopt.inpress  DEBUG com.scoopt.inpress.BuildConfig  IS_HERMES_ENABLED com.scoopt.inpress.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED com.scoopt.inpress.BuildConfig  Build com.scoopt.inpress.MainActivity  BuildConfig com.scoopt.inpress.MainActivity  Bundle com.scoopt.inpress.MainActivity  DefaultReactActivityDelegate com.scoopt.inpress.MainActivity  ReactActivityDelegate com.scoopt.inpress.MainActivity  ReactActivityDelegateWrapper com.scoopt.inpress.MainActivity  SplashScreenManager com.scoopt.inpress.MainActivity  String com.scoopt.inpress.MainActivity  
fabricEnabled com.scoopt.inpress.MainActivity  getFABRICEnabled com.scoopt.inpress.MainActivity  getFabricEnabled com.scoopt.inpress.MainActivity  getMAINComponentName com.scoopt.inpress.MainActivity  getMainComponentName com.scoopt.inpress.MainActivity  mainComponentName com.scoopt.inpress.MainActivity  moveTaskToBack com.scoopt.inpress.MainActivity  setMainComponentName com.scoopt.inpress.MainActivity  ApplicationLifecycleDispatcher "com.scoopt.inpress.MainApplication  Boolean "com.scoopt.inpress.MainApplication  BuildConfig "com.scoopt.inpress.MainApplication  
Configuration "com.scoopt.inpress.MainApplication  DefaultReactNativeHost "com.scoopt.inpress.MainApplication  List "com.scoopt.inpress.MainApplication  OpenSourceMergedSoMapping "com.scoopt.inpress.MainApplication  PackageList "com.scoopt.inpress.MainApplication  	ReactHost "com.scoopt.inpress.MainApplication  ReactNativeHost "com.scoopt.inpress.MainApplication  ReactNativeHostWrapper "com.scoopt.inpress.MainApplication  ReactPackage "com.scoopt.inpress.MainApplication  SoLoader "com.scoopt.inpress.MainApplication  String "com.scoopt.inpress.MainApplication  applicationContext "com.scoopt.inpress.MainApplication  getAPPLICATIONContext "com.scoopt.inpress.MainApplication  getApplicationContext "com.scoopt.inpress.MainApplication  getLOAD "com.scoopt.inpress.MainApplication  getLoad "com.scoopt.inpress.MainApplication  invoke "com.scoopt.inpress.MainApplication  load "com.scoopt.inpress.MainApplication  reactNativeHost "com.scoopt.inpress.MainApplication  setApplicationContext "com.scoopt.inpress.MainApplication  ApplicationLifecycleDispatcher expo.modules  ReactActivityDelegateWrapper expo.modules  ReactNativeHostWrapper expo.modules  onApplicationCreate +expo.modules.ApplicationLifecycleDispatcher  onConfigurationChanged +expo.modules.ApplicationLifecycleDispatcher  createReactHost #expo.modules.ReactNativeHostWrapper  createReactHost -expo.modules.ReactNativeHostWrapper.Companion  invoke -expo.modules.ReactNativeHostWrapper.Companion  SplashScreenManager expo.modules.splashscreen  registerOnActivity -expo.modules.splashscreen.SplashScreenManager  ApplicationLifecycleDispatcher 	java.lang  Build 	java.lang  BuildConfig 	java.lang  OpenSourceMergedSoMapping 	java.lang  PackageList 	java.lang  ReactActivityDelegateWrapper 	java.lang  ReactNativeHostWrapper 	java.lang  SoLoader 	java.lang  SplashScreenManager 	java.lang  
fabricEnabled 	java.lang  load 	java.lang  	ArrayList 	java.util  ApplicationLifecycleDispatcher kotlin  Boolean kotlin  Build kotlin  BuildConfig kotlin  Int kotlin  Nothing kotlin  OpenSourceMergedSoMapping kotlin  PackageList kotlin  ReactActivityDelegateWrapper kotlin  ReactNativeHostWrapper kotlin  SoLoader kotlin  SplashScreenManager kotlin  String kotlin  
fabricEnabled kotlin  load kotlin  ApplicationLifecycleDispatcher kotlin.annotation  Build kotlin.annotation  BuildConfig kotlin.annotation  OpenSourceMergedSoMapping kotlin.annotation  PackageList kotlin.annotation  ReactActivityDelegateWrapper kotlin.annotation  ReactNativeHostWrapper kotlin.annotation  SoLoader kotlin.annotation  SplashScreenManager kotlin.annotation  
fabricEnabled kotlin.annotation  load kotlin.annotation  ApplicationLifecycleDispatcher kotlin.collections  Build kotlin.collections  BuildConfig kotlin.collections  List kotlin.collections  OpenSourceMergedSoMapping kotlin.collections  PackageList kotlin.collections  ReactActivityDelegateWrapper kotlin.collections  ReactNativeHostWrapper kotlin.collections  SoLoader kotlin.collections  SplashScreenManager kotlin.collections  
fabricEnabled kotlin.collections  load kotlin.collections  ApplicationLifecycleDispatcher kotlin.comparisons  Build kotlin.comparisons  BuildConfig kotlin.comparisons  OpenSourceMergedSoMapping kotlin.comparisons  PackageList kotlin.comparisons  ReactActivityDelegateWrapper kotlin.comparisons  ReactNativeHostWrapper kotlin.comparisons  SoLoader kotlin.comparisons  SplashScreenManager kotlin.comparisons  
fabricEnabled kotlin.comparisons  load kotlin.comparisons  ApplicationLifecycleDispatcher 	kotlin.io  Build 	kotlin.io  BuildConfig 	kotlin.io  OpenSourceMergedSoMapping 	kotlin.io  PackageList 	kotlin.io  ReactActivityDelegateWrapper 	kotlin.io  ReactNativeHostWrapper 	kotlin.io  SoLoader 	kotlin.io  SplashScreenManager 	kotlin.io  
fabricEnabled 	kotlin.io  load 	kotlin.io  ApplicationLifecycleDispatcher 
kotlin.jvm  Build 
kotlin.jvm  BuildConfig 
kotlin.jvm  OpenSourceMergedSoMapping 
kotlin.jvm  PackageList 
kotlin.jvm  ReactActivityDelegateWrapper 
kotlin.jvm  ReactNativeHostWrapper 
kotlin.jvm  SoLoader 
kotlin.jvm  SplashScreenManager 
kotlin.jvm  
fabricEnabled 
kotlin.jvm  load 
kotlin.jvm  ApplicationLifecycleDispatcher 
kotlin.ranges  Build 
kotlin.ranges  BuildConfig 
kotlin.ranges  OpenSourceMergedSoMapping 
kotlin.ranges  PackageList 
kotlin.ranges  ReactActivityDelegateWrapper 
kotlin.ranges  ReactNativeHostWrapper 
kotlin.ranges  SoLoader 
kotlin.ranges  SplashScreenManager 
kotlin.ranges  
fabricEnabled 
kotlin.ranges  load 
kotlin.ranges  ApplicationLifecycleDispatcher kotlin.sequences  Build kotlin.sequences  BuildConfig kotlin.sequences  OpenSourceMergedSoMapping kotlin.sequences  PackageList kotlin.sequences  ReactActivityDelegateWrapper kotlin.sequences  ReactNativeHostWrapper kotlin.sequences  SoLoader kotlin.sequences  SplashScreenManager kotlin.sequences  
fabricEnabled kotlin.sequences  load kotlin.sequences  ApplicationLifecycleDispatcher kotlin.text  Build kotlin.text  BuildConfig kotlin.text  OpenSourceMergedSoMapping kotlin.text  PackageList kotlin.text  ReactActivityDelegateWrapper kotlin.text  ReactNativeHostWrapper kotlin.text  SoLoader kotlin.text  SplashScreenManager kotlin.text  
fabricEnabled kotlin.text  load kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      