import { get } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { Platform } from "react-native"
import { getAppVersion } from "@/utils/tracking"

export type VersionStatus =
  | "up_to_date"
  | "update_available"
  | "update_required"

export const checkVersionStatus = async (): Promise<VersionStatus> => {
  type Params = {
    platform: Platform["OS"]
    version: number
  }

  type Response = {
    status: VersionStatus
  }

  const platform = Platform.OS
  const version = getAppVersion()

  const { status } = await get<Params, Response>(
    `${INPRESS_API_URL}/check-version-status`,
    undefined,
    { platform, version },
  )
  return status
}
