import { router, Stack } from "expo-router"
import Colors from "@/constants/Colors"
import { Ionicons } from "@expo/vector-icons"
import { TouchableOpacity } from "react-native"
import { useSession } from "@/ctx"
import Badge from "@/components/profile/Badge"
import { NativeStackNavigationOptions } from "@react-navigation/native-stack"

export const indexScreenOptions = {
  title: "Profile",
  headerTitle: "",
}

export const profileScreenOptions: NativeStackNavigationOptions = {
  headerTitle: "Edit profile",
  headerTitleAlign: "center",
  headerBackVisible: false,
  headerShadowVisible: false,
}

export default function AccountRoute() {
  const { session } = useSession()

  const friendsScreenTitle = session!.user.isNewsOnly
    ? "Connect with Friends"
    : "Match with Friends"

  return (
    <Stack>
      <Stack.Screen name="index" options={indexScreenOptions} />
      <Stack.Screen
        name="journey"
        options={{
          headerTitle: "InScore Progress",
          headerTitleAlign: "center",
          headerShown: false,
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={{ marginLeft: 10 }}
            >
              <Ionicons name="arrow-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () =>
            session && <Badge variant="points" points={session.user.points} />,
        }}
      />
      <Stack.Screen name="profile" options={profileScreenOptions} />
      <Stack.Screen
        name="match-preferences"
        options={{
          headerTitle: "Match Preferences",
        }}
      />
      <Stack.Screen
        name="settings"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="share-with-friends"
        options={{
          headerTitle: "Share With Friends",
        }}
      />
      <Stack.Screen
        name="phone-number"
        options={{
          headerTitle: friendsScreenTitle,
        }}
      />
      <Stack.Screen
        name="verify-phone"
        options={{
          headerTitle: "Verify phone number",
        }}
      />
      <Stack.Screen
        name="match-with-friends"
        options={{
          headerTitle: friendsScreenTitle,
        }}
      />
      <Stack.Screen
        name="qr-code-screen"
        options={{
          headerTitle: "",
          headerBackVisible: true,
          headerShadowVisible: false,
          headerStyle: {
            backgroundColor: Colors.light.background,
          },
        }}
      />
    </Stack>
  )
}
