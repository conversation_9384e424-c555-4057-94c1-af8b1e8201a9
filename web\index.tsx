import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { AppRegistry } from "react-native"

function WebApp() {
  const isStoryMode = process.env.EXPO_PUBLIC_RENDER_STORIES === "true"

  if (isStoryMode) {
    const StoriesApp = require("./stories").default
    return <StoriesApp />
  }

  return (
    <View style={styles.container}>
      <Text style={styles.text}>InPress Web</Text>
      <Text style={styles.subtitle}>Coming soon...</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
  },
  text: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
})

AppRegistry.registerComponent("main", () => WebApp)
AppRegistry.runApplication("main", {
  rootTag: document.getElementById("root"),
})

export default WebApp
