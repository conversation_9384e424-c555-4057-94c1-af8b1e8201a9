import { StyleSheet, View } from "react-native"
import { Image } from "expo-image"
import { LinearGradient } from "expo-linear-gradient"
import { Text } from "../Themed"
import ArticleSource from "./ArticleSource"
import { Card } from "react-native-paper"
import { useState } from "react"
import ReportArticleModal from "./ReportArticleModal"
import { ReadRatedCover } from "./ReadRatedCover"
import { openArticle } from "../ArticlePage"
import { Article } from "@/types/news"

interface FullImageArticleCardProps {
  article: Article
}

export const FullImageArticleCard = ({
  article,
}: FullImageArticleCardProps) => {
  const [isReporting, setIsReporting] = useState<boolean>(false)

  const handlePress = async () => {
    openArticle(article)
  }

  return (
    <Card
      onPress={() => handlePress()}
      onLongPress={() => setIsReporting(true)}
    >
      <View>
        <ReadRatedCover article={article} rightOrientRated />

        <Image
          source={{ uri: article.imageUrl }}
          style={styles.headlineImage}
        />

        <LinearGradient
          colors={[
            "rgba(0, 0, 0, 0)",
            "rgba(45, 45, 45, 0.45)",
            "rgba(0, 0, 0, 1)",
          ]}
          locations={[0.01, 0.45, 1]}
          style={styles.linearGradient}
        />

        <Card.Content style={styles.contentStyle}>
          <Text style={styles.headlineText} numberOfLines={3}>
            {article.title}
          </Text>
          <ArticleSource article={article} textStyle={{ color: "white" }} />
        </Card.Content>
        <ReportArticleModal
          articleId={article.id}
          modalVisible={isReporting}
          onClose={() => setIsReporting(false)}
        />
      </View>
    </Card>
  )
}

export const HEIGHT = 310

const styles = StyleSheet.create({
  headlineImage: {
    borderRadius: 8,
    height: HEIGHT,
    width: "100%",
  },
  linearGradient: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: "65%",
    borderRadius: 8,
  },
  contentStyle: {
    paddingHorizontal: 12,
    position: "absolute",
    bottom: 0,
    left: 0,
    gap: 3,
  },
  headlineText: {
    marginBottom: 5,
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
    color: "white",
    alignItems: "center",
  },
})
