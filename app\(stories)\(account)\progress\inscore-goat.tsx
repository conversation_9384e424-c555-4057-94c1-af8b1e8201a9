import { Screen } from "@/components/Themed"
import InScoreSection from "@/components/leads/InScoreSection"
import { LEVELS } from "../../components/badge"

export default function AccountStory() {
  return (
    <Screen>
      <InScoreSection
        points={10240}
        currentLevel={LEVELS.slice(-1)[0]}
        nextLevel={undefined}
        articlesRead={435}
        articlesRated={300}
        streakDays={7}
      />
    </Screen>
  )
}
