import { useSession } from "@/ctx"
import _ from "lodash"
import {
  Alert,
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Channel } from "stream-chat"
import {
  Channel as ChannelView,
  MessageList,
  MessageInput,
  Message,
} from "stream-chat-expo"
import { Screen, Text } from "./Themed"
import { getMatches, Match, reportMatch } from "@/apiQueries/apiQueries"
import { router, useNavigation, useFocusEffect } from "expo-router"
import { ConfirmationModal } from "./ConfirmationModal"
import { useCallback, useEffect, useState } from "react"
import ChatHeader from "./ChatHeader"
import PersonCard from "./leads/PersonCard"
import { useActiveConnectionMode } from "@/context/ModeContext"
import Toggle from "./Toggle"
import { styles as viewProfileStyles } from "@/screens/account/ViewProfileScreen"
import { unmatch } from "@/apiQueries/matches"
import { EventType, trackEvent } from "@/utils/tracking"
import { useChatContext } from "@/chatContext"
import { Loader } from "./widgets/Loader"
import useDraftMessage from "@/app/hooks/useDraftMessage"
import { ProfileMissing } from "./widgets/ProfileMissing"
import { isUserWithProfile, User } from "@/types/user"

export const ChatScreen = ({ channel }: { channel: Channel }) => {
  const [match, setMatch] = useState<Match | undefined>(undefined)

  const insets = useSafeAreaInsets()
  const { session } = useSession()
  const { activeConnectionMode } = useActiveConnectionMode()
  const navigation = useNavigation("/(app)")
  const { draftMessage, handleInputChange } = useDraftMessage(channel)

  useFocusEffect(
    useCallback(() => {
      navigation.setOptions({ headerRight: false })
      return () => {
        navigation.setOptions({
          headerRight: () => <Toggle />,
        })
        if (Platform.OS === "android") handleInputChange("")
      }
    }, [navigation]),
  )

  const { resetChannelUnreadCount } = useChatContext()
  const otherUserId = _.values(channel.state.members).find(
    (member) => member.user && parseInt(member.user.id) !== session!.user.id,
  )?.user?.id

  const iosVerticalOffset = Platform.OS === "ios" ? 200 + insets.bottom : 0

  useFocusEffect(
    useCallback(() => {
      resetChannelUnreadCount(channel)
    }, [channel, resetChannelUnreadCount]),
  )

  useEffect(() => {
    getMatches({
      token: session!.token,
      connectionMode: activeConnectionMode,
    }).then((matches) => {
      const match = matches.find(
        (match) => String(match.user.id) === otherUserId,
      )
      setMatch(match)
    })
  }, [channel])

  const handleUnmatch = async () => {
    await unmatch({
      token: session!.token,
      channelId: channel.id!,
      unmatchedUserId: otherUserId!,
      connectionMode: activeConnectionMode,
    })
    await trackEvent(EventType.Unmatched, { data: { channelId: channel.id } })
    Alert.alert("User has been unmatched")
    router.back()
  }

  const handleReportAndBlock = async () => {
    await reportMatch({
      token: session!.token,
      channelId: channel.id!,
      reportedUserId: parseInt(otherUserId!),
    })
    Alert.alert("User has been reported and unmatched")
    router.back()
  }

  if (!match) {
    return <Loader connectionMode={activeConnectionMode} />
  }

  if (!isUserWithProfile(session!.user)) {
    return <ProfileMissing />
  }

  return (
    <ChatScreen_
      thisUser={session!.user}
      match={match}
      channelComponent={
        <ChannelView
          MessageAvatar={() => (
            <Image
              source={{
                uri: match?.user.images[0].url,
              }}
              style={styles.messageAvatar}
            />
          )}
          channel={channel}
          keyboardVerticalOffset={iosVerticalOffset}
          hasFilePicker={false}
          hasImagePicker={false}
          allowThreadMessagesInChannel={false}
          initialValue={draftMessage}
          onChangeText={handleInputChange}
        >
          <MessageList
            Message={(p) => (
              <Message
                messageActions={({ flagMessage, quotedReply, pinMessage }) => [
                  quotedReply,
                  flagMessage,
                  pinMessage,
                ]}
                {...p}
              />
            )}
          />
          <MessageInput />
        </ChannelView>
      }
      onUnmatch={handleUnmatch}
      onReportAndBlock={handleReportAndBlock}
    />
  )
}

interface ChatScreenProps_ {
  thisUser: User
  match: Match
  channelComponent: React.ReactNode
  onUnmatch: () => void
  onReportAndBlock: () => void
}

export const ChatScreen_ = ({
  thisUser,
  match,
  channelComponent,
  onUnmatch,
  onReportAndBlock,
}: ChatScreenProps_) => {
  const [selectedTab, setSelectedTab] = useState<"chat" | "profile">("chat")
  const [isConfirmationModalVisible, setIsConfirmationModalVisible] =
    useState(false)

  const handleUnmatch = async () => {
    onUnmatch()
    setIsConfirmationModalVisible(false)
  }

  const handleReport = async () => {
    onReportAndBlock()
    setIsConfirmationModalVisible(false)
  }

  const renderChatTab = () => (
    <View key="chat" style={styles.chatContainer}>
      <View>
        <TouchableOpacity
          style={styles.reportButton}
          onPress={() => setIsConfirmationModalVisible(true)}
        >
          <Text style={styles.reportButtonText}>Unmatch with user</Text>
        </TouchableOpacity>
      </View>
      <View style={{ flex: 1 }}>{channelComponent}</View>
    </View>
  )

  const renderProfileTab = (match: Match) => (
    <View key="profile" style={viewProfileStyles.profile}>
      <PersonCard
        lead={{
          id: 0,
          user: match.user,
          topics: match.topics,
          score: match.score,
        }}
        recipientUser={thisUser}
        showHideAndReport={false}
      />
    </View>
  )

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <View key="header" style={styles.container}>
        <ChatHeader
          userFirstName={match.user.firstName}
          userPhoto={match.user.images[0]}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
        />
      </View>
      {selectedTab === "chat" ? renderChatTab() : renderProfileTab(match)}
      <ConfirmationModal
        title={`Unmatch with ${match.user.firstName}?`}
        description="This cannot be undone."
        visible={isConfirmationModalVisible}
        confirmButtonText="Unmatch"
        onCancel={() => setIsConfirmationModalVisible(false)}
        onConfirm={handleUnmatch}
      >
        <Text style={styles.reportText} onPress={handleReport}>
          If you believe the user is engaging in inappropriate behavior, you can{" "}
          <Text style={styles.reportLink}>tap here</Text> to report them and
          unmatch.
        </Text>
      </ConfirmationModal>
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    rowGap: 14,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: "#fcfcfc",
    zIndex: -10,
  },
  reportText: {
    marginTop: 10,
    marginBottom: 40,
  },
  reportLink: {
    textDecorationLine: "underline",
  },
  reportButton: {
    alignSelf: "center",
    borderRadius: 10,
    marginTop: 15,
    marginHorizontal: 40,
    paddingVertical: 6,
    paddingHorizontal: 10,
    backgroundColor: "#ffe8f2",
  },
  reportButtonText: {
    textAlign: "center",
    color: "#525252",
    fontSize: 12.5,
    fontFamily: "InterTight-Regular",
  },
  messageAvatar: {
    height: 32,
    width: 32,
    borderRadius: 80,
    marginRight: 8,
  },
})
