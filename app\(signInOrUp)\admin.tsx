import { Button } from "@/components/Button"
import { TextInput } from "@/components/TextInput"
import { Screen } from "@/components/Themed"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { useSession } from "@/ctx"
import { isImpersonating } from "@/utils/general"
import { router } from "expo-router"
import { useState } from "react"
import { StyleSheet } from "react-native"

export default function AdminRoute() {
  const [email, setEmail] = useState<string>("")
  const { impersonate } = useSession()

  const handleImpersonate = async () => {
    if (!isImpersonating) {
      alert("Impersonation is not enabled")
      return
    }

    await impersonate({ email })
    router.push("/news/feed")
  }

  return (
    <Screen style={styles.container}>
      <ScreenHeader title="Admin" />
      <TextInput label="Email of user to impersonate" onChangeText={setEmail} />
      <Button text="Impersonate" onPress={handleImpersonate} />
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 100,
  },
})
