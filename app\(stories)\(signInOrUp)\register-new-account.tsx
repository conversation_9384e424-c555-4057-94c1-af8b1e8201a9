import { uploadNewProfileImages } from "@/apiQueries/auth"
import { register } from "@/apiQueries/registerUser"
import { Button } from "@/components/Button"
import MultiImagePicker, {
  LocalOrRemoteImage,
} from "@/components/MultiImagePicker"
import { Screen } from "@/components/Themed"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { defaultNewUser, FinishedNewUser } from "@/components/signInOrUp/SignUp"
import { birthdateFormat } from "@/types/user"
import { washingtonDC } from "@/utils/allowableAreas"
import moment from "moment"
import { useState } from "react"

export const finishedNewUser: FinishedNewUser = {
  ...defaultNewUser,
  accessCode: "TEST",
  accessCodeType: "custom",
  eulaAgreementDate: new Date(),
  firstName: `Test-${Math.random()}`,
  lastName: `User-${Math.random()}`,
  phoneNumber: `+12023334444`,
  email: `test-${Math.random()}@test.com`,
  password: "password",
  passwordConfirmation: "password",
  latitude: washingtonDC.latitude,
  longitude: washingtonDC.longitude,
  birthdate: moment("2000-01-01").format(birthdateFormat),
  gender: "male",
  genderPrefs: ["female"],
  connectionMode: ConnectionMode.Friends,
  isNewsOnly: false,
  datesModeIsActivated: false,
  friendsModeIsActivated: true,
  minAgePref: 18,
  maxAgePref: 30,
  minSimilarityPref: 0,
  maxSimilarityPref: 50,
  images: [],
  biography: "Test bio - I am a test user.",
  scoopResponses: [
    {
      position: 0,
      promptId: 3,
      prompt: "Test prompt",
      text: "Test response",
    },
  ],
  occupation: "Test occupation",
  pushToken: "test-push-token",
}

export default function Story() {
  const [images, setImages] = useState<LocalOrRemoteImage[]>([])

  const handleRegister = async () => {
    const imageIds = await uploadNewProfileImages({
      images,
      connectionMode: finishedNewUser.connectionMode,
    })

    await register({
      ...finishedNewUser,
      email: `test-${Math.random()}@test.com`,
      imageIds,
    })
  }

  return (
    <Screen>
      <MultiImagePicker initialImages={[]} onImagesChange={setImages} />

      <Button text="Register new account" onPress={handleRegister} />
    </Screen>
  )
}
