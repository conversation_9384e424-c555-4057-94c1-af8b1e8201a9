import { StyleSheet, View } from "react-native"
import { Text, TextProps, ViewProps } from "./Themed"
import { screenSubtitleStyle } from "./widgets/styles"
import { fontStyles } from "@/styles"

export function MonoText(props: TextProps) {
  return <Text {...props} style={[props.style, { fontFamily: "SpaceMono" }]} />
}

export const HeaderText = (props: TextProps) => {
  return <Text {...props} style={[styles.headerText, props.style]} />
}

export const SubheaderText = (props: TextProps) => {
  return <Text {...props} style={[styles.subheaderText, props.style]} />
}

export const ItalicHeaderText = (props: TextProps) => {
  return (
    <HeaderText {...props} style={[styles.italicHeaderText, props.style]} />
  )
}

export const LargeText = (props: TextProps) => {
  return (
    <Text {...props} style={[styles.text, styles.largeText, props.style]} />
  )
}

export const NormalText = (props: TextProps) => {
  return (
    <Text {...props} style={[styles.text, styles.normalText, props.style]} />
  )
}

export const SmallText = (props: TextProps) => {
  return (
    <Text {...props} style={[styles.text, styles.smallText, props.style]} />
  )
}

export const BoldText = (props: TextProps) => {
  return <Text {...props} style={[styles.text, styles.boldText, props.style]} />
}

export const Paragraph = (props: ViewProps) => {
  return <View {...props} />
}

export const Paragraphs = (props: ViewProps) => {
  return <View {...props} style={[styles.paragraphs, props.style]} />
}

export const styles = StyleSheet.create({
  headerText: {
    ...fontStyles.editorial,
    fontSize: 50,
    paddingBottom: 12,
    lineHeight: 54,
  },
  subheaderText: screenSubtitleStyle,
  italicHeaderText: {
    ...fontStyles.editorialItalic,
  },
  text: {
    color: "black",
    fontFamily: "InterTight-Regular",
  },
  largeText: {
    fontSize: 16,
  },
  normalText: {
    fontSize: 14,
  },
  smallText: {
    fontSize: 12,
  },
  boldText: {
    fontFamily: "InterTight-SemiBold",
  },
  paragraphs: {
    gap: 18,
  },
})
