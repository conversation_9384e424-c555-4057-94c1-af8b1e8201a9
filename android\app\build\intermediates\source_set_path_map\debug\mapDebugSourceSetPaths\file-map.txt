com.scoopt.inpress.app-ui-release-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01193c995888536bd997d04e0cdddb15\transformed\ui-release\res
com.scoopt.inpress.app-coordinatorlayout-1.2.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01334412ef15649e74ec9893d78559a3\transformed\coordinatorlayout-1.2.0\res
com.scoopt.inpress.app-apng-3.0.2-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ed15c38866280370a59d9d62d3cb16\transformed\apng-3.0.2\res
com.scoopt.inpress.app-lifecycle-service-2.8.3-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04bd76be230be3afda015e7c0cd50c4f\transformed\lifecycle-service-2.8.3\res
com.scoopt.inpress.app-fragment-ktx-1.6.1-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05a74691b8cd1b38d4395dfe50a0b4ee\transformed\fragment-ktx-1.6.1\res
com.scoopt.inpress.app-core-runtime-2.2.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071cc2099c9bf7e7d24e7fdbb394e138\transformed\core-runtime-2.2.0\res
com.scoopt.inpress.app-play-services-maps-18.2.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\093a46847528a32d43d589e6cfd36971\transformed\play-services-maps-18.2.0\res
com.scoopt.inpress.app-animation-core-release-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c145a2625628dc1692a07a5e172b5bb\transformed\animation-core-release\res
com.scoopt.inpress.app-lifecycle-runtime-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f97a256d1c5f7c498ac1fcc5354ff1c\transformed\lifecycle-runtime-release\res
com.scoopt.inpress.app-work-runtime-2.7.1-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11d3e9e2a00c92fb25e1f60e53ed4b10\transformed\work-runtime-2.7.1\res
com.scoopt.inpress.app-glide-4.16.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13163a34b81a53dc146e7d6ecff161ed\transformed\glide-4.16.0\res
com.scoopt.inpress.app-lifecycle-livedata-core-2.8.3-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\135d45547a57ba4ef6d73102fe49363e\transformed\lifecycle-livedata-core-2.8.3\res
com.scoopt.inpress.app-recyclerview-1.2.1-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\15214162fcd85290223d3ecfb84d2ee5\transformed\recyclerview-1.2.1\res
com.scoopt.inpress.app-constraintlayout-2.0.1-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\166bb1aae962427afb8e3b148b07eabf\transformed\constraintlayout-2.0.1\res
com.scoopt.inpress.app-firebase-messaging-24.0.1-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad03ab831288055a29b29ba1dfd34c5\transformed\firebase-messaging-24.0.1\res
com.scoopt.inpress.app-viewpager2-1.0.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b523cce3192ee7592e4492a6cabbc8a\transformed\viewpager2-1.0.0\res
com.scoopt.inpress.app-graphics-path-1.0.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f59f02b7bbd1aabcc70c251eded0cec\transformed\graphics-path-1.0.1\res
com.scoopt.inpress.app-purchases-7.12.0-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2257eafd8498aca343fc8b839cbd8b50\transformed\purchases-7.12.0\res
com.scoopt.inpress.app-autofill-1.1.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27d8e68d5a57b657f05297cae5e4b495\transformed\autofill-1.1.0\res
com.scoopt.inpress.app-media-1.4.3-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b99ebfa5cff4c3c38777aaa7b2191b\transformed\media-1.4.3\res
com.scoopt.inpress.app-lifecycle-livedata-core-ktx-2.8.3-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3722047ea58d3e32ab834186a09a4add\transformed\lifecycle-livedata-core-ktx-2.8.3\res
com.scoopt.inpress.app-camera-lifecycle-1.4.1-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39bede94ba66b0b16feaf12e7d1cc13d\transformed\camera-lifecycle-1.4.1\res
com.scoopt.inpress.app-lifecycle-process-2.8.3-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e5c21c43df26a74fed2fe0ef3aba84d\transformed\lifecycle-process-2.8.3\res
com.scoopt.inpress.app-swiperefreshlayout-1.1.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\431c8829f6f6b27b6455a795856a3344\transformed\swiperefreshlayout-1.1.0\res
com.scoopt.inpress.app-core-ktx-1.13.1-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\435430fbaf443eda8529ebdd0e3c7bf1\transformed\core-ktx-1.13.1\res
com.scoopt.inpress.app-facebook-messenger-18.0.3-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483b0b4d2555cb7cbf1ba22a708479a3\transformed\facebook-messenger-18.0.3\res
com.scoopt.inpress.app-af-android-sdk-6.16.2-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\res
com.scoopt.inpress.app-gif-3.0.2-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d188075c79fdd4e5500862b567aa471\transformed\gif-3.0.2\res
com.scoopt.inpress.app-lifecycle-livedata-2.8.3-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4de29c54e3244094408eb217e4741f9c\transformed\lifecycle-livedata-2.8.3\res
com.scoopt.inpress.app-customview-poolingcontainer-1.0.0-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcfbb00bfb44d8c67b52adbc737fe3f\transformed\customview-poolingcontainer-1.0.0\res
com.scoopt.inpress.app-browser-1.6.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5146738535d3021a7116f39c42fe567a\transformed\browser-1.6.0\res
com.scoopt.inpress.app-sentry-android-replay-7.22.5-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52709b16bfe4b5af8f5a5eabe0c766ee\transformed\sentry-android-replay-7.22.5\res
com.scoopt.inpress.app-startup-runtime-1.1.1-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53426c7420b2e5997934580c727bbe3a\transformed\startup-runtime-1.1.1\res
com.scoopt.inpress.app-lottie-6.5.2-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54b3347a055411cfaa73a8d0e5b66261\transformed\lottie-6.5.2\res
com.scoopt.inpress.app-exoplayer-ui-2.18.1-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56871df614f2f5c9a79b4b54c9edca50\transformed\exoplayer-ui-2.18.1\res
com.scoopt.inpress.app-androidsvg-aar-1.4-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b7b7964f47632f9df96ec8f517abe7d\transformed\androidsvg-aar-1.4\res
com.scoopt.inpress.app-sentry-android-ndk-7.22.5-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\609e1134ca6dae892552d18d7174d246\transformed\sentry-android-ndk-7.22.5\res
com.scoopt.inpress.app-emoji2-views-helper-1.3.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f18b16637dbdcc969dd33e5f8a43d\transformed\emoji2-views-helper-1.3.0\res
com.scoopt.inpress.app-firebase-common-21.0.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629ed1f1e208e7ffd1213132f262c635\transformed\firebase-common-21.0.0\res
com.scoopt.inpress.app-glide-plugin-3.0.2-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\655f8e796df12793c11e60201ac13df0\transformed\glide-plugin-3.0.2\res
com.scoopt.inpress.app-camera-core-1.4.1-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fd6ee1c275f6eede3e390b5342beb0\transformed\camera-core-1.4.1\res
com.scoopt.inpress.app-cardview-1.0.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\692110d5710e4745679c61385a3c3853\transformed\cardview-1.0.0\res
com.scoopt.inpress.app-ui-graphics-release-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6baf68c68fe37274bc7075655edbd953\transformed\ui-graphics-release\res
com.scoopt.inpress.app-runtime-saveable-release-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d0bcc65bed66d2181e2a2293229def3\transformed\runtime-saveable-release\res
com.scoopt.inpress.app-core-1.13.1-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d30d7eadb84577792f5ede26cc0121f\transformed\core-1.13.1\res
com.scoopt.inpress.app-appcompat-1.7.0-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fd63cfeaf959e262224845b26be4452\transformed\appcompat-1.7.0\res
com.scoopt.inpress.app-android-maps-utils-3.8.2-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ba90cbd0d6ec95bf69d0067dc982b9\transformed\android-maps-utils-3.8.2\res
com.scoopt.inpress.app-runtime-release-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739cc41763d5deb2d5267b2bd7911623\transformed\runtime-release\res
com.scoopt.inpress.app-emoji2-1.3.0-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74d41328e0e4ab36881ee4b325083a8c\transformed\emoji2-1.3.0\res
com.scoopt.inpress.app-play-services-base-18.3.0-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\777305bde8bb0eae5df801fbeec7f1af\transformed\play-services-base-18.3.0\res
com.scoopt.inpress.app-lifecycle-viewmodel-release-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77d3f0a98accf3c3f3da3a8731edebae\transformed\lifecycle-viewmodel-release\res
com.scoopt.inpress.app-BlurView-version-2.0.6-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8aa82df4ddf8339506f4afc921b6b4\transformed\BlurView-version-2.0.6\res
com.scoopt.inpress.app-foundation-layout-release-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80ce655b717f8d0500f49bc0a89c3f79\transformed\foundation-layout-release\res
com.scoopt.inpress.app-react-android-0.76.9-debug-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\836b6092fab1e5d2791d225376c878c3\transformed\react-android-0.76.9-debug\res
com.scoopt.inpress.app-drawee-3.2.0-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85586c22835c13b9d3a8a27f8f46d86d\transformed\drawee-3.2.0\res
com.scoopt.inpress.app-fragment-1.6.1-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\868bac4eefab012de29ba5d141b7a6e4\transformed\fragment-1.6.1\res
com.scoopt.inpress.app-tracing-1.2.0-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86fe3428fb49648b88cd1b5b200fc124\transformed\tracing-1.2.0\res
com.scoopt.inpress.app-drawerlayout-1.1.1-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b1f927aff154629f3c8ace576ee7d80\transformed\drawerlayout-1.1.1\res
com.scoopt.inpress.app-savedstate-ktx-1.2.1-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\996e6ac5dce730a58e974de0c4fd1f63\transformed\savedstate-ktx-1.2.1\res
com.scoopt.inpress.app-foundation-release-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d3a26c867c873f2bbcf3c9523bc106a\transformed\foundation-release\res
com.scoopt.inpress.app-profileinstaller-1.3.1-60 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e720ff03b24d7366f806b42e5e9bec0\transformed\profileinstaller-1.3.1\res
com.scoopt.inpress.app-tracing-ktx-1.2.0-61 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a130c94486d708f4697639d7d8d14415\transformed\tracing-ktx-1.2.0\res
com.scoopt.inpress.app-ui-unit-release-62 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aea0148c2d5ec84520fed95ead0cd7d7\transformed\ui-unit-release\res
com.scoopt.inpress.app-animation-release-63 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b07156b6523fe77a52cb3b4361b39785\transformed\animation-release\res
com.scoopt.inpress.app-play-services-basement-18.3.0-64 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b21ce383b752b9531325f3b44e3feedd\transformed\play-services-basement-18.3.0\res
com.scoopt.inpress.app-lifecycle-viewmodel-savedstate-2.8.3-65 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3079fc4a3a067f116fdc198af709606\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.scoopt.inpress.app-facebook-common-18.0.3-66 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d0db5a75430bad87fc47765dc0a917\transformed\facebook-common-18.0.3\res
com.scoopt.inpress.app-savedstate-1.2.1-67 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47a9c898aa0eb32234d150f2eadf2db\transformed\savedstate-1.2.1\res
com.scoopt.inpress.app-activity-ktx-1.7.2-68 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b52d94765285fdc976358000c6e69535\transformed\activity-ktx-1.7.2\res
com.scoopt.inpress.app-activity-1.7.2-69 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7f386dc6e1c6297614f0be80f0a4395\transformed\activity-1.7.2\res
com.scoopt.inpress.app-frameanimation-3.0.2-70 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c37ceaed472349d4a4a0cc32b5c62106\transformed\frameanimation-3.0.2\res
com.scoopt.inpress.app-avif-3.0.2-71 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3cedc0728cba80254f49e59e90e99a6\transformed\avif-3.0.2\res
com.scoopt.inpress.app-camera-video-1.4.1-72 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb33eb2bd08a89dcf71f021fda63e323\transformed\camera-video-1.4.1\res
com.scoopt.inpress.app-ui-geometry-release-73 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cee8421a8599efcc225514e5dd23a061\transformed\ui-geometry-release\res
com.scoopt.inpress.app-camera-extensions-1.4.1-74 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf3481dc5c434a247552669ef573d0c6\transformed\camera-extensions-1.4.1\res
com.scoopt.inpress.app-facebook-login-18.0.3-75 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d799b77c255eed75f24a9bf55ca48f8f\transformed\facebook-login-18.0.3\res
com.scoopt.inpress.app-camera-view-1.4.1-76 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d874d656d3edc0a36a0b7a6124463c43\transformed\camera-view-1.4.1\res
com.scoopt.inpress.app-Android-Image-Cropper-4.3.1-77 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d949fc145caa1c8c8e70db6f350aa6e5\transformed\Android-Image-Cropper-4.3.1\res
com.scoopt.inpress.app-sentry-android-7.22.5-78 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de35ef56d110f5bb03d40da766436b7f\transformed\sentry-android-7.22.5\res
com.scoopt.inpress.app-exoplayer-core-2.18.1-79 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de8360839840b029eae401d3de045f15\transformed\exoplayer-core-2.18.1\res
com.scoopt.inpress.app-facebook-share-18.0.3-80 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3fd05bc4f375e9bd686e94f570f0140\transformed\facebook-share-18.0.3\res
com.scoopt.inpress.app-appcompat-resources-1.7.0-81 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4eb4058a19910a643797faa87a550fc\transformed\appcompat-resources-1.7.0\res
com.scoopt.inpress.app-sentry-android-core-7.22.5-82 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e58f6f6c62d9c2621c5268aa4b6b6180\transformed\sentry-android-core-7.22.5\res
com.scoopt.inpress.app-lifecycle-viewmodel-ktx-2.8.3-83 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5c522a3f043374cf9fb8c527af84193\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.scoopt.inpress.app-camera-mlkit-vision-1.4.1-84 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5ee820f4e7da6e30cce364b067c95a5\transformed\camera-mlkit-vision-1.4.1\res
com.scoopt.inpress.app-core-splashscreen-1.2.0-alpha02-85 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5f2661155b37b34c475d3a6bbc83197\transformed\core-splashscreen-1.2.0-alpha02\res
com.scoopt.inpress.app-billing-6.2.1-86 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e894d039b2a2646a49229031f9d809f6\transformed\billing-6.2.1\res
com.scoopt.inpress.app-camera-camera2-1.4.1-87 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eac3ac83e151d9c51684f4a36feb133a\transformed\camera-camera2-1.4.1\res
com.scoopt.inpress.app-material-1.6.1-88 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb7edd6c4a6a4db7c12a49d0d8e72c63\transformed\material-1.6.1\res
com.scoopt.inpress.app-annotation-experimental-1.4.1-89 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee8dc5aa0b1ccacdd316db7adb3cde2e\transformed\annotation-experimental-1.4.1\res
com.scoopt.inpress.app-ui-util-release-90 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8a2e7a47abfa6c5ae9d97111f9a4da\transformed\ui-util-release\res
com.scoopt.inpress.app-lifecycle-runtime-ktx-release-91 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0765ed377bf3d44ecfda4906552fc54\transformed\lifecycle-runtime-ktx-release\res
com.scoopt.inpress.app-ui-text-release-92 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1717c9d4f49c202931895069c5eacdb\transformed\ui-text-release\res
com.scoopt.inpress.app-lifecycle-runtime-compose-release-93 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f290a05a084430f6a0c046fad5a13d64\transformed\lifecycle-runtime-compose-release\res
com.scoopt.inpress.app-awebp-3.0.2-94 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2befb358b8fb82507640b6dbe4c56fa\transformed\awebp-3.0.2\res
com.scoopt.inpress.app-facebook-core-18.0.3-95 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc46e0d781f75c8357abab356e885beb\transformed\facebook-core-18.0.3\res
com.scoopt.inpress.app-transition-1.2.0-96 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc4b7785b7f60e7f8449e110fc2cc723\transformed\transition-1.2.0\res
com.scoopt.inpress.app-pngs-97 C:\Users\<USER>\code\projects\inpress-react-native\android\app\build\generated\res\pngs\debug
com.scoopt.inpress.app-res-98 C:\Users\<USER>\code\projects\inpress-react-native\android\app\build\generated\res\processDebugGoogleServices
com.scoopt.inpress.app-resValues-99 C:\Users\<USER>\code\projects\inpress-react-native\android\app\build\generated\res\resValues\debug
com.scoopt.inpress.app-packageDebugResources-100 C:\Users\<USER>\code\projects\inpress-react-native\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.scoopt.inpress.app-packageDebugResources-101 C:\Users\<USER>\code\projects\inpress-react-native\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.scoopt.inpress.app-debug-102 C:\Users\<USER>\code\projects\inpress-react-native\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.scoopt.inpress.app-debug-103 C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\debug\res
com.scoopt.inpress.app-main-104 C:\Users\<USER>\code\projects\inpress-react-native\android\app\src\main\res
com.scoopt.inpress.app-debug-105 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-106 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-107 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-108 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-109 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-110 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\@stream-io\flat-list-mvcp\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-111 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-application\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-112 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-asset\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-113 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-av\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-114 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-blur\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-115 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-camera\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-116 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-117 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-118 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-crypto\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-119 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-120 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-121 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-122 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-123 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-device\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-124 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-125 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-font\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-126 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-loader\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-127 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-manipulator\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-128 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-129 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-image\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-130 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-131 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-keep-awake\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-132 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linear-gradient\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-133 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-linking\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-134 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-localization\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-135 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-location\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-136 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-137 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-138 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-139 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-140 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-141 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-sms\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-142 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-splash-screen\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-143 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-system-ui\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-144 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-tracking-transparency\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-145 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-146 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-147 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-148 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\lottie-react-native\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-149 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\mixpanel-react-native\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-150 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-151 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-152 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-153 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-maps\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-154 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-purchases\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-155 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-156 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-157 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-158 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.scoopt.inpress.app-debug-159 C:\Users\<USER>\code\projects\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources
