import { posthog } from "@/services/posthog"

type FeatureFlag =
  | "levels"
  | "match_moods"
  | "rating_sounds_and_haptics"
  | "new_levels_screen"
  | "leaderboard"
  | "new_read_rated_design"
  | "more_news_layouts"
  | "gists"

const storyFeatureFlags = {
  levels: true,
  match_moods: false,
  rating_sounds_and_haptics: true,
  new_levels_screen: true,
  leaderboard: true,
  new_read_rated_design: true,
  more_news_layouts: true,
  gists: true,
}

export function isFeatureEnabled(flag: FeatureFlag): boolean {
  const storyMode = process.env.EXPO_PUBLIC_RENDER_STORIES === "true"
  if (storyMode) {
    return storyFeatureFlags[flag]
  }

  return !!posthog.isFeatureEnabled(flag)
}

export function useFeatureFlag(flag: FeatureFlag): boolean {
  return isFeatureEnabled(flag)
}

export function setStoryFeatureFlag(flag: FeatureFlag, value: boolean): void {
  if (process.env.EXPO_PUBLIC_RENDER_STORIES === "true") {
    storyFeatureFlags[flag] = value
  } else {
    throw new Error(
      `setStoryFeatureFlag can only be used in story mode, current mode: ${process.env.EXPO_PUBLIC_RENDER_STORIES}`,
    )
  }
}
