import { Level } from "@/types/levels"

export const calculateProgress = ({
  points,
  currentLevel,
  nextLevel,
}: {
  points: number
  currentLevel: Level | undefined
  nextLevel: Level | undefined
}): number => {
  if (!nextLevel) return 1

  const startingPoints = currentLevel?.pointsRequired || 0
  const endingPoints = nextLevel.pointsRequired

  return (points - startingPoints) / (endingPoints - startingPoints)
}
