import {
  defaultNewUser,
  FinishedNewProfile,
  SignUp_,
  SignUpProps_,
} from "@/components/signInOrUp/SignUp"
import { washingtonDC } from "@/utils/allowableAreas"

export const SIGN_UP_PROPS: SignUpProps_<FinishedNewProfile> = {
  defaultProfile: {
    ...defaultNewUser,
    phoneNumber: "1234567890",
    email: "<EMAIL>",
  },
  isLoading: false,
  handleRegister: async (profile) => console.log(profile),
  signUpError: undefined,
  onRetry: () => {},
  handleTrackEvent: async (event) => console.log(event),
  sendPhoneVerification: async () => {},
  verifyPhoneNumber: async () => ({ success: true }),
  getUserPosition: async () => washingtonDC,
}

export default function SignUpStory() {
  return <SignUp_ {...SIGN_UP_PROPS} />
}
