import Color from "color"

import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native"
import { Image, ImageProps } from "expo-image"
import { Level } from "@/types/levels"
import { IMAGES } from "@/constants/Images"
import { useMemo } from "react"
import { DARK_GREY, LIGHT_GREY } from "@/constants/Colors"
import { calculateProgress } from "@/utils/levels"
import { fontStyles } from "@/styles"
import { router } from "expo-router"

export type StatSection = {
  icon: ImageProps["source"]
  value: string
  label: string
}

type Props = {
  points: number
  currentLevel: Level | undefined
  nextLevel: Level | undefined
  articlesRead: number
  articlesRated: number
  streakDays: number
}

const InScoreSection = ({
  points,
  currentLevel,
  nextLevel,
  articlesRead,
  articlesRated,
  streakDays,
}: Props) => {
  const pointsRemainingStr = nextLevel
    ? (nextLevel.pointsRequired - points).toLocaleString()
    : ""

  const progress = calculateProgress({ points, currentLevel, nextLevel })

  const statSections: StatSection[] = useMemo(() => {
    return [
      {
        icon: IMAGES.Fire,
        value: `${streakDays}d`,
        label: "streak",
      },
      {
        icon: IMAGES.Brain,
        value: points.toLocaleString(),
        label: "knowledge points",
      },
      {
        icon: IMAGES.Eyeglasses,
        value: articlesRead.toLocaleString(),
        label: "articles read",
      },
      {
        icon: IMAGES.OpenBook,
        value: articlesRated.toLocaleString(),
        label: "articles rated",
      },
    ]
  }, [streakDays, articlesRated, articlesRead, points])

  const renderStat = ({ item }: { item: StatSection }) => {
    return (
      <View style={styles.statContainer}>
        <Image source={item.icon} style={styles.itemIcon} />
        <View>
          <Text style={styles.itemValue}>{item.value}</Text>
          <Text style={styles.itemLabel}>{item.label}</Text>
        </View>
      </View>
    )
  }

  const badgeSource = currentLevel
    ? { uri: currentLevel.badgeUrl }
    : IMAGES.UnachievedNewbieBadge
  const levelNameText = currentLevel ? currentLevel.name : "No Level Yet!"
  const levelColor = currentLevel ? currentLevel.color : LIGHT_GREY

  return (
    <View>
      <View style={styles.statsContainer}>
        {points === 0 ? (
          <>
            <View style={{ flex: 1, flexDirection: "column", gap: 12 }}>
              <Text style={[styles.currentLevelName, { color: "#FFFFFF" }]}>
                {"No InScore... yet"}
              </Text>
              <Text style={styles.remainPoints}>
                {"Earn points to level up!"}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => router.navigate("/news/feed")}
              style={styles.getStartedButton}
            >
              <Text style={styles.getStartedButtonLabel}>{"Get Started"}</Text>
            </TouchableOpacity>
          </>
        ) : (
          <View style={styles.currentLevelContainer}>
            <Image source={badgeSource} style={styles.badgeIcon} />
            <View style={{ flex: 1 }}>
              <Text style={[styles.currentLevelName, { color: levelColor }]}>
                {levelNameText}
              </Text>
              <View style={styles.remainScoreContainer}>
                <Text style={styles.remainPoints}>
                  {nextLevel
                    ? `${pointsRemainingStr} points until ${nextLevel?.name}`
                    : "Congrats on being the G.O.A.T.!"}
                </Text>
                {nextLevel && (
                  <Text style={[styles.totalPoints, { color: levelColor }]}>
                    {`${points}`}
                    <Text
                      style={{ color: "white" }}
                    >{`/${nextLevel.pointsRequired}`}</Text>
                  </Text>
                )}
              </View>
              <View
                style={[
                  styles.progressBarView,
                  { backgroundColor: Color(levelColor).alpha(0.85).string() },
                ]}
              >
                <View
                  style={[
                    styles.progressBar,
                    {
                      backgroundColor: Color(levelColor)
                        .darken(1)
                        .alpha(0.25)
                        .string(),
                      borderColor: Color(levelColor)
                        .darken(0.85)
                        .alpha(0.35)
                        .string(),
                      width: progress ? `${progress * 100}%` : 0,
                    },
                  ]}
                />
              </View>
            </View>
          </View>
        )}
      </View>
      <FlatList
        data={statSections}
        renderItem={renderStat}
        contentContainerStyle={{ gap: 8 }}
        numColumns={2}
        columnWrapperStyle={{ gap: 8 }}
        style={{ marginVertical: 8, gap: 8 }}
        scrollEnabled={false}
      />
    </View>
  )
}

export default InScoreSection

const styles = StyleSheet.create({
  getStartedButtonLabel: {
    color: DARK_GREY,
    fontSize: 12,
    fontFamily: "InterTight-Regular",
  },
  getStartedButton: {
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 100,
  },
  itemLabel: {
    color: "#D8DADC",
    fontFamily: "InterTight-Regular",
    fontSize: 12,
  },
  itemValue: {
    color: "white",
    fontFamily: "InterTight-Regular",
    fontSize: 16,
  },
  itemIcon: {
    width: 24,
    height: 24,
  },
  statContainer: {
    backgroundColor: DARK_GREY,
    padding: 16,
    flex: 1,
    borderRadius: 10,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  currentLevelContainer: {
    flexDirection: "row",
    gap: 16,
    alignItems: "center",
  },
  badgeIcon: {
    width: 40,
    height: 46,
  },
  progressBar: {
    position: "absolute",
    top: 0,
    bottom: 0,
    borderRadius: 100,
    borderWidth: 2,
  },
  progressBarView: {
    width: "100%",
    height: 6,
    borderRadius: 100,
  },
  totalPoints: {
    fontFamily: "InterTight-Regular",
    fontSize: 12,
  },
  remainPoints: {
    color: "#FFFFFF",
    fontFamily: "InterTight-Regular",
    fontSize: 12,
  },
  remainScoreContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
    marginTop: 8,
  },
  currentLevelName: {
    fontSize: 20,
    marginBottom: -6,
    ...fontStyles.editorial,
  },
  title: {
    fontSize: 24,
    marginBottom: 8,
    ...fontStyles.editorial,
  },
  statsContainer: {
    backgroundColor: DARK_GREY,
    borderRadius: 10,
    padding: 16,
    flexDirection: "row",
    alignItems: "flex-end",
    gap: 16,
  },
})
