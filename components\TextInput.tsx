import {
  TextInputProps as DefaultTextInputProps,
  View,
  TextInput as DefaultTextInput,
  StyleSheet,
  ViewStyle,
} from "react-native"
import { Text } from "./Themed"
import { ComponentProps, forwardRef } from "react"
import React from "react"

interface TextInputProps extends DefaultTextInputProps {
  label?: string
  subtext?: string
  value?: string
  inputStyle?: ComponentProps<typeof DefaultTextInput>["style"]
  rightComponent?: React.ReactNode
  onChangeText?: (text: string) => void
  inputContainerStyle?: ViewStyle | ViewStyle[]
}

export const TextInput = forwardRef<DefaultTextInput, TextInputProps>(
  (
    {
      label,
      subtext,
      value,
      onChangeText,
      rightComponent,
      inputStyle,
      inputContainerStyle,
      ...props
    },
    ref,
  ) => {
    return (
      <>
        {label && (
          <Text
            style={{
              marginBottom: 6,
              fontSize: 16,
            }}
          >
            {label}
          </Text>
        )}
        <View style={[styles.inputContainer, inputContainerStyle]}>
          <DefaultTextInput
            ref={ref}
            value={value}
            onChangeText={onChangeText}
            {...props}
            style={[styles.input, inputStyle]}
            selectionColor="gray"
            cursorColor="gray"
          />
          {rightComponent}
        </View>
        {subtext && <Text style={styles.subtext}>{subtext}</Text>}
      </>
    )
  },
)

const inputPadding = 16
export const styles = StyleSheet.create({
  input: {
    paddingTop: inputPadding,
    padding: inputPadding,
    flex: 1,
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: "row",
    backgroundColor: "white",
    marginBottom: 10,
    borderRadius: 10,
    borderColor: "#d8dadc",
    borderWidth: 1,
  },
  subtext: {
    marginBottom: 16,
    color: "rgba(0, 0, 0, 0.7)",
  },
})
