import { useState } from "react"
import { Text, StyleSheet, View, TouchableOpacity } from "react-native"
import { Icon } from "react-native-paper"
import ReportArticleModal from "./ReportArticleModal"

export default function Footer({
  isRead,
  isRated,
  articleId,
}: {
  isRead: boolean
  isRated: boolean
  articleId: number
}) {
  const [menuVisible, setMenuVisible] = useState(false)
  const [isReporting, setIsReporting] = useState(false)

  const footerIcons = [
    { source: "eye", text: "Read", isActive: isRead },
    { source: "star", text: "Rated", isActive: isRated },
  ]

  const menuOptions = [
    {
      text: "Report article",
      icon: "information-outline",
      onPress: () => setIsReporting(true),
    },
  ]

  return (
    <>
      <View style={styles.footer}>
        <View style={styles.footerLeft}>
          {footerIcons.map((icon, index) => (
            <View
              key={index}
              style={[
                styles.iconTextContainer,
                { display: icon.isActive ? "flex" : "none" },
              ]}
            >
              <Icon source={icon.source} size={20} />
              <Text style={styles.footerText}>{icon.text}</Text>
            </View>
          ))}
        </View>
        <TouchableOpacity
          onPress={() => setMenuVisible(!menuVisible)}
          style={{ position: "relative" }}
        >
          <Icon source="dots-horizontal" size={20} />
          {menuVisible && (
            <View
              style={[
                styles.menu,
                {
                  top: -35 * menuOptions.length,
                  height: 32 * menuOptions.length,
                },
              ]}
            >
              {menuOptions.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={option.onPress}
                  style={styles.menuItem}
                >
                  <Text style={styles.menuText}>{option.text}</Text>
                  <Icon source={option.icon} size={16} />
                </TouchableOpacity>
              ))}
            </View>
          )}
        </TouchableOpacity>
      </View>

      <ReportArticleModal
        articleId={articleId}
        modalVisible={isReporting}
        onClose={() => setIsReporting(false)}
      />
    </>
  )
}

export const FOOTER_HEIGHT = 20

const styles = StyleSheet.create({
  footer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    height: FOOTER_HEIGHT,
  },
  footerLeft: {
    flexDirection: "row",
    gap: 12,
  },
  iconTextContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  footerText: {
    fontSize: 12,
    fontFamily: "InterTight-Regular",
    fontWeight: "400",
    marginLeft: 4,
  },
  menu: {
    position: "absolute",
    right: 0,
    backgroundColor: "white",
    borderRadius: 5,
    width: 157,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    height: 32,
    justifyContent: "space-between",
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  menuText: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: "center",
  },
})
