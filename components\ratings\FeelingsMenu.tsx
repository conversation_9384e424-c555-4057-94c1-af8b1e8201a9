import { useEffect, useState } from "react"
import { FlatList, TouchableOpacity, StyleSheet, View } from "react-native"
import { Text } from "../Themed"

type Feeling = { emoji: string; label: string }

const feelings: Feeling[] = [
  {
    emoji: "🙂",
    label: "Happy",
  },
  {
    emoji: "🤩",
    label: "Excited",
  },
  {
    emoji: "😊",
    label: "Inspired",
  },
  {
    emoji: "🤔",
    label: "Intrigued",
  },
  {
    emoji: "🤯",
    label: "Mind Blown",
  },
  {
    emoji: "☹️",
    label: "Sad",
  },
  {
    emoji: "😥",
    label: "Anxious",
  },
  {
    emoji: "😒",
    label: "Annoyed",
  },
  {
    emoji: "😡",
    label: "Angry",
  },
  {
    emoji: "😌",
    label: "Relieved",
  },
]

interface FeelingsMenuProps {
  initialFeelings: string[]
  onChange: (selectedFeelings: string[]) => void
}

export const FeelingsMenu = ({
  initialFeelings,
  onChange,
}: FeelingsMenuProps) => {
  const [selectedFeelings, setSelectedFeelings] = useState(initialFeelings)

  useEffect(() => {
    onChange(selectedFeelings)
  }, [selectedFeelings])

  const toggleFeelingSelection = (feeling: string) => {
    const updatedSelection = [...selectedFeelings]
    if (updatedSelection.includes(feeling)) {
      updatedSelection.splice(updatedSelection.indexOf(feeling), 1)
    } else {
      updatedSelection.push(feeling)
    }
    setSelectedFeelings(updatedSelection)
  }

  const renderItem = ({ item }: { item: Feeling }) => {
    const { emoji, label } = item
    return (
      <TouchableOpacity
        style={[
          styles.item,
          selectedFeelings.includes(label) && styles.selectedItem,
        ]}
        onPress={() => toggleFeelingSelection(label)}
      >
        <Text style={styles.emoji}>{emoji}</Text>
        <Text style={styles.label}>{label}</Text>
      </TouchableOpacity>
    )
  }

  return (
    <FlatList
      style={styles.container}
      data={feelings}
      renderItem={renderItem}
      keyExtractor={(_, index) => index.toString()}
      numColumns={5}
      scrollEnabled={false}
      columnWrapperStyle={styles.columnWrapper}
      ItemSeparatorComponent={() => <View style={{ height: 7 }} />}
    />
  )
}

const styles = StyleSheet.create({
  container: {
    height: 180,
    minHeight: 180,
  },
  item: {
    paddingTop: 4,
    paddingBottom: 7,
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: "rgba(128, 128, 128, 0.2)",
    borderRadius: 8,
    width: 60,
    alignItems: "center",
  },
  selectedItem: {
    backgroundColor: "rgba(128, 128, 128, 0.2)",
    borderRadius: 8,
  },
  emoji: {
    fontSize: 38,
  },
  label: {
    fontSize: 10,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
})
