import { Screen } from "@/components/Themed"
import SharedInterestsPanel from "@/components/leads/SharedInterestsPanel"
import { LEADS } from "./leads"
import { Topic } from "@/types/social"
import { useSocialContext } from "@/context/SocialContext"
import _ from "lodash"

export default function Story() {
  const { rootTopicNameToId } = useSocialContext()
  const topicsWithEveryRoot = _.map(rootTopicNameToId, (id, name) => ({
    name: _.startCase(name),
    rootId: id,
  }))

  const mixedTopics: Topic[] = [
    { name: "Lifestyle", rootId: rootTopicNameToId["lifestyle"] },
    { name: "Hair", rootId: rootTopicNameToId["lifestyle"] },
    { name: "Barbershops", rootId: rootTopicNameToId["lifestyle"] },
    { name: "Space Exploration", rootId: rootTopicNameToId["technology"] },
    { name: "Mars", rootId: rootTopicNameToId["technology"] },
    { name: "Startups", rootId: rootTopicNameToId["business"] },
  ]
  const topicWithSpecialName = {
    name: "Food & Cooking",
    rootId: rootTopicNameToId["foodCooking"],
  }
  const topicWithoutRoot = { ...LEADS[0].topics[0], rootId: null }
  const topicWithoutGroup = { ...LEADS[0].topics[0], rootId: 999999 }
  return (
    <Screen style={{ gap: 25 }}>
      <SharedInterestsPanel topics={topicsWithEveryRoot} />
      <SharedInterestsPanel topics={mixedTopics} />
      <SharedInterestsPanel topics={LEADS[0].topics} />
      <SharedInterestsPanel topics={[topicWithSpecialName]} />
      <SharedInterestsPanel topics={[topicWithoutRoot]} />
      <SharedInterestsPanel topics={[topicWithoutGroup]} />
    </Screen>
  )
}
