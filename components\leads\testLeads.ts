import { Lead } from "@/apiQueries/apiQueries"
import { ConnectionMode } from "../signInOrUp/ConnectionModeStep"

const datesLead: Lead = {
  id: 1,
  score: 0.71,
  topics: [
    "Dad jokes",
    "Craft beer",
    "Disc golf",
    "Retro gaming",
    "Podcasts",
    "Food trucks",
    "Terrible puns",
    "Outdoor adventures",
  ],
  user: {
    id: 1,
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    age: 35,
    latitude: 38.92,
    longitude: -77.0369,
    occupation: "Program Manager",
    gender: "male",
    connectionMode: ConnectionMode.Dates,
    datesModeIsActivated: true,
    friendsModeIsActivated: false,
    images: [
      {
        id: 1,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male2.jpg",
      },
      {
        id: 2,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male1.jpg",
      },
      {
        id: 3,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male3.jpg",
      },
      {
        id: 4,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male4.jpg",
      },
    ],
    biography:
      "Adventure-seeker with a knack for burning toast and making people laugh. I'm like a Swiss Army knife, but instead of tools, I'm full of useless trivia and dad jokes.",
    scoopResponses: [
      {
        position: 1,
        promptId: 1,
        prompt: "My ideal summer day is…",
        text: "a beach BBQ where I inevitably get overconfident about my ability to grill",
      },
      {
        position: 2,
        promptId: 2,
        prompt: "My irrational fear is…",
        text: "that my plants are judging my life choices when I talk to them",
      },
      {
        position: 3,
        promptId: 3,
        prompt: "This is guaranteed to make me laugh…",
        text: "videos of people walking into glass doors (I'm not proud of it, but it gets me every time)",
      },
    ],
    preferences: {
      genders: ["female", "male", "nonbinary"],
      minAge: 18,
      maxAge: 99,
      minSimilarity: 0,
      maxSimilarity: 1,
    },
    settings: {
      autoUpdateLocation: true,
      matchMoodsOptOut: false,
      ratingSoundsAndHaptics: true,
      nonessentialNotificationsDisabled: false,
    },
    level: {
      id: 1,
      name: "Explorer",
      place: 3,
      color: "#FFD700",
      pointsRequired: 20,
      description: "You're just getting started on your journey.",
      levelUpTitle: "",
      levelUpSubtitle: "",
      badgeUrl:
        "https://inpress-media.s3.amazonaws.com/levels/newbie-badge.png",
      grayscaleBadgeUrl:
        "https://inpress-media.s3.amazonaws.com/levels/newbie-badge-grayscale.png",
      iconUrl: "https://inpress-media.s3.amazonaws.com/levels/newbie-icon.png",
      shareableUrl:
        "https://inpress-media.s3.amazonaws.com/levels/newbie-celebration.png",
    },
    points: 20,
    isNewsOnly: false,
    isGenerated: false,
    isArchived: false,
    createdAt: "2021-08-01T00:00:00.000Z",
  },
}

const friendsLead: Lead = {
  id: 1,
  score: 0.77,
  topics: [
    "Taco Tuesday",
    "Hogwarts",
    "Yoga fails",
    "Karaoke disasters",
    "Netflix binges",
    "Plant parenthood",
    "Wine o'clock",
    "Thrift store treasures",
  ],
  user: {
    id: 1,
    firstName: "Jessica",
    lastName: "Jones",
    age: 25,
    latitude: 38.91,
    longitude: -77.0369,
    occupation: "Program Manager",
    gender: "female",
    connectionMode: ConnectionMode.Friends,
    datesModeIsActivated: true,
    friendsModeIsActivated: false,
    images: [
      {
        id: 1,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-female4.jpg",
      },
      {
        id: 2,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-female1.jpg",
      },
      {
        id: 3,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-female2.jpg",
      },
      {
        id: 4,
        url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-female3.jpg",
      },
    ],
    biography:
      "Professional nap taker, amateur comedian, and full-time dreamer. I'm fluent in sarcasm and can turn any situation into a dance party.",
    scoopResponses: [
      {
        position: 1,
        promptId: 1,
        prompt: "My TED Talk would be about...",
        text: "The Art of Procrastination: How to Perfect the Last-Minute Panic",
      },
      {
        position: 2,
        promptId: 2,
        prompt: "Most of the time, I'm just trying to...",
        text: "figure out if it's too early for pizza",
      },
      {
        position: 3,
        promptId: 3,
        prompt: "My partner hates it when I...",
        text: "use movie quotes as responses in serious conversations",
      },
    ],
    preferences: {
      genders: ["female", "male", "nonbinary"],
      minAge: 18,
      maxAge: 99,
      minSimilarity: 0,
      maxSimilarity: 1,
    },
    settings: datesLead.user.settings,
    level: datesLead.user.level,
    points: datesLead.user.points,
    isNewsOnly: false,
    isGenerated: false,
    isArchived: false,
    createdAt: "2021-08-01T00:00:00.000Z",
  },
}

export { datesLead, friendsLead }
