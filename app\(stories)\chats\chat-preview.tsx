import { View } from "react-native"
import { USER } from "../(account)/account"
import {
  ChatPreview_,
  ChatPreviewProps_,
  Message,
} from "@/components/ChatPreview"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"

const OTHER_USER = {
  ...USER,
  id: USER.id + 1,
  images: [
    {
      id: 0,
      path: "images/profile2.jpg",
      url: "https://inpress-media-staging.s3.us-east-1.amazonaws.com/fake-male2.jpg",
    },
  ],
}

const messageSentBySelf = {
  user: { ...USER, id: USER.id.toString() },
  text: "Hello",
} as unknown as Message

const messageSentByOther = {
  user: { ...OTHER_USER, id: OTHER_USER.id.toString() },
  text: "Hi",
} as unknown as Message

const longMessage = {
  user: { ...OTHER_USER, id: OTHER_USER.id.toString() },
  text: "This is a long message that should be truncated",
} as unknown as Message

const defaultProps: ChatPreviewProps_ = {
  user: USER,
  otherUser: OTHER_USER,
  score: 0.3,
  connectionMode: ConnectionMode.Dates,
  lastMessage: null,
}

export default function Story() {
  return (
    <View style={{ flex: 1 }}>
      <ChatPreview_ {...defaultProps} lastMessage={messageSentBySelf} />
      <ChatPreview_ {...defaultProps} lastMessage={messageSentByOther} />
      <ChatPreview_ {...defaultProps} lastMessage={null} />
      <ChatPreview_ {...defaultProps} lastMessage={longMessage} />
      <ChatPreview_ {...defaultProps} score={0} lastMessage={null} />
    </View>
  )
}

export { defaultProps }
