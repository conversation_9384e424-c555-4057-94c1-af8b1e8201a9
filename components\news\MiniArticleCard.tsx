import React from "react"
import { StyleSheet, Text, View } from "react-native"

import { Image } from "expo-image"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"

import { DARK_GREY, WHITE } from "@/constants/Colors"
import { ReadRatedCover } from "../news/ReadRatedCover"
import { Card } from "react-native-paper"
import { openArticle } from "../ArticlePage"
import { trackEvent } from "@/utils/tracking"
import { Article, NewsEventType } from "@/types/news"

type Props = {
  article: Article
}

const MiniArticleCard: React.FC<Props> = ({ article }) => {
  const handlePress = () => {
    trackEvent(NewsEventType.FinishReadingArticleTapped, {
      data: { article_id: article.id },
    })
    openArticle(article)
  }

  return (
    <Card onPress={handlePress} style={styles.container}>
      <Card.Content style={styles.content}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: article.imageUrl }}
            style={styles.articleImage}
          />
          <ReadRatedCover article={article} />
        </View>
        <View style={styles.contentContainer}>
          <View style={styles.sourceContainer}>
            <Image
              source={{ uri: article.faviconUrl }}
              style={styles.favicon}
            />
            <Text style={styles.sourceText} numberOfLines={1}>
              {article.source}
            </Text>
          </View>
          <Text style={styles.titleText} numberOfLines={3}>
            {article.title}
          </Text>
        </View>
      </Card.Content>
    </Card>
  )
}

export default MiniArticleCard

const CONTENT_HEIGHT = 211
const MARGIN_VERTICAL = 2
export const HEIGHT = CONTENT_HEIGHT + MARGIN_VERTICAL * 2

const styles = StyleSheet.create({
  container: {
    marginVertical: MARGIN_VERTICAL,
  },
  content: {
    width: wp(44.8),
    height: CONTENT_HEIGHT,
    backgroundColor: WHITE,
    borderRadius: 10,
    flexDirection: "column",
    gap: 12,
  },
  imageContainer: {
    position: "relative",
  },
  articleImage: {
    height: 96,
    borderRadius: 8,
  },
  contentContainer: {
    flexDirection: "column",
    gap: 8,
  },
  sourceContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  favicon: {
    width: wp(4.3),
    height: wp(4.3),
    borderRadius: 2.46,
    borderWidth: 0.5,
    borderColor: "#E6E6E6",
  },
  sourceText: {
    fontSize: 12,
    color: "#000000",
    fontFamily: "Inter-Regular",
    width: "85%",
    letterSpacing: -0.15,
  },
  titleText: {
    color: DARK_GREY,
    fontSize: 14,
    fontFamily: "InterTight-Medium",
  },
})
