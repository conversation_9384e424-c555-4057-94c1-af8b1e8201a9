import { LevelsProvider } from "@/context/LevelContext"
import { Stack } from "expo-router"
import { Streak } from "@/types/levels"
import { LEVELS_PROVIDER_PROPS } from "./constants/levels"
import { NewsProvider } from "@/context/NewsContext"
import { NEWS_PROVIDER_PROPS } from "./constants/news"
import { TestingProvider } from "@/context/TestingContext"

export const STREAKS: Streak[] = [
  {
    title: "2 Day Streak",
    subtitle:
      "Congrats on staying informed for 2 days in a row, you student of the world you.",
    shareableUrl:
      "https://inpress-media.s3.us-east-1.amazonaws.com/levels/streaks/2-day-inpress-streak.png",
    type: "2_day",
  },
  {
    title: "7 Day Streak",
    subtitle:
      "Congrats on staying informed for a week, you student of the world you.",
    shareableUrl:
      "https://inpress-media.s3.us-east-1.amazonaws.com/levels/streaks/7-day-inpress-streak.png",
    type: "7_day",
  },
  {
    title: "4 Week Streak",
    subtitle:
      "Congrats on staying informed for a month, you student of the world you.",
    shareableUrl:
      "https://inpress-media.s3.us-east-1.amazonaws.com/levels/streaks/4-week-inpress-streak.png",
    type: "4_week",
  },
  {
    title: "180 Day Streak",
    subtitle:
      "Congrats on staying informed for 6 months, you student of the world you.",
    shareableUrl:
      "https://inpress-media.s3.us-east-1.amazonaws.com/levels/streaks/180-day-inpress-streak.png",
    type: "180_day",
  },
]

export default function StoryLayout() {
  return (
    <TestingProvider>
      <LevelsProvider {...LEVELS_PROVIDER_PROPS}>
        <NewsProvider {...NEWS_PROVIDER_PROPS}>
          <Stack screenOptions={{ headerShown: true }}>
            <Stack.Screen
              name="index"
              options={{ headerShown: true, headerTitle: "Stories" }}
            />
          </Stack>
        </NewsProvider>
      </LevelsProvider>
    </TestingProvider>
  )
}
