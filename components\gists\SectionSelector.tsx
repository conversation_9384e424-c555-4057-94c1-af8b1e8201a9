import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import {
  Animated,
  FlatList,
  LayoutAnimation,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  UIManager,
  View,
  Image,
} from "react-native"

import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"

import { Modal } from "../Modal"
import { IMAGES } from "@/constants/Images"
import { Octicons } from "@expo/vector-icons"
import { Section, SectionGroup, sectionGroups } from "../news/constants"
import { DARK_GREY, VERY_LIGHT_GREY } from "@/constants/Colors"
import { LargeText } from "../StyledText"
import { trackEvent } from "@/utils/tracking"
import { NewsEventType } from "@/types/news"
import { useNewsContext } from "@/context/NewsContext"
import { isUnratedArticle } from "@/utils/processArticles"

if (Platform.OS === "android") {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true)
  }
}

const SectionSelector = () => {
  const {
    articles,
    activeSection,
    setSelectedSection,
    shuffleModeOn,
    setShuffleModeOn,
  } = useNewsContext()

  const flatListRef = useRef<FlatList>(null)

  const findSectionGroup = useCallback(
    (sectionName: string) =>
      sectionGroups.find((group) =>
        group.sections.some((section) => section.name === sectionName),
      )!,
    [],
  )

  const [modalIsVisible, setModalIsVisible] = useState(false)
  const [expandedGroup, setExpandedGroup] = useState<SectionGroup | null>(
    findSectionGroup(activeSection.name),
  )

  useEffect(() => {
    const newGroup = findSectionGroup(activeSection.name)
    if (newGroup) {
      setExpandedGroup(newGroup)
    }
  }, [activeSection])

  const activeGroupIndex = useMemo(
    () => sectionGroups.findIndex(({ name }) => name === expandedGroup?.name),
    [sectionGroups, expandedGroup],
  )

  useEffect(() => {
    if (modalIsVisible && activeGroupIndex !== -1 && flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({
          index: activeGroupIndex,
          animated: true,
          viewPosition: 0.2, // Center the item
        })
      }, 100)
    }
  }, [modalIsVisible, activeGroupIndex])

  const handleScrollToIndexFailed = (info: {
    index: number
    highestMeasuredFrameIndex: number
    averageItemLength: number
  }) => {
    // Try again with a larger timeout
    setTimeout(() => {
      if (flatListRef.current) {
        flatListRef.current.scrollToOffset({
          offset: info.averageItemLength * info.index,
          animated: true,
        })
      }
    }, 200)
  }

  const SectionItem = ({
    item: group,
    selectedGroup,
    selectedSection,
  }: {
    item: SectionGroup
    selectedGroup: SectionGroup | null
    selectedSection: Section
  }) => {
    const isExpanded = group.name === selectedGroup?.name
    const rotateAnim = useRef(new Animated.Value(0)).current
    const handleToggle = () => {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
      setExpandedGroup(isExpanded ? null : group)
    }

    useEffect(() => {
      Animated.timing(rotateAnim, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }).start()
    }, [isExpanded, rotateAnim])

    const rotateInterpolate = rotateAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ["0deg", "90deg"],
    })

    const animatedStyle = {
      transform: [{ rotate: rotateInterpolate }],
    }

    return (
      <View style={styles.containerItem}>
        <TouchableOpacity
          onPress={handleToggle}
          style={styles.headerContainer}
          activeOpacity={0.8}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <LargeText style={styles.titleText}>{group.title}</LargeText>
          <Animated.Image
            source={IMAGES.ArrowRight}
            style={[styles.arrowIcon, animatedStyle]}
          />
        </TouchableOpacity>
        {isExpanded ? (
          <View style={styles.expandedContainer}>
            {group.sections.map((section) => {
              const isSelected = section.name === selectedSection.name
              const hasArticles = articles.some(
                (article) =>
                  article.frontpageSection === section.name &&
                  isUnratedArticle(article),
              )

              return (
                <TouchableOpacity
                  style={[
                    styles.sectionItemContainer,
                    isSelected && {
                      backgroundColor: VERY_LIGHT_GREY,
                    },
                    !hasArticles && { opacity: 0.5 },
                  ]}
                  key={section.name}
                  activeOpacity={0.6}
                  disabled={!hasArticles}
                  onPress={() => {
                    trackEvent(NewsEventType.GistSectionSelected, {
                      data: { section: section.name },
                    })
                    setSelectedSection(section)
                    setModalIsVisible(false)
                  }}
                >
                  <LargeText style={styles.titleText}>
                    {section.title}
                  </LargeText>
                  {isSelected && (
                    <Octicons name="check-circle-fill" size={wp(4.2)} />
                  )}
                </TouchableOpacity>
              )
            })}
          </View>
        ) : null}
      </View>
    )
  }

  return (
    <>
      <View style={styles.container}>
        <TouchableOpacity
          onPress={() => {
            setModalIsVisible(true)
            trackEvent(NewsEventType.SectionSelectorOpened)
          }}
          style={[styles.buttonContainer, { flex: 1 }]}
          activeOpacity={0.6}
        >
          <Text style={styles.buttonLabel}>{activeSection.title}</Text>
          <Image source={IMAGES.ArrowRight} style={styles.arrowIcon} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            setShuffleModeOn(!shuffleModeOn)
            trackEvent(NewsEventType.ShuffleToggled, {
              data: { isActive: !shuffleModeOn },
            })
          }}
          style={styles.buttonContainer}
          activeOpacity={0.6}
        >
          <Image
            source={shuffleModeOn ? IMAGES.ShuffleOn : IMAGES.ShuffleOff}
            style={styles.shuffleIcon}
          />
        </TouchableOpacity>
      </View>
      <Modal
        visible={modalIsVisible}
        modalProps={{
          animationType: "fade",
          onRequestClose: () => setModalIsVisible(false),
        }}
        containerStyle={styles.modalContainer}
        onPressOverlay={() => setModalIsVisible(false)}
      >
        <FlatList
          ref={flatListRef}
          data={sectionGroups}
          renderItem={({ item }) => (
            <SectionItem
              item={item}
              selectedGroup={expandedGroup}
              selectedSection={activeSection}
            />
          )}
          contentContainerStyle={{ gap: hp(1.5) }}
          style={{ width: "100%" }}
          showsVerticalScrollIndicator={false}
          onScrollToIndexFailed={handleScrollToIndexFailed}
          keyExtractor={(item) => item.name}
        />
      </Modal>
    </>
  )
}

export default SectionSelector

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  buttonLabel: {
    color: "#C18C5D",
    fontSize: 20,
    fontFamily: "Inter-Medium",
    includeFontPadding: false,
    letterSpacing: -1.5,
  },
  titleText: {
    color: DARK_GREY,
    flex: 1,
  },
  arrowIcon: {
    width: wp(4),
    height: wp(4),
    resizeMode: "contain",
  },
  shuffleIcon: {
    height: wp(6.4),
    width: wp(6.4),
    resizeMode: "contain",
  },
  modalContainer: {
    width: wp(57),
    alignItems: "stretch",
    height: hp(50),
  },
  containerItem: {
    flexDirection: "column",
    gap: wp(3.2),
    borderBottomWidth: hp(0.1),
    paddingBottom: wp(3.2),
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  expandedContainer: {
    flexDirection: "column",
    gap: wp(1.5),
  },
  sectionItemContainer: {
    padding: wp(2.24),
    borderRadius: wp(2.7),
    flexDirection: "row",
    alignItems: "center",
  },
})
