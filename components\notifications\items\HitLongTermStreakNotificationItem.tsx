import { GenericNotificationItem } from "./GenericNotificationItem"
import { HitLongTermStreakNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"
import { useLevels } from "@/context/LevelContext"

export function HitLongTermStreakNotificationItem({
  item,
}: {
  item: HitLongTermStreakNotification
}) {
  const { loading, streaks } = useLevels()

  if (loading || !streaks) {
    return null
  }

  const streak = streaks.find((s) => s.type === item.streakType)

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          Wow! Your <BoldText>{streak!.title}</BoldText> streak just earned you{" "}
          {item.pointsEarned} points. You're on fire!
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
