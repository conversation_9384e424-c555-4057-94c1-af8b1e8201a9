import { StyleSheet, Text, View } from "react-native"
import { router } from "expo-router"
import { activateModePath, leadsPath, levelsPath } from "@/utils/deepLinks"
import { Button } from "../Button"
import SparklesIcon from "../icons/SparklesIcon"
import Colors from "@/constants/Colors"
import { ACTIVATE_SOCIAL_PROPS } from "@/screens/leads/LeadsTeaser"
import { pushWithParams } from "@/utils/localParams"
import { ActivateParams } from "@/app/(signInOrUp)/activate-mode"
import { fontStyles } from "@/styles"

export const NewsFeedFooter = ({ isNewsOnly }: { isNewsOnly: boolean }) => {
  const footerText = isNewsOnly
    ? "That's all folks! \nCheck your InScore, or InPress Social:"
    : "That's all for today! Done reading? Check your Leads!"

  return (
    <View style={styles.bottomTextContainer}>
      <Text style={styles.bottomText}>{footerText}</Text>

      {isNewsOnly ? (
        <View style={styles.buttonsContainer}>
          <Button
            text={"InPress Social"}
            iconComponent={ACTIVATE_SOCIAL_PROPS.buttonIcon}
            isTextOnLeft
            onPress={() =>
              pushWithParams<ActivateParams>({
                pathname: activateModePath,
                params: { isActivatingSocialStr: "true" },
              })
            }
          />
          <Button
            text="Check InScore Progress"
            style={styles.outlineButtonStyle}
            textStyle={styles.outlineButtonTextStyle}
            onPress={() => router.navigate(levelsPath)}
          />
        </View>
      ) : (
        <Button
          text="Check your Leads feed"
          onPress={() => router.push(leadsPath)}
          isTextOnLeft
          iconComponent={<SparklesIcon />}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  bottomTextContainer: {
    marginTop: 35,
    marginHorizontal: 16,
    marginBottom: 90,
  },
  bottomText: {
    fontSize: 40,
    fontWeight: "400",
    textAlign: "left",
    marginBottom: 30,
    ...fontStyles.editorial,
  },
  buttonsContainer: {
    gap: 10,
  },
  outlineButtonStyle: {
    backgroundColor: "transparent",
    borderWidth: 1,
  },
  outlineButtonTextStyle: {
    color: Colors.light.text,
  },
})
